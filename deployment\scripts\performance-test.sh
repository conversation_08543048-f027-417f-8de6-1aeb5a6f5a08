#!/bin/bash

# Performance Testing Script for Barber App
# This script runs comprehensive performance tests using various tools
# Usage: ./performance-test.sh [--type=<test_type>] [--duration=<seconds>] [--users=<count>]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default configuration
TEST_TYPE="all"
DURATION=300  # 5 minutes
CONCURRENT_USERS=50
BASE_URL="http://localhost"
API_URL="$BASE_URL/api"
RESULTS_DIR="./performance-results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --type=*)
            TEST_TYPE="${1#*=}"
            shift
            ;;
        --duration=*)
            DURATION="${1#*=}"
            shift
            ;;
        --users=*)
            CONCURRENT_USERS="${1#*=}"
            shift
            ;;
        --url=*)
            BASE_URL="${1#*=}"
            API_URL="$BASE_URL/api"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--type=<test_type>] [--duration=<seconds>] [--users=<count>] [--url=<base_url>]"
            echo "Test types: all, load, stress, spike, endurance, api, database"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

# Create results directory
mkdir -p "$RESULTS_DIR"

# Logging function
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$RESULTS_DIR/performance-test-$TIMESTAMP.log"
}

# Check if required tools are installed
check_dependencies() {
    log "INFO" "Checking dependencies..."
    
    local missing_tools=()
    
    if ! command -v ab &> /dev/null; then
        missing_tools+=("apache2-utils (for ab)")
    fi
    
    if ! command -v wrk &> /dev/null; then
        missing_tools+=("wrk")
    fi
    
    if ! command -v curl &> /dev/null; then
        missing_tools+=("curl")
    fi
    
    if ! command -v jq &> /dev/null; then
        missing_tools+=("jq")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log "ERROR" "Missing required tools: ${missing_tools[*]}"
        log "INFO" "Install with: sudo apt-get install apache2-utils wrk curl jq"
        exit 1
    fi
    
    log "SUCCESS" "All dependencies are available"
}

# Function to run Apache Bench tests
run_ab_test() {
    local test_name=$1
    local url=$2
    local requests=$3
    local concurrency=$4
    local output_file="$RESULTS_DIR/ab_${test_name}_$TIMESTAMP.txt"
    
    log "INFO" "Running Apache Bench test: $test_name"
    log "INFO" "URL: $url, Requests: $requests, Concurrency: $concurrency"
    
    ab -n "$requests" -c "$concurrency" -g "$RESULTS_DIR/ab_${test_name}_$TIMESTAMP.tsv" "$url" > "$output_file" 2>&1
    
    if [[ $? -eq 0 ]]; then
        # Parse results
        local rps=$(grep "Requests per second" "$output_file" | awk '{print $4}')
        local mean_time=$(grep "Time per request" "$output_file" | head -n 1 | awk '{print $4}')
        local failed_requests=$(grep "Failed requests" "$output_file" | awk '{print $3}')
        
        log "SUCCESS" "$test_name completed - RPS: $rps, Mean time: ${mean_time}ms, Failed: $failed_requests"
        
        # Generate summary
        echo "=== $test_name Results ===" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
        echo "Requests per second: $rps" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
        echo "Mean response time: ${mean_time}ms" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
        echo "Failed requests: $failed_requests" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
        echo "" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
    else
        log "ERROR" "$test_name failed"
        return 1
    fi
}

# Function to run wrk tests
run_wrk_test() {
    local test_name=$1
    local url=$2
    local duration=$3
    local threads=$4
    local connections=$5
    local output_file="$RESULTS_DIR/wrk_${test_name}_$TIMESTAMP.txt"
    
    log "INFO" "Running wrk test: $test_name"
    log "INFO" "URL: $url, Duration: ${duration}s, Threads: $threads, Connections: $connections"
    
    wrk -t"$threads" -c"$connections" -d"${duration}s" --latency "$url" > "$output_file" 2>&1
    
    if [[ $? -eq 0 ]]; then
        # Parse results
        local rps=$(grep "Requests/sec" "$output_file" | awk '{print $2}')
        local avg_latency=$(grep "Latency" "$output_file" | awk '{print $2}')
        local total_requests=$(grep "requests in" "$output_file" | awk '{print $1}')
        
        log "SUCCESS" "$test_name completed - RPS: $rps, Avg latency: $avg_latency, Total requests: $total_requests"
        
        # Append to summary
        echo "=== $test_name Results (wrk) ===" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
        echo "Requests per second: $rps" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
        echo "Average latency: $avg_latency" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
        echo "Total requests: $total_requests" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
        echo "" >> "$RESULTS_DIR/summary_$TIMESTAMP.txt"
    else
        log "ERROR" "$test_name failed"
        return 1
    fi
}

# Function to test API endpoints
test_api_endpoints() {
    log "INFO" "Testing API endpoints performance..."
    
    local endpoints=(
        "health:GET:$API_URL/health"
        "services:GET:$API_URL/services"
        "barbers:GET:$API_URL/barbers"
        "cities:GET:$API_URL/cities"
    )
    
    for endpoint_info in "${endpoints[@]}"; do
        IFS=':' read -r name method url <<< "$endpoint_info"
        
        log "INFO" "Testing $name endpoint..."
        
        # Quick response time test
        local response_time=$(curl -o /dev/null -s -w "%{time_total}" "$url" 2>/dev/null || echo "999")
        local http_code=$(curl -o /dev/null -s -w "%{http_code}" "$url" 2>/dev/null || echo "000")
        
        if [[ "$http_code" == "200" ]]; then
            if (( $(echo "$response_time < 1.0" | bc -l) )); then
                log "SUCCESS" "$name endpoint: ${response_time}s (HTTP $http_code)"
            else
                log "WARNING" "$name endpoint slow: ${response_time}s (HTTP $http_code)"
            fi
        else
            log "ERROR" "$name endpoint failed: HTTP $http_code"
        fi
        
        # Run load test on endpoint
        run_ab_test "api_$name" "$url" 1000 10
    done
}

# Function to run load tests
run_load_tests() {
    log "INFO" "Running load tests..."
    
    # Test main page
    run_ab_test "homepage" "$BASE_URL/" 5000 "$CONCURRENT_USERS"
    
    # Test API health endpoint
    run_ab_test "api_health" "$API_URL/health" 10000 "$CONCURRENT_USERS"
    
    # Test services endpoint
    run_ab_test "api_services" "$API_URL/services" 5000 "$CONCURRENT_USERS"
    
    # Test barbers endpoint
    run_ab_test "api_barbers" "$API_URL/barbers" 3000 "$CONCURRENT_USERS"
}

# Function to run stress tests
run_stress_tests() {
    log "INFO" "Running stress tests..."
    
    # Gradually increase load
    local stress_levels=(10 25 50 100 200 500)
    
    for level in "${stress_levels[@]}"; do
        log "INFO" "Stress test with $level concurrent users..."
        run_wrk_test "stress_${level}" "$API_URL/health" 60 10 "$level"
        
        # Brief pause between tests
        sleep 10
    done
}

# Function to run spike tests
run_spike_tests() {
    log "INFO" "Running spike tests..."
    
    # Sudden spike in traffic
    run_wrk_test "spike_test" "$API_URL/health" 30 20 1000
    
    # Recovery test
    sleep 30
    run_wrk_test "post_spike" "$API_URL/health" 60 10 50
}

# Function to run endurance tests
run_endurance_tests() {
    log "INFO" "Running endurance tests..."
    
    local endurance_duration=$((DURATION * 2)) # Double the normal duration
    
    run_wrk_test "endurance" "$API_URL/health" "$endurance_duration" 10 "$CONCURRENT_USERS"
}

# Function to test database performance
test_database_performance() {
    log "INFO" "Testing database performance..."
    
    # Test endpoints that hit the database
    local db_endpoints=(
        "barbers_search:$API_URL/barbers?city_id=1"
        "services_list:$API_URL/services"
        "bookings_recent:$API_URL/bookings?limit=10"
    )
    
    for endpoint_info in "${db_endpoints[@]}"; do
        IFS=':' read -r name url <<< "$endpoint_info"
        
        log "INFO" "Testing database endpoint: $name"
        run_ab_test "db_$name" "$url" 2000 20
    done
}

# Function to monitor system resources during tests
monitor_resources() {
    local duration=$1
    local output_file="$RESULTS_DIR/resources_$TIMESTAMP.log"
    
    log "INFO" "Monitoring system resources for ${duration}s..."
    
    {
        echo "Timestamp,CPU%,Memory%,DiskIO,NetworkRX,NetworkTX"
        
        for ((i=1; i<=duration; i++)); do
            local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
            local cpu=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
            local memory=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
            local disk_io=$(iostat -d 1 1 | tail -n +4 | awk '{sum+=$4} END {print sum}')
            local network_rx=$(cat /proc/net/dev | grep eth0 | awk '{print $2}')
            local network_tx=$(cat /proc/net/dev | grep eth0 | awk '{print $10}')
            
            echo "$timestamp,$cpu,$memory,$disk_io,$network_rx,$network_tx"
            sleep 1
        done
    } > "$output_file" &
    
    local monitor_pid=$!
    return $monitor_pid
}

# Function to generate performance report
generate_report() {
    log "INFO" "Generating performance report..."
    
    local report_file="$RESULTS_DIR/performance_report_$TIMESTAMP.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Barber App Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Barber App Performance Test Report</h1>
        <p><strong>Test Date:</strong> $(date)</p>
        <p><strong>Test Duration:</strong> ${DURATION}s</p>
        <p><strong>Concurrent Users:</strong> $CONCURRENT_USERS</p>
        <p><strong>Base URL:</strong> $BASE_URL</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <pre>$(cat "$RESULTS_DIR/summary_$TIMESTAMP.txt" 2>/dev/null || echo "No summary available")</pre>
    </div>
    
    <div class="section">
        <h2>System Information</h2>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            <tr><td>CPU Cores</td><td>$(nproc)</td></tr>
            <tr><td>Total Memory</td><td>$(free -h | grep Mem | awk '{print $2}')</td></tr>
            <tr><td>Available Disk</td><td>$(df -h / | awk 'NR==2 {print $4}')</td></tr>
            <tr><td>OS Version</td><td>$(lsb_release -d | cut -f2)</td></tr>
        </table>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        <ul>
            <li>Monitor response times under load</li>
            <li>Check for memory leaks during endurance tests</li>
            <li>Optimize database queries if response times are high</li>
            <li>Consider caching for frequently accessed endpoints</li>
            <li>Scale horizontally if CPU usage is consistently high</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    log "SUCCESS" "Performance report generated: $report_file"
}

# Main function
main() {
    log "INFO" "Starting performance tests..."
    log "INFO" "Test type: $TEST_TYPE, Duration: ${DURATION}s, Users: $CONCURRENT_USERS"
    
    # Check dependencies
    check_dependencies
    
    # Start resource monitoring
    monitor_resources "$DURATION" &
    local monitor_pid=$!
    
    # Run tests based on type
    case $TEST_TYPE in
        "load")
            run_load_tests
            ;;
        "stress")
            run_stress_tests
            ;;
        "spike")
            run_spike_tests
            ;;
        "endurance")
            run_endurance_tests
            ;;
        "api")
            test_api_endpoints
            ;;
        "database")
            test_database_performance
            ;;
        "all")
            test_api_endpoints
            run_load_tests
            run_stress_tests
            test_database_performance
            ;;
        *)
            log "ERROR" "Unknown test type: $TEST_TYPE"
            exit 1
            ;;
    esac
    
    # Stop resource monitoring
    kill $monitor_pid 2>/dev/null || true
    
    # Generate report
    generate_report
    
    log "SUCCESS" "Performance testing completed!"
    log "INFO" "Results saved in: $RESULTS_DIR"
    
    # Display quick summary
    if [[ -f "$RESULTS_DIR/summary_$TIMESTAMP.txt" ]]; then
        echo -e "\n${BLUE}=== Quick Summary ===${NC}"
        cat "$RESULTS_DIR/summary_$TIMESTAMP.txt"
    fi
}

# Run main function
main "$@"
