# 🚀 دليل النشر الشامل - حلاق على بابك

## 📋 متطلبات النشر

### 🖥️ **متطلبات الخادم**
```bash
# Operating System
Ubuntu 20.04 LTS أو أحدث
CentOS 8+ أو أحدث

# Hardware Requirements
CPU: 4 cores minimum (8 cores recommended)
RAM: 8GB minimum (16GB recommended)
Storage: 100GB SSD minimum (500GB recommended)
Network: 1Gbps connection

# Software Requirements
Docker 20.10+
Docker Compose 2.0+
Nginx 1.18+
SSL Certificate (Let's Encrypt)
```

### ☁️ **الخدمات السحابية المطلوبة**
```bash
# AWS Services
- EC2 Instance (t3.large minimum)
- RDS MySQL 8.0
- S3 Bucket for file storage
- CloudFront CDN
- Route 53 DNS
- Application Load Balancer

# Firebase Services
- Authentication
- Cloud Messaging (FCM)
- Analytics
- Crashlytics

# Third-party Services
- Stripe Account (Payment)
- Paymob Account (Payment)
- Google Maps API Key
- Twilio Account (SMS)
- SendGrid Account (Email)
```

## 🐳 نشر Backend API

### **1. إعداد Docker**
```dockerfile
# Dockerfile
FROM php:8.1-fpm

# Install dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    nginx

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application
COPY . .

# Install dependencies
RUN composer install --optimize-autoloader --no-dev

# Set permissions
RUN chown -R www-data:www-data /var/www
RUN chmod -R 755 /var/www/storage

EXPOSE 9000
CMD ["php-fpm"]
```

### **2. Docker Compose**
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    container_name: barber-api
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - barber-network

  webserver:
    image: nginx:alpine
    container_name: barber-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx/conf.d/:/etc/nginx/conf.d/
      - ./docker/nginx/ssl/:/etc/nginx/ssl/
    networks:
      - barber-network

  db:
    image: mysql:8.0
    container_name: barber-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: barber_db
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
    volumes:
      - dbdata:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - barber-network

  redis:
    image: redis:alpine
    container_name: barber-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - barber-network

  elasticsearch:
    image: elasticsearch:7.14.0
    container_name: barber-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    networks:
      - barber-network

volumes:
  dbdata:

networks:
  barber-network:
    driver: bridge
```

### **3. متغيرات البيئة**
```env
# .env.production
APP_NAME="Barber at Your Door"
APP_ENV=production
APP_KEY=base64:your-app-key-here
APP_DEBUG=false
APP_URL=https://api.barber-app.com

# Database
DB_CONNECTION=mysql
DB_HOST=db
DB_PORT=3306
DB_DATABASE=barber_db
DB_USERNAME=barber_user
DB_PASSWORD=secure_password_here

# Redis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail
MAIL_MAILER=sendgrid
SENDGRID_API_KEY=your-sendgrid-key

# AWS S3
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=barber-app-storage

# Payment Gateways
STRIPE_KEY=pk_live_your-stripe-key
STRIPE_SECRET=sk_live_your-stripe-secret
PAYMOB_API_KEY=your-paymob-key

# Firebase
FIREBASE_PROJECT_ID=barber-app-prod
FIREBASE_PRIVATE_KEY=your-firebase-key

# Google Maps
GOOGLE_MAPS_API_KEY=your-google-maps-key

# SMS
TWILIO_SID=your-twilio-sid
TWILIO_TOKEN=your-twilio-token
```

## 🌐 نشر Admin Dashboard

### **1. بناء الإنتاج**
```bash
# Build for production
cd admin-dashboard
npm install
npm run build

# Deploy to S3 + CloudFront
aws s3 sync dist/ s3://barber-admin-dashboard
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

### **2. إعداد Nginx**
```nginx
# /etc/nginx/sites-available/admin.barber-app.com
server {
    listen 80;
    listen [::]:80;
    server_name admin.barber-app.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name admin.barber-app.com;

    ssl_certificate /etc/letsencrypt/live/admin.barber-app.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/admin.barber-app.com/privkey.pem;

    root /var/www/admin-dashboard/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📱 نشر تطبيقات Flutter

### **1. تطبيق العملاء**
```bash
# Android Build
cd customer-app
flutter clean
flutter pub get
flutter build appbundle --release

# iOS Build
flutter build ios --release
flutter build ipa

# Upload to Stores
# Google Play Console: Upload app-release.aab
# App Store Connect: Upload Runner.ipa
```

### **2. تطبيق الحلاقين**
```bash
# Android Build
cd barber-app
flutter clean
flutter pub get
flutter build appbundle --release

# iOS Build
flutter build ios --release
flutter build ipa
```

### **3. إعداد Firebase**
```json
// google-services.json (Android)
{
  "project_info": {
    "project_number": "your-project-number",
    "project_id": "barber-app-prod"
  },
  "client": [
    {
      "client_info": {
        "mobilesdk_app_id": "your-app-id",
        "android_client_info": {
          "package_name": "com.barberapp.customer"
        }
      }
    }
  ]
}
```

## 🔧 إعداد CI/CD

### **1. GitHub Actions**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy-backend:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.KEY }}
        script: |
          cd /var/www/barber-backend
          git pull origin main
          docker-compose down
          docker-compose up -d --build
          docker-compose exec app php artisan migrate --force
          docker-compose exec app php artisan config:cache
          docker-compose exec app php artisan route:cache

  deploy-admin:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
    
    - name: Build and Deploy
      run: |
        cd admin-dashboard
        npm install
        npm run build
        aws s3 sync dist/ s3://barber-admin-dashboard
        aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_ID }} --paths "/*"
```

## 📊 مراقبة النظام

### **1. إعداد Monitoring**
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana

  elasticsearch:
    image: elasticsearch:7.14.0
    environment:
      - discovery.type=single-node

  kibana:
    image: kibana:7.14.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200

volumes:
  grafana-data:
```

### **2. إعداد Alerts**
```yaml
# monitoring/alerts.yml
groups:
- name: barber-app-alerts
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    annotations:
      summary: "High error rate detected"
      
  - alert: DatabaseDown
    expr: mysql_up == 0
    for: 1m
    annotations:
      summary: "MySQL database is down"
      
  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
    for: 5m
    annotations:
      summary: "High memory usage detected"
```

## 🔒 الأمان والنسخ الاحتياطي

### **1. إعداد SSL**
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL Certificate
sudo certbot --nginx -d api.barber-app.com
sudo certbot --nginx -d admin.barber-app.com

# Auto-renewal
sudo crontab -e
0 12 * * * /usr/bin/certbot renew --quiet
```

### **2. النسخ الاحتياطي**
```bash
#!/bin/bash
# backup.sh

# Database Backup
docker exec barber-mysql mysqldump -u root -p$MYSQL_ROOT_PASSWORD barber_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Upload to S3
aws s3 cp backup_$(date +%Y%m%d_%H%M%S).sql s3://barber-backups/database/

# Files Backup
tar -czf files_backup_$(date +%Y%m%d_%H%M%S).tar.gz /var/www/storage
aws s3 cp files_backup_$(date +%Y%m%d_%H%M%S).tar.gz s3://barber-backups/files/

# Cleanup old backups (keep last 30 days)
find . -name "backup_*.sql" -mtime +30 -delete
find . -name "files_backup_*.tar.gz" -mtime +30 -delete
```

## 🚀 خطة الإطلاق

### **المرحلة 1: الإطلاق التجريبي (Beta)**
- [ ] نشر النظام على خوادم الاختبار
- [ ] اختبار شامل لجميع المزايا
- [ ] دعوة 100 مستخدم تجريبي
- [ ] جمع التغذية الراجعة وإصلاح الأخطاء

### **المرحلة 2: الإطلاق المحدود**
- [ ] نشر في القاهرة فقط
- [ ] 50 حلاق معتمد
- [ ] حملة تسويقية محدودة
- [ ] مراقبة الأداء والاستقرار

### **المرحلة 3: الإطلاق الكامل**
- [ ] توسع لجميع المحافظات
- [ ] 500+ حلاق معتمد
- [ ] حملة تسويقية شاملة
- [ ] إطلاق مزايا VIP

---

**🎉 النظام جاهز للإطلاق والنجاح في السوق!**
