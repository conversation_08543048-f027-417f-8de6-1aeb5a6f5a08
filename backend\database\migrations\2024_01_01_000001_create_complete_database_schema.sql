-- Complete Database Schema for Barber App
-- Created: 2025-07-17
-- Version: 1.0

-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Users table (main users table)
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `phone_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `fcm_token` text,
  `two_factor_secret` text,
  `two_factor_enabled` tinyint(1) DEFAULT '0',
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  UNIQUE KEY `users_phone_unique` (`phone`),
  KEY `idx_users_email` (`email`),
  KEY `idx_users_phone` (`phone`),
  KEY `idx_users_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User profiles table
CREATE TABLE `user_profiles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `address` text,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `city_id` bigint unsigned DEFAULT NULL,
  `area_id` bigint unsigned DEFAULT NULL,
  `preferences` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_profiles_user_id_unique` (`user_id`),
  KEY `idx_user_profiles_location` (`latitude`,`longitude`),
  CONSTRAINT `user_profiles_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Cities table
CREATE TABLE `cities` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `name_en` varchar(255) DEFAULT NULL,
  `country_code` varchar(3) DEFAULT 'EG',
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_cities_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Areas table
CREATE TABLE `areas` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `city_id` bigint unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_en` varchar(255) DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_areas_city` (`city_id`),
  KEY `idx_areas_active` (`is_active`),
  CONSTRAINT `areas_city_id_foreign` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Barbers table
CREATE TABLE `barbers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `experience_years` int DEFAULT '0',
  `specialties` json DEFAULT NULL,
  `bio` text,
  `rating` decimal(3,2) DEFAULT '0.00',
  `total_reviews` int DEFAULT '0',
  `total_bookings` int DEFAULT '0',
  `is_verified` tinyint(1) DEFAULT '0',
  `is_available` tinyint(1) DEFAULT '1',
  `verification_documents` json DEFAULT NULL,
  `working_hours` json DEFAULT NULL,
  `service_radius` int DEFAULT '10',
  `commission_rate` decimal(5,2) DEFAULT '15.00',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `barbers_user_id_unique` (`user_id`),
  KEY `idx_barbers_verified` (`is_verified`),
  KEY `idx_barbers_available` (`is_available`),
  KEY `idx_barbers_rating` (`rating`),
  CONSTRAINT `barbers_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Barber locations table
CREATE TABLE `barber_locations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `barber_id` bigint unsigned NOT NULL,
  `city_id` bigint unsigned NOT NULL,
  `area_id` bigint unsigned DEFAULT NULL,
  `address` text,
  `latitude` decimal(10,8) NOT NULL,
  `longitude` decimal(11,8) NOT NULL,
  `is_current` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_barber_locations_barber` (`barber_id`),
  KEY `idx_barber_locations_location` (`latitude`,`longitude`),
  KEY `idx_barber_locations_current` (`is_current`),
  CONSTRAINT `barber_locations_barber_id_foreign` FOREIGN KEY (`barber_id`) REFERENCES `barbers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `barber_locations_city_id_foreign` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`),
  CONSTRAINT `barber_locations_area_id_foreign` FOREIGN KEY (`area_id`) REFERENCES `areas` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Services table
CREATE TABLE `services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `base_price` decimal(8,2) NOT NULL,
  `duration_minutes` int NOT NULL DEFAULT '30',
  `category_id` bigint unsigned DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `is_vip_exclusive` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_services_active` (`is_active`),
  KEY `idx_services_category` (`category_id`),
  KEY `idx_services_vip` (`is_vip_exclusive`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Service categories table
CREATE TABLE `service_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `icon` varchar(255) DEFAULT NULL,
  `sort_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_service_categories_active` (`is_active`),
  KEY `idx_service_categories_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Barber services (many-to-many with custom pricing)
CREATE TABLE `barber_services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `barber_id` bigint unsigned NOT NULL,
  `service_id` bigint unsigned NOT NULL,
  `custom_price` decimal(8,2) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `barber_services_barber_service_unique` (`barber_id`,`service_id`),
  KEY `idx_barber_services_available` (`is_available`),
  CONSTRAINT `barber_services_barber_id_foreign` FOREIGN KEY (`barber_id`) REFERENCES `barbers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `barber_services_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bookings table
CREATE TABLE `bookings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `booking_number` varchar(50) NOT NULL,
  `customer_id` bigint unsigned NOT NULL,
  `barber_id` bigint unsigned NOT NULL,
  `scheduled_date` date NOT NULL,
  `scheduled_time` time NOT NULL,
  `status` enum('pending','confirmed','in_progress','completed','cancelled','no_show') DEFAULT 'pending',
  `total_amount` decimal(8,2) NOT NULL,
  `commission_amount` decimal(8,2) NOT NULL,
  `barber_amount` decimal(8,2) NOT NULL,
  `discount_amount` decimal(8,2) DEFAULT '0.00',
  `customer_address` text NOT NULL,
  `customer_latitude` decimal(10,8) DEFAULT NULL,
  `customer_longitude` decimal(11,8) DEFAULT NULL,
  `notes` text,
  `cancellation_reason` text,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `confirmed_at` timestamp NULL DEFAULT NULL,
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `is_instant_booking` tinyint(1) DEFAULT '0',
  `vip_discount_applied` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `bookings_booking_number_unique` (`booking_number`),
  KEY `idx_bookings_customer` (`customer_id`),
  KEY `idx_bookings_barber` (`barber_id`),
  KEY `idx_bookings_date` (`scheduled_date`),
  KEY `idx_bookings_status` (`status`),
  KEY `idx_bookings_created` (`created_at`),
  CONSTRAINT `bookings_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`),
  CONSTRAINT `bookings_barber_id_foreign` FOREIGN KEY (`barber_id`) REFERENCES `barbers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Booking services (services included in each booking)
CREATE TABLE `booking_services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `booking_id` bigint unsigned NOT NULL,
  `service_id` bigint unsigned NOT NULL,
  `quantity` int DEFAULT '1',
  `unit_price` decimal(8,2) NOT NULL,
  `total_price` decimal(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_booking_services_booking` (`booking_id`),
  KEY `idx_booking_services_service` (`service_id`),
  CONSTRAINT `booking_services_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE,
  CONSTRAINT `booking_services_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Payment methods table
CREATE TABLE `payment_methods` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` enum('card','wallet','cash','bank_transfer') NOT NULL,
  `provider` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `settings` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_payment_methods_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Payments table
CREATE TABLE `payments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `booking_id` bigint unsigned NOT NULL,
  `payment_method_id` bigint unsigned NOT NULL,
  `amount` decimal(8,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'EGP',
  `status` enum('pending','processing','completed','failed','refunded','cancelled') DEFAULT 'pending',
  `transaction_id` varchar(255) DEFAULT NULL,
  `gateway_response` json DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_payments_booking` (`booking_id`),
  KEY `idx_payments_status` (`status`),
  KEY `idx_payments_transaction` (`transaction_id`),
  CONSTRAINT `payments_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`),
  CONSTRAINT `payments_payment_method_id_foreign` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Reviews table
CREATE TABLE `reviews` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `booking_id` bigint unsigned NOT NULL,
  `customer_id` bigint unsigned NOT NULL,
  `barber_id` bigint unsigned NOT NULL,
  `rating` tinyint unsigned NOT NULL,
  `comment` text,
  `images` json DEFAULT NULL,
  `is_anonymous` tinyint(1) DEFAULT '0',
  `is_approved` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `reviews_booking_id_unique` (`booking_id`),
  KEY `idx_reviews_customer` (`customer_id`),
  KEY `idx_reviews_barber` (`barber_id`),
  KEY `idx_reviews_rating` (`rating`),
  KEY `idx_reviews_approved` (`is_approved`),
  CONSTRAINT `reviews_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE,
  CONSTRAINT `reviews_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`),
  CONSTRAINT `reviews_barber_id_foreign` FOREIGN KEY (`barber_id`) REFERENCES `barbers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- VIP packages table
CREATE TABLE `vip_packages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `price` decimal(8,2) NOT NULL,
  `duration_days` int NOT NULL,
  `discount_percentage` decimal(5,2) DEFAULT '0.00',
  `priority_booking` tinyint(1) DEFAULT '0',
  `free_cancellation` tinyint(1) DEFAULT '0',
  `features` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `sort_order` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_vip_packages_active` (`is_active`),
  KEY `idx_vip_packages_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User subscriptions table
CREATE TABLE `user_subscriptions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `vip_package_id` bigint unsigned NOT NULL,
  `starts_at` timestamp NOT NULL,
  `expires_at` timestamp NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `auto_renew` tinyint(1) DEFAULT '0',
  `renewed_at` timestamp NULL DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `cancellation_reason` text,
  `prorated_amount` decimal(8,2) DEFAULT NULL,
  `upgraded_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_subscriptions_user` (`user_id`),
  KEY `idx_user_subscriptions_active` (`is_active`),
  KEY `idx_user_subscriptions_expires` (`expires_at`),
  CONSTRAINT `user_subscriptions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_subscriptions_vip_package_id_foreign` FOREIGN KEY (`vip_package_id`) REFERENCES `vip_packages` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Loyalty tiers table
CREATE TABLE `loyalty_tiers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `level` int NOT NULL,
  `points_required` int NOT NULL,
  `points_multiplier` decimal(3,2) DEFAULT '1.00',
  `discount_percentage` decimal(5,2) DEFAULT '0.00',
  `upgrade_bonus` int DEFAULT '0',
  `benefits` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `loyalty_tiers_level_unique` (`level`),
  KEY `idx_loyalty_tiers_points` (`points_required`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User loyalty table
CREATE TABLE `user_loyalty` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `tier_id` bigint unsigned NOT NULL,
  `total_points` int DEFAULT '0',
  `available_points` int DEFAULT '0',
  `lifetime_points` int DEFAULT '0',
  `tier_achieved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_loyalty_user_id_unique` (`user_id`),
  KEY `idx_user_loyalty_tier` (`tier_id`),
  KEY `idx_user_loyalty_points` (`total_points`),
  CONSTRAINT `user_loyalty_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_loyalty_tier_id_foreign` FOREIGN KEY (`tier_id`) REFERENCES `loyalty_tiers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Loyalty points table
CREATE TABLE `loyalty_points` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `points` int NOT NULL,
  `source` varchar(255) NOT NULL,
  `metadata` json DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `earned_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_loyalty_points_user` (`user_id`),
  KEY `idx_loyalty_points_source` (`source`),
  KEY `idx_loyalty_points_expires` (`expires_at`),
  CONSTRAINT `loyalty_points_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Loyalty rewards table
CREATE TABLE `loyalty_rewards` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `type` enum('discount_coupon','free_service','wallet_credit','vip_upgrade') NOT NULL,
  `points_required` int NOT NULL,
  `value` decimal(8,2) DEFAULT NULL,
  `minimum_amount` decimal(8,2) DEFAULT NULL,
  `tier_required` int DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `usage_limit` int DEFAULT NULL,
  `used_count` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_loyalty_rewards_active` (`is_active`),
  KEY `idx_loyalty_rewards_points` (`points_required`),
  KEY `idx_loyalty_rewards_tier` (`tier_required`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User loyalty redemptions table
CREATE TABLE `user_loyalty_redemptions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `reward_id` bigint unsigned NOT NULL,
  `points_used` int NOT NULL,
  `status` enum('pending','completed','cancelled') DEFAULT 'pending',
  `redeemed_at` timestamp NOT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_loyalty_redemptions_user` (`user_id`),
  KEY `idx_user_loyalty_redemptions_reward` (`reward_id`),
  KEY `idx_user_loyalty_redemptions_status` (`status`),
  CONSTRAINT `user_loyalty_redemptions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_loyalty_redemptions_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `loyalty_rewards` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- VIP benefits usage table
CREATE TABLE `vip_benefits` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `benefit_type` varchar(255) NOT NULL,
  `benefit_data` json DEFAULT NULL,
  `used_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_vip_benefits_user` (`user_id`),
  KEY `idx_vip_benefits_type` (`benefit_type`),
  CONSTRAINT `vip_benefits_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Live streams table
CREATE TABLE `live_streams` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `booking_id` bigint unsigned NOT NULL,
  `channel_name` varchar(255) NOT NULL,
  `initiated_by` enum('barber','customer') NOT NULL,
  `status` enum('active','ended','failed') DEFAULT 'active',
  `barber_token` text,
  `customer_token` text,
  `recording_id` varchar(255) DEFAULT NULL,
  `recording_url` varchar(255) DEFAULT NULL,
  `recording_processed` tinyint(1) DEFAULT '0',
  `participants` json DEFAULT NULL,
  `settings` json DEFAULT NULL,
  `started_at` timestamp NOT NULL,
  `ended_at` timestamp NULL DEFAULT NULL,
  `duration` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `live_streams_channel_name_unique` (`channel_name`),
  KEY `idx_live_streams_booking` (`booking_id`),
  KEY `idx_live_streams_status` (`status`),
  CONSTRAINT `live_streams_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Notifications table
CREATE TABLE `notifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `title` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `type` varchar(255) NOT NULL,
  `data` json DEFAULT NULL,
  `channels` json DEFAULT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `failed_at` timestamp NULL DEFAULT NULL,
  `failure_reason` text,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_notifications_user` (`user_id`),
  KEY `idx_notifications_type` (`type`),
  KEY `idx_notifications_read` (`read_at`),
  KEY `idx_notifications_sent` (`sent_at`),
  CONSTRAINT `notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Coupons table
CREATE TABLE `coupons` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('percentage','fixed_amount') NOT NULL,
  `value` decimal(8,2) NOT NULL,
  `minimum_amount` decimal(8,2) DEFAULT '0.00',
  `maximum_discount` decimal(8,2) DEFAULT NULL,
  `usage_limit` int DEFAULT NULL,
  `used_count` int DEFAULT '0',
  `user_id` bigint unsigned DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `source` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `coupons_code_unique` (`code`),
  KEY `idx_coupons_active` (`is_active`),
  KEY `idx_coupons_expires` (`expires_at`),
  KEY `idx_coupons_user` (`user_id`),
  CONSTRAINT `coupons_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Coupon usage table
CREATE TABLE `coupon_usage` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `coupon_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `booking_id` bigint unsigned NOT NULL,
  `discount_amount` decimal(8,2) NOT NULL,
  `used_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_coupon_usage_coupon` (`coupon_id`),
  KEY `idx_coupon_usage_user` (`user_id`),
  KEY `idx_coupon_usage_booking` (`booking_id`),
  CONSTRAINT `coupon_usage_coupon_id_foreign` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`) ON DELETE CASCADE,
  CONSTRAINT `coupon_usage_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `coupon_usage_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Analytics table
CREATE TABLE `analytics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `event_type` varchar(255) NOT NULL,
  `event_data` json DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_analytics_user` (`user_id`),
  KEY `idx_analytics_event` (`event_type`),
  KEY `idx_analytics_session` (`session_id`),
  KEY `idx_analytics_created` (`created_at`),
  CONSTRAINT `analytics_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Security logs table
CREATE TABLE `security_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `event` varchar(255) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `details` json DEFAULT NULL,
  `risk_level` enum('low','medium','high') DEFAULT 'low',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_security_logs_user` (`user_id`),
  KEY `idx_security_logs_event` (`event`),
  KEY `idx_security_logs_risk` (`risk_level`),
  KEY `idx_security_logs_created` (`created_at`),
  CONSTRAINT `security_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System settings table
CREATE TABLE `system_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) NOT NULL,
  `value` text,
  `type` enum('string','integer','boolean','json') DEFAULT 'string',
  `description` text,
  `is_public` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `system_settings_key_unique` (`key`),
  KEY `idx_system_settings_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial data
INSERT INTO `cities` (`name`, `name_en`, `latitude`, `longitude`, `is_active`) VALUES
('القاهرة', 'Cairo', 30.0444, 31.2357, 1),
('الجيزة', 'Giza', 30.0131, 31.2089, 1),
('الإسكندرية', 'Alexandria', 31.2001, 29.9187, 1),
('شبرا الخيمة', 'Shubra El Kheima', 30.1287, 31.2441, 1),
('بورسعيد', 'Port Said', 31.2653, 32.3019, 1);

INSERT INTO `service_categories` (`name`, `description`, `sort_order`, `is_active`) VALUES
('قص الشعر', 'خدمات قص وتصفيف الشعر', 1, 1),
('الحلاقة', 'حلاقة الذقن والشارب', 2, 1),
('العناية بالشعر', 'غسيل وترطيب الشعر', 3, 1),
('التجميل', 'خدمات التجميل والعناية', 4, 1);

INSERT INTO `services` (`name`, `description`, `base_price`, `duration_minutes`, `category_id`, `is_active`) VALUES
('قص شعر عادي', 'قص شعر كلاسيكي', 50.00, 30, 1, 1),
('قص شعر مدرج', 'قص شعر مدرج حديث', 70.00, 45, 1, 1),
('حلاقة ذقن', 'حلاقة وتهذيب الذقن', 30.00, 20, 2, 1),
('غسيل شعر', 'غسيل وتنظيف الشعر', 25.00, 15, 3, 1),
('ماسك للشعر', 'ماسك مغذي للشعر', 40.00, 30, 3, 1);

INSERT INTO `payment_methods` (`name`, `type`, `provider`, `is_active`) VALUES
('فيزا/ماستركارد', 'card', 'stripe', 1),
('فوري', 'wallet', 'paymob', 1),
('كاش', 'cash', NULL, 1),
('تحويل بنكي', 'bank_transfer', NULL, 1);

INSERT INTO `loyalty_tiers` (`name`, `level`, `points_required`, `points_multiplier`, `discount_percentage`, `upgrade_bonus`) VALUES
('برونزي', 1, 0, 1.00, 0.00, 0),
('فضي', 2, 500, 1.25, 5.00, 50),
('ذهبي', 3, 1500, 1.50, 10.00, 100),
('بلاتيني', 4, 3000, 2.00, 15.00, 200);

INSERT INTO `vip_packages` (`name`, `description`, `price`, `duration_days`, `discount_percentage`, `priority_booking`, `free_cancellation`, `features`, `is_active`, `sort_order`) VALUES
('VIP برونز', 'باقة VIP الأساسية', 99.00, 30, 10.00, 1, 0, '["خصم 10%", "أولوية في الحجز"]', 1, 1),
('VIP فضة', 'باقة VIP متوسطة', 199.00, 30, 15.00, 1, 1, '["خصم 15%", "أولوية في الحجز", "إلغاء مجاني"]', 1, 2),
('VIP ذهب', 'باقة VIP متقدمة', 299.00, 30, 20.00, 1, 1, '["خصم 20%", "أولوية في الحجز", "إلغاء مجاني", "حجز فوري"]', 1, 3),
('VIP بلاتين', 'باقة VIP الفاخرة', 499.00, 30, 25.00, 1, 1, '["خصم 25%", "أولوية في الحجز", "إلغاء مجاني", "حجز فوري", "خدمة عملاء VIP"]', 1, 4);

INSERT INTO `system_settings` (`key`, `value`, `type`, `description`, `is_public`) VALUES
('app_name', 'حلاق على بابك', 'string', 'اسم التطبيق', 1),
('app_version', '1.0.0', 'string', 'إصدار التطبيق', 1),
('commission_rate', '15.00', 'string', 'نسبة العمولة الافتراضية', 0),
('booking_cancellation_hours', '2', 'integer', 'عدد الساعات المسموحة للإلغاء', 1),
('max_booking_days_ahead', '30', 'integer', 'أقصى عدد أيام للحجز مقدماً', 1),
('maintenance_mode', 'false', 'boolean', 'وضع الصيانة', 1);

-- Add foreign key for service categories
ALTER TABLE `services` ADD CONSTRAINT `services_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `service_categories` (`id`) ON DELETE SET NULL;

-- Create indexes for better performance
CREATE INDEX `idx_bookings_date_status` ON `bookings` (`scheduled_date`, `status`);
CREATE INDEX `idx_bookings_barber_date` ON `bookings` (`barber_id`, `scheduled_date`);
CREATE INDEX `idx_reviews_barber_rating` ON `reviews` (`barber_id`, `rating`);
CREATE INDEX `idx_payments_created_status` ON `payments` (`created_at`, `status`);
CREATE INDEX `idx_notifications_user_read` ON `notifications` (`user_id`, `read_at`);

-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;
