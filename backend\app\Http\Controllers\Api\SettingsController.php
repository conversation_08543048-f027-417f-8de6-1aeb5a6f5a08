<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\SettingsService;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SettingsController extends Controller
{
    protected $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    /**
     * Get public settings for mobile apps
     */
    public function getPublicSettings(): JsonResponse
    {
        try {
            $settings = Setting::getPublicSettings();
            
            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Public settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get app configuration for mobile apps
     */
    public function getAppConfig(): JsonResponse
    {
        try {
            $config = $this->settingsService->getAppConfig();
            
            return response()->json([
                'success' => true,
                'data' => $config,
                'message' => 'App configuration retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve app configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get feature flags for mobile apps
     */
    public function getFeatureFlags(): JsonResponse
    {
        try {
            $features = $this->settingsService->getFeatureFlags();
            
            return response()->json([
                'success' => true,
                'data' => $features,
                'message' => 'Feature flags retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve feature flags',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get booking configuration
     */
    public function getBookingConfig(): JsonResponse
    {
        try {
            $config = $this->settingsService->getBookingConfig();
            
            return response()->json([
                'success' => true,
                'data' => $config,
                'message' => 'Booking configuration retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve booking configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get VIP configuration
     */
    public function getVipConfig(): JsonResponse
    {
        try {
            $config = $this->settingsService->getVipConfig();
            
            return response()->json([
                'success' => true,
                'data' => $config,
                'message' => 'VIP configuration retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve VIP configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment configuration
     */
    public function getPaymentConfig(): JsonResponse
    {
        try {
            $config = $this->settingsService->getPaymentConfig();
            
            return response()->json([
                'success' => true,
                'data' => $config,
                'message' => 'Payment configuration retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check maintenance mode
     */
    public function checkMaintenance(): JsonResponse
    {
        try {
            $isMaintenanceMode = $this->settingsService->isMaintenanceMode();
            $message = Setting::get('maintenance_message', 'التطبيق تحت الصيانة، سنعود قريباً');
            
            return response()->json([
                'success' => true,
                'data' => [
                    'maintenance_mode' => $isMaintenanceMode,
                    'message' => $message
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check maintenance status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all settings for admin (requires authentication)
     */
    public function getAllSettings(): JsonResponse
    {
        try {
            // Add authentication middleware check here
            $settings = $this->settingsService->getAdminSettings();
            
            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'All settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update settings (requires admin authentication)
     */
    public function updateSettings(Request $request): JsonResponse
    {
        try {
            // Add authentication and authorization middleware check here
            
            $settings = $request->input('settings', []);
            
            if (empty($settings)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No settings provided'
                ], 400);
            }

            $updated = $this->settingsService->updateSettings($settings);
            
            return response()->json([
                'success' => true,
                'data' => $updated,
                'message' => 'Settings updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle feature (requires admin authentication)
     */
    public function toggleFeature(Request $request, $feature): JsonResponse
    {
        try {
            // Add authentication and authorization middleware check here
            
            $enabled = $request->input('enabled');
            $result = $this->settingsService->toggleFeature($feature, $enabled);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'feature' => $feature,
                    'enabled' => $result
                ],
                'message' => "Feature {$feature} " . ($result ? 'enabled' : 'disabled') . ' successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle feature',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export settings (requires admin authentication)
     */
    public function exportSettings(): JsonResponse
    {
        try {
            // Add authentication and authorization middleware check here
            
            $settings = $this->settingsService->exportSettings();
            
            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Settings exported successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear settings cache (requires admin authentication)
     */
    public function clearCache(): JsonResponse
    {
        try {
            // Add authentication and authorization middleware check here
            
            Setting::clearCache();
            
            return response()->json([
                'success' => true,
                'message' => 'Settings cache cleared successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
