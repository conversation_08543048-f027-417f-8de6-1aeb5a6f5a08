<?php

namespace App\Services;

use App\Models\User;
use App\Models\Barber;
use App\Models\Booking;
use App\Models\Service;
use App\Models\Review;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class AIRecommendationService
{
    /**
     * Get recommended barbers for a user based on AI analysis
     */
    public function getRecommendedBarbers(User $user, array $preferences = [])
    {
        $cacheKey = "recommended_barbers_{$user->id}_" . md5(serialize($preferences));
        
        return Cache::remember($cacheKey, now()->addHours(2), function () use ($user, $preferences) {
            $recommendations = [];
            
            // Get user's booking history
            $userBookings = $this->getUserBookingHistory($user);
            
            // Get user's location preferences
            $locationPrefs = $this->getUserLocationPreferences($user);
            
            // Get user's service preferences
            $servicePrefs = $this->getUserServicePreferences($user);
            
            // Get available barbers
            $availableBarbers = $this->getAvailableBarbers($preferences);
            
            foreach ($availableBarbers as $barber) {
                $score = $this->calculateBarberScore($barber, $user, [
                    'booking_history' => $userBookings,
                    'location_prefs' => $locationPrefs,
                    'service_prefs' => $servicePrefs,
                    'preferences' => $preferences
                ]);
                
                $recommendations[] = [
                    'barber' => $barber,
                    'score' => $score,
                    'reasons' => $this->getRecommendationReasons($barber, $user, $score)
                ];
            }
            
            // Sort by score and return top recommendations
            usort($recommendations, function ($a, $b) {
                return $b['score'] <=> $a['score'];
            });
            
            return array_slice($recommendations, 0, 10);
        });
    }

    /**
     * Calculate barber recommendation score using AI algorithms
     */
    private function calculateBarberScore(Barber $barber, User $user, array $context)
    {
        $score = 0;
        $weights = [
            'rating' => 0.25,
            'distance' => 0.20,
            'availability' => 0.15,
            'service_match' => 0.15,
            'price_preference' => 0.10,
            'past_experience' => 0.10,
            'popularity' => 0.05
        ];

        // Rating score (0-100)
        $ratingScore = ($barber->rating / 5) * 100;
        $score += $ratingScore * $weights['rating'];

        // Distance score (closer = higher score)
        $distanceScore = $this->calculateDistanceScore($barber, $user);
        $score += $distanceScore * $weights['distance'];

        // Availability score
        $availabilityScore = $this->calculateAvailabilityScore($barber);
        $score += $availabilityScore * $weights['availability'];

        // Service match score
        $serviceMatchScore = $this->calculateServiceMatchScore($barber, $context['service_prefs']);
        $score += $serviceMatchScore * $weights['service_match'];

        // Price preference score
        $priceScore = $this->calculatePriceScore($barber, $user);
        $score += $priceScore * $weights['price_preference'];

        // Past experience score
        $pastExperienceScore = $this->calculatePastExperienceScore($barber, $user);
        $score += $pastExperienceScore * $weights['past_experience'];

        // Popularity score
        $popularityScore = $this->calculatePopularityScore($barber);
        $score += $popularityScore * $weights['popularity'];

        return round($score, 2);
    }

    /**
     * Get user's booking history for analysis
     */
    private function getUserBookingHistory(User $user)
    {
        return Booking::where('customer_id', $user->id)
            ->where('status', 'completed')
            ->with(['barber.user', 'services', 'review'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();
    }

    /**
     * Get user's location preferences
     */
    private function getUserLocationPreferences(User $user)
    {
        $recentBookings = $this->getUserBookingHistory($user);
        
        $locations = $recentBookings->map(function ($booking) {
            return [
                'latitude' => $booking->customer_latitude,
                'longitude' => $booking->customer_longitude,
                'area' => $booking->customer_address
            ];
        })->filter();

        return [
            'preferred_areas' => $this->extractPreferredAreas($locations),
            'max_distance' => $this->calculatePreferredMaxDistance($locations),
            'center_point' => $this->calculateCenterPoint($locations)
        ];
    }

    /**
     * Get user's service preferences
     */
    private function getUserServicePreferences(User $user)
    {
        $serviceFrequency = DB::table('booking_services')
            ->join('bookings', 'booking_services.booking_id', '=', 'bookings.id')
            ->join('services', 'booking_services.service_id', '=', 'services.id')
            ->where('bookings.customer_id', $user->id)
            ->where('bookings.status', 'completed')
            ->select('services.id', 'services.name', DB::raw('COUNT(*) as frequency'))
            ->groupBy('services.id', 'services.name')
            ->orderBy('frequency', 'desc')
            ->get();

        return [
            'preferred_services' => $serviceFrequency->pluck('id')->toArray(),
            'service_frequency' => $serviceFrequency->pluck('frequency', 'id')->toArray()
        ];
    }

    /**
     * Calculate distance score (closer = higher score)
     */
    private function calculateDistanceScore(Barber $barber, User $user)
    {
        $userProfile = $user->profile;
        if (!$userProfile || !$userProfile->latitude || !$userProfile->longitude) {
            return 50; // Neutral score if no location data
        }

        $barberLocation = $barber->locations()->where('is_current', true)->first();
        if (!$barberLocation) {
            return 30; // Lower score if barber has no location
        }

        $distance = $this->calculateDistance(
            $userProfile->latitude,
            $userProfile->longitude,
            $barberLocation->latitude,
            $barberLocation->longitude
        );

        // Score decreases with distance (max 20km)
        if ($distance <= 2) return 100;
        if ($distance <= 5) return 80;
        if ($distance <= 10) return 60;
        if ($distance <= 15) return 40;
        if ($distance <= 20) return 20;
        return 10;
    }

    /**
     * Calculate availability score
     */
    private function calculateAvailabilityScore(Barber $barber)
    {
        if (!$barber->is_available) {
            return 0;
        }

        // Check current week availability
        $currentWeekBookings = Booking::where('barber_id', $barber->id)
            ->whereBetween('scheduled_date', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])
            ->count();

        // Assume max 40 bookings per week for full availability
        $availabilityRatio = max(0, (40 - $currentWeekBookings) / 40);
        
        return $availabilityRatio * 100;
    }

    /**
     * Calculate service match score
     */
    private function calculateServiceMatchScore(Barber $barber, array $servicePrefs)
    {
        if (empty($servicePrefs['preferred_services'])) {
            return 50; // Neutral score if no preferences
        }

        $barberServices = $barber->services()->pluck('service_id')->toArray();
        $matchingServices = array_intersect($servicePrefs['preferred_services'], $barberServices);
        
        $matchRatio = count($matchingServices) / count($servicePrefs['preferred_services']);
        
        return $matchRatio * 100;
    }

    /**
     * Calculate price preference score
     */
    private function calculatePriceScore(Barber $barber, User $user)
    {
        // Get user's average spending
        $avgSpending = Booking::where('customer_id', $user->id)
            ->where('status', 'completed')
            ->avg('total_amount');

        if (!$avgSpending) {
            return 50; // Neutral score if no history
        }

        // Get barber's average price
        $barberAvgPrice = $barber->services()
            ->join('services', 'barber_services.service_id', '=', 'services.id')
            ->avg(DB::raw('COALESCE(barber_services.custom_price, services.base_price)'));

        if (!$barberAvgPrice) {
            return 50;
        }

        // Score based on price match (±20% of user's average spending)
        $priceDiff = abs($barberAvgPrice - $avgSpending) / $avgSpending;
        
        if ($priceDiff <= 0.1) return 100; // Within 10%
        if ($priceDiff <= 0.2) return 80;  // Within 20%
        if ($priceDiff <= 0.3) return 60;  // Within 30%
        if ($priceDiff <= 0.5) return 40;  // Within 50%
        return 20;
    }

    /**
     * Calculate past experience score
     */
    private function calculatePastExperienceScore(Barber $barber, User $user)
    {
        $pastBookings = Booking::where('customer_id', $user->id)
            ->where('barber_id', $barber->id)
            ->where('status', 'completed')
            ->with('review')
            ->get();

        if ($pastBookings->isEmpty()) {
            return 50; // Neutral score for new barbers
        }

        $avgRating = $pastBookings->filter(function ($booking) {
            return $booking->review;
        })->avg('review.rating');

        if (!$avgRating) {
            return 70; // Slightly positive if no reviews but had bookings
        }

        return ($avgRating / 5) * 100;
    }

    /**
     * Calculate popularity score
     */
    private function calculatePopularityScore(Barber $barber)
    {
        $recentBookings = Booking::where('barber_id', $barber->id)
            ->where('created_at', '>', now()->subDays(30))
            ->count();

        // Normalize based on max expected bookings (100 per month)
        return min(100, ($recentBookings / 100) * 100);
    }

    /**
     * Get recommendation reasons for user
     */
    private function getRecommendationReasons(Barber $barber, User $user, float $score)
    {
        $reasons = [];

        if ($barber->rating >= 4.5) {
            $reasons[] = "تقييم ممتاز ({$barber->rating}/5)";
        }

        if ($barber->total_bookings > 100) {
            $reasons[] = "خبرة واسعة ({$barber->total_bookings} حجز)";
        }

        $pastBookings = Booking::where('customer_id', $user->id)
            ->where('barber_id', $barber->id)
            ->count();

        if ($pastBookings > 0) {
            $reasons[] = "حجزت معه من قبل ({$pastBookings} مرة)";
        }

        if ($barber->is_verified) {
            $reasons[] = "حلاق معتمد ومؤكد";
        }

        if (empty($reasons)) {
            $reasons[] = "مناسب لتفضيلاتك";
        }

        return $reasons;
    }

    /**
     * Get available barbers based on filters
     */
    private function getAvailableBarbers(array $preferences = [])
    {
        $query = Barber::where('is_available', true)
            ->where('is_verified', true)
            ->with(['user', 'services', 'locations']);

        if (isset($preferences['service_ids'])) {
            $query->whereHas('services', function ($q) use ($preferences) {
                $q->whereIn('service_id', $preferences['service_ids']);
            });
        }

        if (isset($preferences['max_distance']) && isset($preferences['latitude']) && isset($preferences['longitude'])) {
            $query->whereHas('locations', function ($q) use ($preferences) {
                $q->where('is_current', true)
                  ->whereRaw("
                      (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * 
                      cos(radians(longitude) - radians(?)) + sin(radians(?)) * 
                      sin(radians(latitude)))) <= ?
                  ", [
                      $preferences['latitude'],
                      $preferences['longitude'],
                      $preferences['latitude'],
                      $preferences['max_distance']
                  ]);
            });
        }

        return $query->get();
    }

    /**
     * Predict optimal pricing for services
     */
    public function predictOptimalPricing(Service $service, array $factors = [])
    {
        $basePrice = $service->base_price;
        
        // Demand factor
        $demandMultiplier = $this->calculateDemandMultiplier($service);
        
        // Time factor (peak hours, weekends)
        $timeMultiplier = $this->calculateTimeMultiplier($factors['datetime'] ?? now());
        
        // Location factor
        $locationMultiplier = $this->calculateLocationMultiplier($factors['area_id'] ?? null);
        
        // Competition factor
        $competitionMultiplier = $this->calculateCompetitionMultiplier($service, $factors);
        
        $optimalPrice = $basePrice * $demandMultiplier * $timeMultiplier * $locationMultiplier * $competitionMultiplier;
        
        return [
            'base_price' => $basePrice,
            'optimal_price' => round($optimalPrice, 2),
            'factors' => [
                'demand' => $demandMultiplier,
                'time' => $timeMultiplier,
                'location' => $locationMultiplier,
                'competition' => $competitionMultiplier
            ],
            'recommendation' => $this->getPricingRecommendation($basePrice, $optimalPrice)
        ];
    }

    /**
     * Calculate demand multiplier based on recent bookings
     */
    private function calculateDemandMultiplier(Service $service)
    {
        $recentBookings = DB::table('booking_services')
            ->join('bookings', 'booking_services.booking_id', '=', 'bookings.id')
            ->where('booking_services.service_id', $service->id)
            ->where('bookings.created_at', '>', now()->subDays(7))
            ->count();

        $avgBookings = DB::table('booking_services')
            ->join('bookings', 'booking_services.booking_id', '=', 'bookings.id')
            ->where('booking_services.service_id', $service->id)
            ->where('bookings.created_at', '>', now()->subDays(30))
            ->count() / 4; // Weekly average

        if ($avgBookings == 0) return 1.0;

        $demandRatio = $recentBookings / $avgBookings;
        
        // Cap the multiplier between 0.8 and 1.3
        return max(0.8, min(1.3, $demandRatio));
    }

    /**
     * Calculate time-based multiplier
     */
    private function calculateTimeMultiplier(Carbon $datetime)
    {
        $hour = $datetime->hour;
        $isWeekend = $datetime->isWeekend();
        
        $multiplier = 1.0;
        
        // Peak hours (6-9 PM)
        if ($hour >= 18 && $hour <= 21) {
            $multiplier += 0.1;
        }
        
        // Weekend premium
        if ($isWeekend) {
            $multiplier += 0.05;
        }
        
        // Off-peak discount (10 AM - 2 PM on weekdays)
        if (!$isWeekend && $hour >= 10 && $hour <= 14) {
            $multiplier -= 0.05;
        }
        
        return $multiplier;
    }

    /**
     * Calculate location-based multiplier
     */
    private function calculateLocationMultiplier($areaId)
    {
        if (!$areaId) return 1.0;
        
        // This would be based on area demographics and average income
        // For now, return a simple multiplier
        $premiumAreas = [1, 2, 3]; // Example premium area IDs
        
        return in_array($areaId, $premiumAreas) ? 1.1 : 1.0;
    }

    /**
     * Calculate competition-based multiplier
     */
    private function calculateCompetitionMultiplier(Service $service, array $factors)
    {
        // Count competing barbers in the area
        $competitorCount = Barber::whereHas('services', function ($q) use ($service) {
            $q->where('service_id', $service->id);
        })->where('is_available', true)->count();
        
        // More competition = lower prices
        if ($competitorCount > 20) return 0.95;
        if ($competitorCount > 10) return 0.98;
        if ($competitorCount < 5) return 1.05;
        
        return 1.0;
    }

    /**
     * Get pricing recommendation text
     */
    private function getPricingRecommendation(float $basePrice, float $optimalPrice)
    {
        $difference = $optimalPrice - $basePrice;
        $percentChange = ($difference / $basePrice) * 100;
        
        if ($percentChange > 10) {
            return "يُنصح برفع السعر بنسبة " . round($percentChange, 1) . "% بسبب ارتفاع الطلب";
        } elseif ($percentChange < -5) {
            return "يُنصح بخفض السعر بنسبة " . round(abs($percentChange), 1) . "% لزيادة التنافسية";
        } else {
            return "السعر الحالي مناسب للسوق";
        }
    }

    /**
     * Calculate distance between two points
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371; // km
        
        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);
        
        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        
        return $earthRadius * $c;
    }

    /**
     * Extract preferred areas from location history
     */
    private function extractPreferredAreas(array $locations)
    {
        // Implementation would analyze location patterns
        return [];
    }

    /**
     * Calculate preferred max distance
     */
    private function calculatePreferredMaxDistance(array $locations)
    {
        // Implementation would analyze distance patterns
        return 10; // Default 10km
    }

    /**
     * Calculate center point of user's locations
     */
    private function calculateCenterPoint(array $locations)
    {
        if (empty($locations)) return null;
        
        $avgLat = collect($locations)->avg('latitude');
        $avgLon = collect($locations)->avg('longitude');
        
        return ['latitude' => $avgLat, 'longitude' => $avgLon];
    }
}
