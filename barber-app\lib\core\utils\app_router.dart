import 'package:get/get.dart';
import '../../features/splash/views/splash_screen.dart';
import '../../features/onboarding/views/onboarding_screen.dart';
import '../../features/auth/views/login_screen.dart';
import '../../features/auth/views/register_screen.dart';
import '../../features/auth/views/verification_screen.dart';
import '../../features/auth/views/forgot_password_screen.dart';
import '../../features/dashboard/views/dashboard_screen.dart';
import '../../features/dashboard/views/main_navigation_screen.dart';
import '../../features/bookings/views/bookings_screen.dart';
import '../../features/bookings/views/booking_details_screen.dart';
import '../../features/bookings/views/booking_requests_screen.dart';
import '../../features/bookings/views/booking_history_screen.dart';
import '../../features/profile/views/profile_screen.dart';
import '../../features/profile/views/edit_profile_screen.dart';
import '../../features/profile/views/documents_screen.dart';
import '../../features/profile/views/verification_status_screen.dart';
import '../../features/earnings/views/earnings_screen.dart';
import '../../features/earnings/views/earnings_details_screen.dart';
import '../../features/earnings/views/withdrawal_screen.dart';
import '../../features/earnings/views/withdrawal_history_screen.dart';
import '../../features/schedule/views/schedule_screen.dart';
import '../../features/schedule/views/availability_screen.dart';
import '../../features/schedule/views/time_slots_screen.dart';
import '../../features/services/views/services_screen.dart';
import '../../features/services/views/service_details_screen.dart';
import '../../features/services/views/add_service_screen.dart';
import '../../features/analytics/views/analytics_screen.dart';
import '../../features/analytics/views/performance_screen.dart';
import '../../features/analytics/views/customer_feedback_screen.dart';
import '../../features/notifications/views/notifications_screen.dart';
import '../../features/support/views/support_screen.dart';
import '../../features/support/views/chat_screen.dart';
import '../../features/settings/views/settings_screen.dart';

class AppRouter {
  // Route Names
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String verification = '/verification';
  static const String forgotPassword = '/forgot-password';
  static const String dashboard = '/dashboard';
  static const String mainNavigation = '/main-navigation';
  
  // Booking Routes
  static const String bookings = '/bookings';
  static const String bookingDetails = '/booking-details';
  static const String bookingRequests = '/booking-requests';
  static const String bookingHistory = '/booking-history';
  
  // Profile Routes
  static const String profile = '/profile';
  static const String editProfile = '/edit-profile';
  static const String documents = '/documents';
  static const String verificationStatus = '/verification-status';
  
  // Earnings Routes
  static const String earnings = '/earnings';
  static const String earningsDetails = '/earnings-details';
  static const String withdrawal = '/withdrawal';
  static const String withdrawalHistory = '/withdrawal-history';
  
  // Schedule Routes
  static const String schedule = '/schedule';
  static const String availability = '/availability';
  static const String timeSlots = '/time-slots';
  
  // Services Routes
  static const String services = '/services';
  static const String serviceDetails = '/service-details';
  static const String addService = '/add-service';
  
  // Analytics Routes
  static const String analytics = '/analytics';
  static const String performance = '/performance';
  static const String customerFeedback = '/customer-feedback';
  
  // Other Routes
  static const String notifications = '/notifications';
  static const String support = '/support';
  static const String chat = '/chat';
  static const String settings = '/settings';

  // Route List
  static List<GetPage> routes = [
    // Splash & Onboarding
    GetPage(
      name: splash,
      page: () => const SplashScreen(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: onboarding,
      page: () => const OnboardingScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Authentication
    GetPage(
      name: login,
      page: () => const LoginScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: register,
      page: () => const RegisterScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: verification,
      page: () => const VerificationScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: forgotPassword,
      page: () => const ForgotPasswordScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Main Navigation
    GetPage(
      name: dashboard,
      page: () => const DashboardScreen(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: mainNavigation,
      page: () => const MainNavigationScreen(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Bookings
    GetPage(
      name: bookings,
      page: () => const BookingsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: bookingDetails,
      page: () => const BookingDetailsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: bookingRequests,
      page: () => const BookingRequestsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: bookingHistory,
      page: () => const BookingHistoryScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Profile
    GetPage(
      name: profile,
      page: () => const ProfileScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: editProfile,
      page: () => const EditProfileScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: documents,
      page: () => const DocumentsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: verificationStatus,
      page: () => const VerificationStatusScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Earnings
    GetPage(
      name: earnings,
      page: () => const EarningsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: earningsDetails,
      page: () => const EarningsDetailsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: withdrawal,
      page: () => const WithdrawalScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: withdrawalHistory,
      page: () => const WithdrawalHistoryScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Schedule
    GetPage(
      name: schedule,
      page: () => const ScheduleScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: availability,
      page: () => const AvailabilityScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: timeSlots,
      page: () => const TimeSlotsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Services
    GetPage(
      name: services,
      page: () => const ServicesScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: serviceDetails,
      page: () => const ServiceDetailsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: addService,
      page: () => const AddServiceScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Analytics
    GetPage(
      name: analytics,
      page: () => const AnalyticsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: performance,
      page: () => const PerformanceScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: customerFeedback,
      page: () => const CustomerFeedbackScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Other
    GetPage(
      name: notifications,
      page: () => const NotificationsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: support,
      page: () => const SupportScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: chat,
      page: () => const ChatScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: settings,
      page: () => const SettingsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
  ];

  // Navigation Methods
  static void toSplash() => Get.offAllNamed(splash);
  static void toOnboarding() => Get.offAllNamed(onboarding);
  static void toLogin() => Get.offAllNamed(login);
  static void toRegister() => Get.toNamed(register);
  static void toVerification({Map<String, dynamic>? arguments}) => 
      Get.toNamed(verification, arguments: arguments);
  static void toForgotPassword() => Get.toNamed(forgotPassword);
  static void toDashboard() => Get.offAllNamed(mainNavigation);
  
  // Booking Navigation
  static void toBookings() => Get.toNamed(bookings);
  static void toBookingDetails({required String bookingId}) => 
      Get.toNamed(bookingDetails, arguments: {'bookingId': bookingId});
  static void toBookingRequests() => Get.toNamed(bookingRequests);
  static void toBookingHistory() => Get.toNamed(bookingHistory);
  
  // Profile Navigation
  static void toProfile() => Get.toNamed(profile);
  static void toEditProfile() => Get.toNamed(editProfile);
  static void toDocuments() => Get.toNamed(documents);
  static void toVerificationStatus() => Get.toNamed(verificationStatus);
  
  // Earnings Navigation
  static void toEarnings() => Get.toNamed(earnings);
  static void toEarningsDetails({Map<String, dynamic>? arguments}) => 
      Get.toNamed(earningsDetails, arguments: arguments);
  static void toWithdrawal() => Get.toNamed(withdrawal);
  static void toWithdrawalHistory() => Get.toNamed(withdrawalHistory);
  
  // Schedule Navigation
  static void toSchedule() => Get.toNamed(schedule);
  static void toAvailability() => Get.toNamed(availability);
  static void toTimeSlots() => Get.toNamed(timeSlots);
  
  // Services Navigation
  static void toServices() => Get.toNamed(services);
  static void toServiceDetails({required String serviceId}) => 
      Get.toNamed(serviceDetails, arguments: {'serviceId': serviceId});
  static void toAddService() => Get.toNamed(addService);
  
  // Analytics Navigation
  static void toAnalytics() => Get.toNamed(analytics);
  static void toPerformance() => Get.toNamed(performance);
  static void toCustomerFeedback() => Get.toNamed(customerFeedback);
  
  // Other Navigation
  static void toNotifications() => Get.toNamed(notifications);
  static void toSupport() => Get.toNamed(support);
  static void toChat({Map<String, dynamic>? arguments}) => 
      Get.toNamed(chat, arguments: arguments);
  static void toSettings() => Get.toNamed(settings);
  
  // Utility Methods
  static void back() => Get.back();
  static void backUntil(String routeName) => Get.until((route) => route.settings.name == routeName);
  static void offAll(String routeName) => Get.offAllNamed(routeName);
}
