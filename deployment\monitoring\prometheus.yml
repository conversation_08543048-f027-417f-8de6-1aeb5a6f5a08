# Prometheus configuration for Barber App monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'barber-app-production'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: 
        - 'node-exporter:9100'
    scrape_interval: 15s
    metrics_path: /metrics

  # Laravel Application
  - job_name: 'barber-app'
    static_configs:
      - targets: 
        - 'app:9000'
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Nginx Web Server
  - job_name: 'nginx'
    static_configs:
      - targets: 
        - 'webserver:9113'
    scrape_interval: 30s
    metrics_path: /metrics

  # MySQL Database
  - job_name: 'mysql'
    static_configs:
      - targets: 
        - 'mysql-exporter:9104'
    scrape_interval: 30s
    metrics_path: /metrics

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: 
        - 'redis-exporter:9121'
    scrape_interval: 30s
    metrics_path: /metrics

  # Elasticsearch
  - job_name: 'elasticsearch'
    static_configs:
      - targets: 
        - 'elasticsearch-exporter:9114'
    scrape_interval: 30s
    metrics_path: /metrics

  # Docker containers
  - job_name: 'docker'
    static_configs:
      - targets: 
        - 'cadvisor:8080'
    scrape_interval: 30s
    metrics_path: /metrics

  # Application-specific metrics
  - job_name: 'barber-app-custom'
    static_configs:
      - targets: 
        - 'app:8080'
    scrape_interval: 15s
    metrics_path: /api/metrics
    basic_auth:
      username: 'prometheus'
      password: 'secure_password'

  # Queue monitoring (Laravel Horizon)
  - job_name: 'horizon'
    static_configs:
      - targets: 
        - 'horizon:8080'
    scrape_interval: 30s
    metrics_path: /horizon/api/stats

  # Load Balancer
  - job_name: 'load-balancer'
    static_configs:
      - targets: 
        - 'load-balancer:9145'
    scrape_interval: 30s
    metrics_path: /metrics

  # Kubernetes cluster (if using K8s)
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
    - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
      action: keep
      regex: default;kubernetes;https

  # Kubernetes nodes
  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
    - role: node
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - action: labelmap
      regex: __meta_kubernetes_node_label_(.+)

  # Kubernetes pods
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
    - role: pod
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)
    - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
      action: replace
      regex: ([^:]+)(?::\d+)?;(\d+)
      replacement: $1:$2
      target_label: __address__
    - action: labelmap
      regex: __meta_kubernetes_pod_label_(.+)
    - source_labels: [__meta_kubernetes_namespace]
      action: replace
      target_label: kubernetes_namespace
    - source_labels: [__meta_kubernetes_pod_name]
      action: replace
      target_label: kubernetes_pod_name

  # External services monitoring
  - job_name: 'external-services'
    static_configs:
      - targets:
        - 'api.stripe.com:443'
        - 'api.agora.io:443'
        - 'fcm.googleapis.com:443'
    scrape_interval: 60s
    metrics_path: /health
    scheme: https

  # Blackbox exporter for endpoint monitoring
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://api.barber-app.com/health
        - https://admin.barber-app.com/health
        - https://barber-app.com
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

# Remote write configuration (for long-term storage)
remote_write:
  - url: "https://prometheus-remote-write.example.com/api/v1/write"
    basic_auth:
      username: "barber-app"
      password: "secure_remote_password"
    write_relabel_configs:
      - source_labels: [__name__]
        regex: 'go_.*'
        action: drop

# Remote read configuration
remote_read:
  - url: "https://prometheus-remote-read.example.com/api/v1/read"
    basic_auth:
      username: "barber-app"
      password: "secure_remote_password"

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 50GB
    wal-compression: true
