version: '3.8'

services:
  # Laravel Application
  app:
    build:
      context: ../backend
      dockerfile: Dockerfile.dev
    container_name: barber-app-dev
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ../backend:/var/www
      - ./php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - barber-network
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_HOST=db
      - DB_DATABASE=barber_db
      - DB_USERNAME=barber_user
      - DB_PASSWORD=barber_password
      - REDIS_HOST=redis
      - REDIS_PASSWORD=redis_password
    depends_on:
      - db
      - redis

  # Nginx Web Server
  webserver:
    image: nginx:alpine
    container_name: barber-webserver-dev
    restart: unless-stopped
    ports:
      - "8000:80"
      - "8443:443"
    volumes:
      - ../backend:/var/www
      - ./nginx/development.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
    networks:
      - barber-network
    depends_on:
      - app

  # MySQL Database
  db:
    image: mysql:8.0
    container_name: barber-db-dev
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: barber_db
      MYSQL_USER: barber_user
      MYSQL_PASSWORD: barber_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - db_data:/var/lib/mysql
      - ./mysql/my.cnf:/etc/mysql/my.cnf
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - barber-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: barber-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    networks:
      - barber-network

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: barber-elasticsearch-dev
    restart: unless-stopped
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - barber-network

  # Kibana (for Elasticsearch visualization)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: barber-kibana-dev
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    networks:
      - barber-network
    depends_on:
      - elasticsearch

  # Laravel Horizon (Queue Management)
  horizon:
    build:
      context: ../backend
      dockerfile: Dockerfile.dev
    container_name: barber-horizon-dev
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ../backend:/var/www
    networks:
      - barber-network
    environment:
      - APP_ENV=local
      - DB_HOST=db
      - REDIS_HOST=redis
    command: php artisan horizon
    depends_on:
      - app
      - redis

  # Laravel Scheduler
  scheduler:
    build:
      context: ../backend
      dockerfile: Dockerfile.dev
    container_name: barber-scheduler-dev
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ../backend:/var/www
    networks:
      - barber-network
    environment:
      - APP_ENV=local
      - DB_HOST=db
      - REDIS_HOST=redis
    command: php artisan schedule:work
    depends_on:
      - app

  # MailHog (Email Testing)
  mailhog:
    image: mailhog/mailhog
    container_name: barber-mailhog-dev
    restart: unless-stopped
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - barber-network

  # MinIO (S3 Compatible Storage)
  minio:
    image: minio/minio
    container_name: barber-minio-dev
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    networks:
      - barber-network
    command: server /data --console-address ":9001"

  # Prometheus (Monitoring)
  prometheus:
    image: prom/prometheus:latest
    container_name: barber-prometheus-dev
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/alert_rules.yml:/etc/prometheus/alert_rules.yml
      - prometheus_data:/prometheus
    networks:
      - barber-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana (Monitoring Dashboard)
  grafana:
    image: grafana/grafana:latest
    container_name: barber-grafana-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - barber-network
    depends_on:
      - prometheus

  # Node Exporter (System Metrics)
  node-exporter:
    image: prom/node-exporter:latest
    container_name: barber-node-exporter-dev
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    networks:
      - barber-network
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'

  # cAdvisor (Container Metrics)
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: barber-cadvisor-dev
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
    networks:
      - barber-network

  # Redis Exporter (Redis Metrics)
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: barber-redis-exporter-dev
    restart: unless-stopped
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis:6379
      - REDIS_PASSWORD=redis_password
    networks:
      - barber-network
    depends_on:
      - redis

  # MySQL Exporter (Database Metrics)
  mysql-exporter:
    image: prom/mysqld-exporter:latest
    container_name: barber-mysql-exporter-dev
    restart: unless-stopped
    ports:
      - "9104:9104"
    environment:
      - DATA_SOURCE_NAME=barber_user:barber_password@(db:3306)/barber_db
    networks:
      - barber-network
    depends_on:
      - db

  # Nginx Exporter (Web Server Metrics)
  nginx-exporter:
    image: nginx/nginx-prometheus-exporter:latest
    container_name: barber-nginx-exporter-dev
    restart: unless-stopped
    ports:
      - "9113:9113"
    command:
      - '-nginx.scrape-uri=http://webserver:80/nginx_status'
    networks:
      - barber-network
    depends_on:
      - webserver

  # Elasticsearch Exporter
  elasticsearch-exporter:
    image: quay.io/prometheuscommunity/elasticsearch-exporter:latest
    container_name: barber-elasticsearch-exporter-dev
    restart: unless-stopped
    ports:
      - "9114:9114"
    command:
      - '--es.uri=http://elasticsearch:9200'
    networks:
      - barber-network
    depends_on:
      - elasticsearch

  # Jaeger (Distributed Tracing)
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: barber-jaeger-dev
    restart: unless-stopped
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - barber-network

  # Adminer (Database Management)
  adminer:
    image: adminer:latest
    container_name: barber-adminer-dev
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - barber-network
    depends_on:
      - db

  # Redis Commander (Redis Management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: barber-redis-commander-dev
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:redis_password
    networks:
      - barber-network
    depends_on:
      - redis

  # Portainer (Container Management)
  portainer:
    image: portainer/portainer-ce:latest
    container_name: barber-portainer-dev
    restart: unless-stopped
    ports:
      - "9443:9443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    networks:
      - barber-network

  # Watchtower (Auto Updates)
  watchtower:
    image: containrrr/watchtower
    container_name: barber-watchtower-dev
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=3600
      - WATCHTOWER_INCLUDE_STOPPED=true
    networks:
      - barber-network

  # Nginx Proxy Manager (Reverse Proxy)
  nginx-proxy-manager:
    image: 'jc21/nginx-proxy-manager:latest'
    container_name: barber-nginx-proxy-manager-dev
    restart: unless-stopped
    ports:
      - '80:80'
      - '81:81'
      - '443:443'
    volumes:
      - nginx_proxy_data:/data
      - nginx_proxy_letsencrypt:/etc/letsencrypt
    networks:
      - barber-network

  # Logstash (Log Processing)
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: barber-logstash-dev
    restart: unless-stopped
    ports:
      - "5044:5044"
      - "9600:9600"
    volumes:
      - ./monitoring/logstash/config:/usr/share/logstash/config
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
    networks:
      - barber-network
    depends_on:
      - elasticsearch

  # Filebeat (Log Shipping)
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: barber-filebeat-dev
    restart: unless-stopped
    user: root
    volumes:
      - ./monitoring/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ../backend/storage/logs:/var/log/laravel:ro
    networks:
      - barber-network
    depends_on:
      - elasticsearch

networks:
  barber-network:
    driver: bridge

volumes:
  db_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  minio_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  portainer_data:
    driver: local
  nginx_proxy_data:
    driver: local
  nginx_proxy_letsencrypt:
    driver: local
