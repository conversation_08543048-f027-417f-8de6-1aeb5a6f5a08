// Import Vuetify styles
@import 'vuetify/styles';

// Custom variables
:root {
  --primary-color: #1976D2;
  --secondary-color: #424242;
  --success-color: #4CAF50;
  --error-color: #FF5252;
  --warning-color: #FFC107;
  --info-color: #2196F3;
}

// Base styles
* {
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', sans-serif !important;
  direction: rtl;
  text-align: right;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}

// RTL Support
.v-application {
  direction: rtl !important;
}

.v-application--is-rtl {
  direction: rtl !important;
}

// Custom utility classes
.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cursor-pointer {
  cursor: pointer;
}

.border-radius-lg {
  border-radius: 12px !important;
}

.border-radius-xl {
  border-radius: 16px !important;
}

.shadow-sm {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24) !important;
}

.shadow-md {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06) !important;
}

.shadow-lg {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05) !important;
}

// Card customizations
.v-card {
  border-radius: 8px !important;
  
  &.elevation-hover {
    transition: box-shadow 0.3s ease;
    
    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    }
  }
}

// Button customizations
.v-btn {
  text-transform: none !important;
  font-weight: 500 !important;
  letter-spacing: 0 !important;
  
  &.v-btn--size-default {
    min-height: 40px;
  }
}

// Data table customizations
.v-data-table {
  .v-data-table__wrapper {
    border-radius: 8px;
    overflow: hidden;
  }
  
  .v-data-table-header {
    background-color: #f8f9fa;
    
    th {
      font-weight: 600 !important;
      color: #495057 !important;
    }
  }
  
  .v-data-table-rows-no-data {
    text-align: center;
    padding: 40px;
    color: #6c757d;
  }
}

// Form customizations
.v-text-field,
.v-select,
.v-textarea {
  .v-field {
    border-radius: 8px !important;
  }
  
  .v-field--focused {
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
  }
}

// Navigation drawer customizations
.v-navigation-drawer {
  .v-list-item {
    border-radius: 8px !important;
    margin: 2px 8px !important;
    
    &.v-list-item--active {
      background: linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(25, 118, 210, 0.05) 100%) !important;
      
      .v-list-item__prepend .v-icon,
      .v-list-item-title {
        color: var(--primary-color) !important;
      }
    }
  }
}

// App bar customizations
.v-app-bar {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
  
  .v-toolbar-title {
    color: #2c3e50 !important;
  }
}

// Loading states
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

// Status badges
.status-badge {
  &.status-active {
    background-color: #d4edda !important;
    color: #155724 !important;
  }
  
  &.status-inactive {
    background-color: #f8d7da !important;
    color: #721c24 !important;
  }
  
  &.status-pending {
    background-color: #fff3cd !important;
    color: #856404 !important;
  }
  
  &.status-verified {
    background-color: #d1ecf1 !important;
    color: #0c5460 !important;
  }
}

// Responsive utilities
@media (max-width: 600px) {
  .hide-on-mobile {
    display: none !important;
  }
  
  .v-card {
    margin: 8px !important;
  }
  
  .v-container {
    padding: 12px !important;
  }
}

@media (min-width: 601px) {
  .hide-on-desktop {
    display: none !important;
  }
}

// Animation classes
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

// Custom scrollbar
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// Print styles
@media print {
  .no-print {
    display: none !important;
  }
  
  .v-navigation-drawer,
  .v-app-bar,
  .v-footer {
    display: none !important;
  }
  
  .v-main {
    padding: 0 !important;
  }
}
