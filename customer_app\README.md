# تطبيق حلاقي - العملاء 💇‍♂️

تطبيق الأندرويد الخاص بالعملاء لحجز خدمات الحلاقة المنزلية

## 🌟 المميزات

### 🔐 المصادقة والأمان
- تسجيل دخول وإنشاء حساب آمن
- مصادقة ثنائية العامل
- تسجيل دخول بالبصمة
- تسجيل دخول اجتماعي (Google, Apple)

### 🏠 الشاشة الرئيسية
- ترحيب شخصي بالمستخدم
- عرض الخدمات المتاحة
- إجراءات سريعة للحجز
- عرض نقاط الولاء والمستوى

### 📅 نظام الحجز
- حجز خدمات الحلاقة المختلفة
- اختيار الحلاق المفضل
- تحديد الوقت والتاريخ
- تتبع حالة الحجز
- إلغاء وإعادة جدولة الحجوزات

### 🗺️ الخرائط والموقع
- تحديد الموقع الحالي
- البحث عن الحلاقين القريبين
- تتبع موقع الحلاق المباشر
- حفظ العناوين المفضلة

### 🛒 المتجر الإلكتروني
- تصفح منتجات العناية
- إضافة المنتجات للسلة
- عملية شراء آمنة
- تتبع الطلبات

### 💳 نظام الدفع
- دفع آمن بالبطاقات الائتمانية
- محفظة رقمية
- كوبونات الخصم
- نقاط الولاء

### ⭐ التقييم والمراجعات
- تقييم الحلاقين والخدمات
- كتابة مراجعات مفصلة
- عرض تقييمات العملاء الآخرين

### 🔔 الإشعارات
- إشعارات فورية للحجوزات
- تذكيرات المواعيد
- عروض وخصومات حصرية
- تحديثات حالة الطلبات

### 👤 الملف الشخصي
- إدارة البيانات الشخصية
- عرض تاريخ الحجوزات
- إدارة طرق الدفع
- إعدادات الخصوصية

## 🛠️ التقنيات المستخدمة

### Frontend
- **Flutter 3.16+** - إطار العمل الرئيسي
- **Dart 3.0+** - لغة البرمجة
- **Provider** - إدارة الحالة
- **Go Router** - التنقل بين الشاشات

### UI/UX
- **Material Design 3** - نظام التصميم
- **Google Fonts** - الخطوط العربية
- **Lottie** - الرسوم المتحركة
- **Shimmer** - تأثيرات التحميل

### الخدمات الخارجية
- **Firebase Auth** - المصادقة
- **Firebase Messaging** - الإشعارات الفورية
- **Google Maps** - الخرائط والموقع
- **Stripe** - معالجة المدفوعات

### التخزين
- **Hive** - قاعدة بيانات محلية
- **Shared Preferences** - إعدادات التطبيق
- **Secure Storage** - تخزين البيانات الحساسة

## 📱 متطلبات النظام

- **Android 5.0** (API level 21) أو أحدث
- **RAM**: 2 جيجابايت أو أكثر
- **التخزين**: 100 ميجابايت مساحة فارغة
- **الإنترنت**: اتصال مستقر بالإنترنت

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
```bash
# تثبيت Flutter
flutter --version

# تثبيت Android Studio
# تثبيت VS Code (اختياري)
```

### خطوات التثبيت
```bash
# 1. استنساخ المشروع
git clone https://github.com/your-repo/barber-customer-app.git
cd barber-customer-app

# 2. تثبيت التبعيات
flutter pub get

# 3. تشغيل التطبيق
flutter run
```

### إعداد Firebase
1. إنشاء مشروع Firebase جديد
2. إضافة تطبيق Android
3. تحميل ملف `google-services.json`
4. وضعه في `android/app/`

### إعداد Google Maps
1. الحصول على API Key من Google Cloud Console
2. إضافته في `android/app/src/main/AndroidManifest.xml`

## 📁 هيكل المشروع

```
lib/
├── core/                   # الملفات الأساسية
│   ├── constants/         # الثوابت والألوان
│   ├── services/          # الخدمات المشتركة
│   └── utils/             # الأدوات المساعدة
├── models/                # نماذج البيانات
├── providers/             # مزودي الحالة
├── screens/               # شاشات التطبيق
│   ├── auth/             # شاشات المصادقة
│   ├── home/             # الشاشة الرئيسية
│   ├── booking/          # شاشات الحجز
│   ├── profile/          # الملف الشخصي
│   └── shop/             # المتجر
├── widgets/               # الويدجت المخصصة
└── main.dart             # نقطة البداية
```

## 🎨 التصميم والألوان

### الألوان الأساسية
- **الأساسي**: `#2E7D32` (أخضر)
- **الثانوي**: `#FF9800` (برتقالي)
- **المميز**: `#03DAC6` (تركوازي)

### الخطوط
- **Cairo** - للنصوص العربية
- **Roboto** - للنصوص الإنجليزية

## 🔧 الإعدادات

### إعدادات التطبيق
```dart
// في lib/core/constants/app_config.dart
class AppConfig {
  static const String baseUrl = 'https://api.barber.app';
  static const String apiVersion = 'v1';
  static const bool enableLogging = true;
}
```

### إعدادات Firebase
```json
// في android/app/google-services.json
{
  "project_info": {
    "project_id": "your-project-id"
  }
}
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
flutter test

# اختبارات التكامل
flutter test integration_test/
```

### أنواع الاختبارات
- **Unit Tests** - اختبار الوظائف المفردة
- **Widget Tests** - اختبار الواجهات
- **Integration Tests** - اختبار التدفق الكامل

## 📦 البناء والنشر

### بناء APK للتطوير
```bash
flutter build apk --debug
```

### بناء APK للإنتاج
```bash
flutter build apk --release
```

### بناء App Bundle
```bash
flutter build appbundle --release
```

## 🔒 الأمان

### حماية البيانات
- تشفير البيانات الحساسة
- استخدام HTTPS لجميع الطلبات
- التحقق من صحة المدخلات

### الصلاحيات
- طلب الصلاحيات عند الحاجة فقط
- شرح سبب الحاجة للصلاحية
- إمكانية العمل بدون صلاحيات اختيارية

## 🐛 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في Firebase**: تأكد من إعداد `google-services.json`
2. **خطأ في الخرائط**: تأكد من API Key صحيح
3. **مشاكل البناء**: تشغيل `flutter clean && flutter pub get`

### السجلات
```bash
# عرض السجلات
flutter logs

# سجلات Android
adb logcat
```

## 🤝 المساهمة

### خطوات المساهمة
1. Fork المشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

### معايير الكود
- اتباع Dart Style Guide
- كتابة تعليقات واضحة
- إضافة اختبارات للميزات الجديدة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://barber.app
- **التليجرام**: @BarberSupport

## 🙏 شكر وتقدير

- فريق Flutter للإطار الرائع
- مجتمع المطورين العرب
- جميع المساهمين في المشروع

---

**تم تطوير التطبيق بـ ❤️ للمجتمع العربي**
