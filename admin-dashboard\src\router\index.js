import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Lazy load components
const Dashboard = () => import('@/views/Dashboard.vue')
const Login = () => import('@/views/auth/Login.vue')
const Users = () => import('@/views/users/Users.vue')
const UserDetails = () => import('@/views/users/UserDetails.vue')
const Barbers = () => import('@/views/barbers/Barbers.vue')
const BarberDetails = () => import('@/views/barbers/BarberDetails.vue')
const BarberVerification = () => import('@/views/barbers/BarberVerification.vue')
const Bookings = () => import('@/views/bookings/Bookings.vue')
const BookingDetails = () => import('@/views/bookings/BookingDetails.vue')
const Finance = () => import('@/views/finance/Finance.vue')
const Withdrawals = () => import('@/views/finance/Withdrawals.vue')
const Store = () => import('@/views/store/Store.vue')
const Products = () => import('@/views/store/Products.vue')
const Categories = () => import('@/views/store/Categories.vue')
const Analytics = () => import('@/views/analytics/Analytics.vue')
const DynamicSettings = () => import('@/views/settings/DynamicSettings.vue')
const GeneralSettings = () => import('@/views/settings/GeneralSettings.vue')
const Profile = () => import('@/views/auth/Profile.vue')
const NotFound = () => import('@/views/errors/NotFound.vue')

const routes = [
  // Auth routes
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { 
      requiresAuth: false,
      title: 'تسجيل الدخول'
    }
  },

  // Main dashboard routes
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: { 
      requiresAuth: true,
      title: 'لوحة المعلومات'
    }
  },

  // User management
  {
    path: '/users',
    name: 'Users',
    component: Users,
    meta: { 
      requiresAuth: true,
      permission: 'users.view',
      title: 'إدارة المستخدمين'
    }
  },
  {
    path: '/users/:id',
    name: 'UserDetails',
    component: UserDetails,
    meta: { 
      requiresAuth: true,
      permission: 'users.view',
      title: 'تفاصيل المستخدم'
    }
  },

  // Barber management
  {
    path: '/barbers',
    name: 'Barbers',
    component: Barbers,
    meta: { 
      requiresAuth: true,
      permission: 'barbers.view',
      title: 'إدارة الحلاقين'
    }
  },
  {
    path: '/barbers/:id',
    name: 'BarberDetails',
    component: BarberDetails,
    meta: { 
      requiresAuth: true,
      permission: 'barbers.view',
      title: 'تفاصيل الحلاق'
    }
  },
  {
    path: '/barbers/verification',
    name: 'BarberVerification',
    component: BarberVerification,
    meta: { 
      requiresAuth: true,
      permission: 'barbers.verify',
      title: 'توثيق الحلاقين'
    }
  },

  // Booking management
  {
    path: '/bookings',
    name: 'Bookings',
    component: Bookings,
    meta: { 
      requiresAuth: true,
      permission: 'bookings.view',
      title: 'إدارة الحجوزات'
    }
  },
  {
    path: '/bookings/:id',
    name: 'BookingDetails',
    component: BookingDetails,
    meta: { 
      requiresAuth: true,
      permission: 'bookings.view',
      title: 'تفاصيل الحجز'
    }
  },

  // Financial management
  {
    path: '/finance',
    name: 'Finance',
    component: Finance,
    meta: { 
      requiresAuth: true,
      permission: 'finance.view',
      title: 'الإدارة المالية'
    }
  },
  {
    path: '/finance/withdrawals',
    name: 'Withdrawals',
    component: Withdrawals,
    meta: { 
      requiresAuth: true,
      permission: 'finance.withdraw',
      title: 'طلبات السحب'
    }
  },

  // Store management
  {
    path: '/store',
    name: 'Store',
    component: Store,
    meta: { 
      requiresAuth: true,
      permission: 'store.view',
      title: 'إدارة المتجر'
    }
  },
  {
    path: '/store/products',
    name: 'Products',
    component: Products,
    meta: { 
      requiresAuth: true,
      permission: 'store.manage',
      title: 'إدارة المنتجات'
    }
  },
  {
    path: '/store/categories',
    name: 'Categories',
    component: Categories,
    meta: { 
      requiresAuth: true,
      permission: 'store.manage',
      title: 'إدارة الفئات'
    }
  },

  // Analytics
  {
    path: '/analytics',
    name: 'Analytics',
    component: Analytics,
    meta: { 
      requiresAuth: true,
      title: 'التقارير والإحصائيات'
    }
  },

  // Settings
  {
    path: '/dynamic-settings',
    name: 'DynamicSettings',
    component: DynamicSettings,
    meta: { 
      requiresAuth: true,
      permission: 'settings.view',
      title: 'الإعدادات الديناميكية'
    }
  },
  {
    path: '/settings',
    name: 'GeneralSettings',
    component: GeneralSettings,
    meta: { 
      requiresAuth: true,
      permission: 'settings.view',
      title: 'الإعدادات العامة'
    }
  },

  // Profile
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { 
      requiresAuth: true,
      title: 'الملف الشخصي'
    }
  },

  // 404 page
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: 'الصفحة غير موجودة'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Set page title
  document.title = to.meta.title ? `${to.meta.title} - حلاق على بابك` : 'حلاق على بابك'
  
  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // Try to restore session from localStorage
      await authStore.restoreSession()
      
      if (!authStore.isAuthenticated) {
        next('/login')
        return
      }
    }
    
    // Check permissions
    if (to.meta.permission && !authStore.hasPermission(to.meta.permission)) {
      // Redirect to dashboard if no permission
      next('/')
      return
    }
  }
  
  // Redirect to dashboard if already authenticated and trying to access login
  if (to.name === 'Login' && authStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

export default router
