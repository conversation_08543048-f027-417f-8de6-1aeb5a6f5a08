import 'package:flutter/foundation.dart';

class BarberProvider extends ChangeNotifier {
  List<Barber> _barbers = [];
  List<Barber> _favoriteBarbers = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Barber> get barbers => _barbers;
  List<Barber> get favoriteBarbers => _favoriteBarbers;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Load barbers
  Future<void> loadBarbers() async {
    try {
      _setLoading(true);
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      _barbers = [
        <PERSON>(
          id: '1',
          name: 'أحمد محمد',
          rating: 4.8,
          reviewCount: 156,
          distance: 2.5,
          price: 25,
          specialties: ['قص شعر', 'تصفيف'],
          isAvailable: true,
          avatar: '',
        ),
        <PERSON>(
          id: '2',
          name: 'محمد علي',
          rating: 4.9,
          reviewCount: 203,
          distance: 1.8,
          price: 30,
          specialties: ['حلاقة كاملة', 'VIP'],
          isAvailable: true,
          avatar: '',
        ),
        <PERSON>(
          id: '3',
          name: 'علي حسن',
          rating: 4.7,
          reviewCount: 89,
          distance: 3.2,
          price: 35,
          specialties: ['تصفيف', 'علاج الشعر'],
          isAvailable: false,
          avatar: '',
        ),
      ];
      
      _clearError();
    } catch (e) {
      _setError('خطأ في تحميل الحلاقين');
    } finally {
      _setLoading(false);
    }
  }

  // Add to favorites
  void addToFavorites(String barberId) {
    final barber = _barbers.firstWhere((b) => b.id == barberId);
    if (!_favoriteBarbers.contains(barber)) {
      _favoriteBarbers.add(barber);
      notifyListeners();
    }
  }

  // Remove from favorites
  void removeFromFavorites(String barberId) {
    _favoriteBarbers.removeWhere((b) => b.id == barberId);
    notifyListeners();
  }

  // Check if barber is favorite
  bool isFavorite(String barberId) {
    return _favoriteBarbers.any((b) => b.id == barberId);
  }

  // Search barbers
  List<Barber> searchBarbers(String query) {
    if (query.isEmpty) return _barbers;
    
    return _barbers.where((barber) =>
      barber.name.toLowerCase().contains(query.toLowerCase()) ||
      barber.specialties.any((specialty) =>
        specialty.toLowerCase().contains(query.toLowerCase())
      )
    ).toList();
  }

  // Filter barbers
  List<Barber> filterBarbers({
    double? minRating,
    double? maxDistance,
    bool? availableOnly,
    List<String>? specialties,
  }) {
    return _barbers.where((barber) {
      if (minRating != null && barber.rating < minRating) return false;
      if (maxDistance != null && barber.distance > maxDistance) return false;
      if (availableOnly == true && !barber.isAvailable) return false;
      if (specialties != null && specialties.isNotEmpty) {
        if (!barber.specialties.any((s) => specialties.contains(s))) return false;
      }
      return true;
    }).toList();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}

class Barber {
  final String id;
  final String name;
  final double rating;
  final int reviewCount;
  final double distance;
  final double price;
  final List<String> specialties;
  final bool isAvailable;
  final String avatar;

  Barber({
    required this.id,
    required this.name,
    required this.rating,
    required this.reviewCount,
    required this.distance,
    required this.price,
    required this.specialties,
    required this.isAvailable,
    required this.avatar,
  });

  String get initials {
    final names = name.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names[0][0].toUpperCase();
    }
    return 'B';
  }
}
