import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

// Services
import 'services/ar_service.dart';
import 'services/biometric_service.dart';
import 'services/holographic_service.dart';
import 'services/iot_service.dart';
import 'services/telepathic_interface_service.dart';
import 'services/voice_ai_service.dart';

// Providers
import 'providers/auth_provider.dart';
import 'providers/booking_provider.dart';
import 'providers/location_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/language_provider.dart';

// Screens
import 'screens/splash_screen.dart';
import 'screens/onboarding_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/register_screen.dart';
import 'screens/home/<USER>';
import 'screens/booking/booking_screen.dart';
import 'screens/profile/profile_screen.dart';

// Utils
import 'utils/app_constants.dart';
import 'utils/app_theme.dart';
import 'utils/app_localizations.dart';
import 'utils/notification_service.dart';

// Firebase options
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Load environment variables
  await dotenv.load(fileName: ".env");
  
  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  // Initialize Firebase Messaging
  await _initializeFirebaseMessaging();
  
  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );
  
  runApp(const BarberApp());
}

Future<void> _initializeFirebaseMessaging() async {
  FirebaseMessaging messaging = FirebaseMessaging.instance;
  
  // Request permission for notifications
  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );
  
  print('User granted permission: ${settings.authorizationStatus}');
  
  // Handle background messages
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
}

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  print("Handling a background message: ${message.messageId}");
}

class BarberApp extends StatefulWidget {
  const BarberApp({Key? key}) : super(key: key);

  @override
  State<BarberApp> createState() => _BarberAppState();
}

class _BarberAppState extends State<BarberApp> with WidgetsBindingObserver {
  late Future<void> _initializationFuture;
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializationFuture = _initializeServices();
  }
  
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _disposeServices();
    super.dispose();
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        _onAppResumed();
        break;
      case AppLifecycleState.paused:
        _onAppPaused();
        break;
      case AppLifecycleState.detached:
        _onAppDetached();
        break;
      case AppLifecycleState.inactive:
        break;
    }
  }
  
  Future<void> _initializeServices() async {
    try {
      // Initialize core services
      await Future.wait([
        BiometricService().initialize(),
        VoiceAIService().initialize(),
        ARService().initialize(),
        IoTService().initialize(),
        HolographicService().initialize(),
        TelepathicInterfaceService().initialize(),
      ]);
      
      // Initialize notification service
      await NotificationService().initialize();
      
      print('All services initialized successfully');
    } catch (e) {
      print('Error initializing services: $e');
    }
  }
  
  void _disposeServices() {
    // Dispose all services
    BiometricService().dispose();
    VoiceAIService().dispose();
    ARService().dispose();
    IoTService().dispose();
    HolographicService().dispose();
    TelepathicInterfaceService().dispose();
  }
  
  void _onAppResumed() {
    // Handle app resume
    print('App resumed');
  }
  
  void _onAppPaused() {
    // Handle app pause
    print('App paused');
  }
  
  void _onAppDetached() {
    // Handle app detached
    print('App detached');
    _disposeServices();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => BookingProvider()),
        ChangeNotifierProvider(create: (_) => LocationProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
      ],
      child: Consumer2<ThemeProvider, LanguageProvider>(
        builder: (context, themeProvider, languageProvider, child) {
          return MaterialApp(
            title: 'حلاق على بابك',
            debugShowCheckedModeBanner: false,
            
            // Theme configuration
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.themeMode,
            
            // Localization configuration
            locale: languageProvider.currentLocale,
            supportedLocales: const [
              Locale('ar', 'EG'), // Arabic (Egypt)
              Locale('en', 'US'), // English (US)
              Locale('fr', 'FR'), // French
              Locale('tr', 'TR'), // Turkish
              Locale('de', 'DE'), // German
            ],
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            
            // Route configuration
            initialRoute: '/',
            routes: {
              '/': (context) => FutureBuilder<void>(
                future: _initializationFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const SplashScreen();
                  } else if (snapshot.hasError) {
                    return _buildErrorScreen(snapshot.error.toString());
                  } else {
                    return const AppNavigator();
                  }
                },
              ),
              '/onboarding': (context) => const OnboardingScreen(),
              '/login': (context) => const LoginScreen(),
              '/register': (context) => const RegisterScreen(),
              '/home': (context) => const HomeScreen(),
              '/booking': (context) => const BookingScreen(),
              '/profile': (context) => const ProfileScreen(),
            },
            
            // Global error handling
            builder: (context, widget) {
              ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
                return _buildErrorWidget(errorDetails);
              };
              
              return widget!;
            },
          );
        },
      ),
    );
  }
  
  Widget _buildErrorScreen(String error) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تهيئة التطبيق',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                // Restart app
                setState(() {
                  _initializationFuture = _initializeServices();
                });
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildErrorWidget(FlutterErrorDetails errorDetails) {
    return Material(
      child: Container(
        color: Colors.red.shade100,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error,
                size: 48,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'حدث خطأ غير متوقع',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              if (kDebugMode)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    errorDetails.exception.toString(),
                    style: const TextStyle(fontSize: 12),
                    textAlign: TextAlign.center,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class AppNavigator extends StatelessWidget {
  const AppNavigator({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Check authentication status
        if (authProvider.isLoading) {
          return const SplashScreen();
        }
        
        if (!authProvider.isFirstTimeUser && !authProvider.isAuthenticated) {
          return const OnboardingScreen();
        }
        
        if (!authProvider.isAuthenticated) {
          return const LoginScreen();
        }
        
        return const HomeScreen();
      },
    );
  }
}

// Global constants
const bool kDebugMode = bool.fromEnvironment('dart.vm.product') == false;
