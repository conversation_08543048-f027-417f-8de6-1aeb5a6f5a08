# 👨‍💻 دليل المطورين الشامل - حلاق على بابك

## 🏗️ نظرة عامة على البنية

### 📋 **معمارية النظام**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Customer App  │    │   Barber App    │    │ Admin Dashboard │
│   (Flutter)     │    │   (Flutter)     │    │   (Vue.js)      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │     Laravel API        │
                    │   (Backend Service)    │
                    └─────────────┬───────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
    ┌─────┴─────┐         ┌─────┴─────┐         ┌─────┴─────┐
    │   MySQL   │         │   Redis   │         │    S3     │
    │ Database  │         │   Cache   │         │  Storage  │
    └───────────┘         └───────────┘         └───────────┘
```

### 🔧 **التقنيات الأساسية**
- **Backend**: Laravel 10 + PHP 8.1+
- **Frontend**: Vue.js 3 + TypeScript
- **Mobile**: Flutter 3.x + Dart 3.x
- **Database**: MySQL 8.0 + Redis 6.0
- **Storage**: AWS S3 + CloudFront
- **Search**: Elasticsearch 7.x
- **Queue**: Laravel Horizon + Redis
- **Cache**: Redis + Laravel Cache

## 🖥️ Backend API Development

### 📁 **هيكل المشروع**
```
backend/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── API/
│   │   │   │   ├── AuthController.php
│   │   │   │   ├── UserController.php
│   │   │   │   ├── BarberController.php
│   │   │   │   ├── BookingController.php
│   │   │   │   └── PaymentController.php
│   │   │   └── Admin/
│   │   ├── Middleware/
│   │   ├── Requests/
│   │   └── Resources/
│   ├── Models/
│   │   ├── User.php
│   │   ├── Barber.php
│   │   ├── Booking.php
│   │   ├── Service.php
│   │   └── Payment.php
│   ├── Services/
│   │   ├── AuthService.php
│   │   ├── BookingService.php
│   │   ├── PaymentService.php
│   │   └── NotificationService.php
│   └── Jobs/
├── database/
│   ├── migrations/
│   ├── seeders/
│   └── factories/
├── routes/
│   ├── api.php
│   ├── web.php
│   └── admin.php
└── tests/
    ├── Feature/
    └── Unit/
```

### 🔐 **نظام المصادقة**
```php
// app/Http/Controllers/API/AuthController.php
<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\RegisterRequest;
use App\Services\AuthService;
use Illuminate\Http\JsonResponse;

class AuthController extends Controller
{
    public function __construct(
        private AuthService $authService
    ) {}

    public function register(RegisterRequest $request): JsonResponse
    {
        $result = $this->authService->register($request->validated());
        
        return response()->json([
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $result['data'] ?? null,
        ], $result['success'] ? 201 : 422);
    }

    public function login(LoginRequest $request): JsonResponse
    {
        $result = $this->authService->login($request->validated());
        
        return response()->json([
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $result['data'] ?? null,
        ], $result['success'] ? 200 : 401);
    }

    public function logout(): JsonResponse
    {
        $this->authService->logout();
        
        return response()->json([
            'success' => true,
            'message' => 'تم تسجيل الخروج بنجاح',
        ]);
    }
}
```

### 📊 **نموذج البيانات**
```php
// app/Models/Booking.php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Booking extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'customer_id',
        'barber_id',
        'service_id',
        'scheduled_at',
        'status',
        'total_amount',
        'payment_status',
        'notes',
        'address',
        'latitude',
        'longitude',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'total_amount' => 'decimal:2',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function barber(): BelongsTo
    {
        return $this->belongsTo(Barber::class);
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'confirmed', 'in_progress']);
    }

    public function scopeForBarber($query, $barberId)
    {
        return $query->where('barber_id', $barberId);
    }

    // Accessors
    public function getFormattedTotalAttribute(): string
    {
        return number_format($this->total_amount, 2) . ' ج.م';
    }

    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'pending' => 'في الانتظار',
            'confirmed' => 'مؤكد',
            'in_progress' => 'جاري التنفيذ',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
            default => $this->status,
        };
    }
}
```

### 🔄 **خدمة الحجوزات**
```php
// app/Services/BookingService.php
<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\Barber;
use App\Jobs\SendBookingNotification;
use Illuminate\Support\Facades\DB;

class BookingService
{
    public function createBooking(array $data): array
    {
        try {
            DB::beginTransaction();

            // Check barber availability
            if (!$this->isBarberAvailable($data['barber_id'], $data['scheduled_at'])) {
                return [
                    'success' => false,
                    'message' => 'الحلاق غير متاح في هذا الوقت',
                ];
            }

            // Calculate total amount
            $totalAmount = $this->calculateTotalAmount($data);

            // Create booking
            $booking = Booking::create([
                ...$data,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'payment_status' => 'pending',
            ]);

            // Send notifications
            SendBookingNotification::dispatch($booking);

            DB::commit();

            return [
                'success' => true,
                'message' => 'تم إنشاء الحجز بنجاح',
                'data' => $booking->load(['customer', 'barber', 'service']),
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الحجز',
            ];
        }
    }

    private function isBarberAvailable(int $barberId, string $scheduledAt): bool
    {
        $barber = Barber::find($barberId);
        
        if (!$barber || !$barber->is_available) {
            return false;
        }

        // Check for conflicting bookings
        $conflictingBookings = Booking::where('barber_id', $barberId)
            ->where('scheduled_at', $scheduledAt)
            ->whereIn('status', ['pending', 'confirmed', 'in_progress'])
            ->exists();

        return !$conflictingBookings;
    }

    private function calculateTotalAmount(array $data): float
    {
        $service = Service::find($data['service_id']);
        $baseAmount = $service->price;

        // Add VIP surcharge if applicable
        if ($data['is_vip'] ?? false) {
            $baseAmount *= 1.2; // 20% VIP surcharge
        }

        // Add emergency fee if applicable
        if ($data['is_emergency'] ?? false) {
            $baseAmount += 50; // Emergency fee
        }

        return $baseAmount;
    }
}
```

## 📱 Flutter Development

### 📁 **هيكل تطبيق Flutter**
```
lib/
├── core/
│   ├── constants/
│   ├── theme/
│   ├── utils/
│   └── network/
├── features/
│   ├── auth/
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   ├── home/
│   ├── booking/
│   └── profile/
├── shared/
│   ├── widgets/
│   ├── models/
│   └── services/
└── main.dart
```

### 🔧 **إعداد Dependency Injection**
```dart
// core/utils/dependency_injection.dart
import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';

final GetIt getIt = GetIt.instance;

class DependencyInjection {
  static Future<void> init() async {
    // Network
    getIt.registerLazySingleton<Dio>(() {
      final dio = Dio(BaseOptions(
        baseUrl: AppConstants.baseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
      ));
      
      dio.interceptors.addAll([
        AuthInterceptor(),
        LogInterceptor(requestBody: true, responseBody: true),
      ]);
      
      return dio;
    });

    // Services
    getIt.registerLazySingleton<ApiClient>(() => ApiClient(getIt()));
    getIt.registerLazySingleton<AuthService>(() => AuthServiceImpl(getIt()));
    getIt.registerLazySingleton<BookingService>(() => BookingServiceImpl(getIt()));
    
    // Storage
    await _initHive();
    getIt.registerLazySingleton<StorageService>(() => HiveStorageService());
  }

  static Future<void> _initHive() async {
    await Hive.initFlutter();
    await Hive.openBox(AppConstants.userBox);
    await Hive.openBox(AppConstants.settingsBox);
  }
}
```

### 🎨 **نظام الثيم**
```dart
// core/theme/app_theme.dart
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
      ),
      fontFamily: 'Cairo',
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          minimumSize: const Size(double.infinity, 48),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }
}
```

### 🔄 **إدارة الحالة مع Provider**
```dart
// features/booking/providers/booking_provider.dart
class BookingProvider extends ChangeNotifier {
  final BookingService _bookingService;
  
  BookingProvider(this._bookingService);

  List<BookingModel> _bookings = [];
  bool _isLoading = false;
  String? _error;

  List<BookingModel> get bookings => _bookings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadBookings() async {
    _setLoading(true);
    _setError(null);

    try {
      _bookings = await _bookingService.getMyBookings();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> createBooking(CreateBookingRequest request) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _bookingService.createBooking(request);
      
      if (result.success) {
        _bookings.insert(0, result.booking!);
        notifyListeners();
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
}
```

## 🌐 Frontend Development (Vue.js)

### 📁 **هيكل المشروع**
```
admin-dashboard/
├── src/
│   ├── components/
│   │   ├── common/
│   │   ├── charts/
│   │   └── forms/
│   ├── views/
│   │   ├── Dashboard.vue
│   │   ├── Users.vue
│   │   ├── Barbers.vue
│   │   └── Bookings.vue
│   ├── store/
│   │   ├── modules/
│   │   │   ├── auth.ts
│   │   │   ├── users.ts
│   │   │   └── bookings.ts
│   │   └── index.ts
│   ├── services/
│   │   ├── api.ts
│   │   ├── auth.ts
│   │   └── booking.ts
│   ├── utils/
│   └── types/
├── public/
└── package.json
```

### 🔧 **إعداد Pinia Store**
```typescript
// store/modules/bookings.ts
import { defineStore } from 'pinia'
import { BookingService } from '@/services/booking'
import type { Booking, BookingFilters } from '@/types/booking'

export const useBookingsStore = defineStore('bookings', {
  state: () => ({
    bookings: [] as Booking[],
    loading: false,
    error: null as string | null,
    pagination: {
      current_page: 1,
      per_page: 20,
      total: 0,
    },
  }),

  getters: {
    pendingBookings: (state) => 
      state.bookings.filter(booking => booking.status === 'pending'),
    
    completedBookings: (state) => 
      state.bookings.filter(booking => booking.status === 'completed'),
    
    totalRevenue: (state) => 
      state.bookings
        .filter(booking => booking.status === 'completed')
        .reduce((sum, booking) => sum + booking.total_amount, 0),
  },

  actions: {
    async fetchBookings(filters?: BookingFilters) {
      this.loading = true
      this.error = null

      try {
        const response = await BookingService.getBookings(filters)
        this.bookings = response.data
        this.pagination = response.meta
      } catch (error) {
        this.error = 'فشل في جلب الحجوزات'
        console.error(error)
      } finally {
        this.loading = false
      }
    },

    async updateBookingStatus(bookingId: number, status: string) {
      try {
        await BookingService.updateStatus(bookingId, status)
        
        const booking = this.bookings.find(b => b.id === bookingId)
        if (booking) {
          booking.status = status
        }
      } catch (error) {
        this.error = 'فشل في تحديث حالة الحجز'
        throw error
      }
    },
  },
})
```

## 🧪 الاختبارات

### 🔍 **اختبارات Backend (PHPUnit)**
```php
// tests/Feature/BookingTest.php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Barber;
use App\Models\Service;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BookingTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_create_booking()
    {
        $user = User::factory()->create();
        $barber = Barber::factory()->create();
        $service = Service::factory()->create();

        $response = $this->actingAs($user, 'api')
            ->postJson('/api/bookings', [
                'barber_id' => $barber->id,
                'service_id' => $service->id,
                'scheduled_at' => now()->addDay()->format('Y-m-d H:i:s'),
                'address' => 'Test Address',
                'latitude' => 30.0444,
                'longitude' => 31.2357,
            ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'customer_id',
                    'barber_id',
                    'service_id',
                    'status',
                    'total_amount',
                ],
            ]);

        $this->assertDatabaseHas('bookings', [
            'customer_id' => $user->id,
            'barber_id' => $barber->id,
            'service_id' => $service->id,
        ]);
    }

    public function test_cannot_book_unavailable_barber()
    {
        $user = User::factory()->create();
        $barber = Barber::factory()->create(['is_available' => false]);
        $service = Service::factory()->create();

        $response = $this->actingAs($user, 'api')
            ->postJson('/api/bookings', [
                'barber_id' => $barber->id,
                'service_id' => $service->id,
                'scheduled_at' => now()->addDay()->format('Y-m-d H:i:s'),
                'address' => 'Test Address',
            ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'الحلاق غير متاح في هذا الوقت',
            ]);
    }
}
```

### 📱 **اختبارات Flutter (Widget Tests)**
```dart
// test/features/booking/booking_screen_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';

import 'package:customer_app/features/booking/views/booking_screen.dart';
import 'package:customer_app/features/booking/providers/booking_provider.dart';

class MockBookingProvider extends Mock implements BookingProvider {}

void main() {
  group('BookingScreen Tests', () {
    late MockBookingProvider mockProvider;

    setUp(() {
      mockProvider = MockBookingProvider();
    });

    testWidgets('should display loading indicator when loading', (tester) async {
      // Arrange
      when(mockProvider.isLoading).thenReturn(true);
      when(mockProvider.bookings).thenReturn([]);
      when(mockProvider.error).thenReturn(null);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<BookingProvider>.value(
            value: mockProvider,
            child: const BookingScreen(),
          ),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display bookings list when loaded', (tester) async {
      // Arrange
      final mockBookings = [
        BookingModel(
          id: '1',
          serviceName: 'حلاقة كلاسيكية',
          barberName: 'أحمد محمد',
          status: 'confirmed',
          scheduledAt: DateTime.now(),
          totalAmount: 50.0,
        ),
      ];

      when(mockProvider.isLoading).thenReturn(false);
      when(mockProvider.bookings).thenReturn(mockBookings);
      when(mockProvider.error).thenReturn(null);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<BookingProvider>.value(
            value: mockProvider,
            child: const BookingScreen(),
          ),
        ),
      );

      // Assert
      expect(find.text('حلاقة كلاسيكية'), findsOneWidget);
      expect(find.text('أحمد محمد'), findsOneWidget);
      expect(find.text('50.0 ج.م'), findsOneWidget);
    });
  });
}
```

## 📚 أفضل الممارسات

### 🔒 **الأمان**
1. **تشفير البيانات الحساسة**
2. **التحقق من الصلاحيات في كل API**
3. **استخدام HTTPS في جميع الاتصالات**
4. **تنظيف المدخلات من XSS و SQL Injection**
5. **تطبيق Rate Limiting**

### 🚀 **الأداء**
1. **استخدام Cache للبيانات المتكررة**
2. **تحسين استعلامات قاعدة البيانات**
3. **ضغط الصور والملفات**
4. **استخدام CDN للملفات الثابتة**
5. **تطبيق Lazy Loading**

### 🧹 **جودة الكود**
1. **اتباع PSR-12 في PHP**
2. **استخدام TypeScript في Vue.js**
3. **كتابة اختبارات شاملة**
4. **توثيق الكود والAPI**
5. **مراجعة الكود قبل الدمج**

---

**🎯 دليل شامل لتطوير وصيانة المنصة بأعلى معايير الجودة!**
