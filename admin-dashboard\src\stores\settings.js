import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

export const useSettingsStore = defineStore('settings', () => {
  // State
  const settings = ref({})
  const loading = ref(false)
  const categories = ref([
    { key: 'general', name: 'إعدادات عامة', icon: 'mdi-cog' },
    { key: 'features', name: 'المزايا والخصائص', icon: 'mdi-feature-search' },
    { key: 'financial', name: 'الإعدادات المالية', icon: 'mdi-cash' },
    { key: 'booking', name: 'إعدادات الحجز', icon: 'mdi-calendar' },
    { key: 'vip', name: 'إعدادات VIP', icon: 'mdi-crown' },
    { key: 'loyalty', name: 'نظام الولاء', icon: 'mdi-star' },
    { key: 'payment', name: 'بوابات الدفع', icon: 'mdi-credit-card' },
    { key: 'notifications', name: 'الإشعارات', icon: 'mdi-bell' },
    { key: 'maps', name: 'الخرائط والمواقع', icon: 'mdi-map' },
    { key: 'store', name: 'المتجر', icon: 'mdi-store' },
    { key: 'ai', name: 'الذكاء الاصطناعي', icon: 'mdi-robot' },
    { key: 'marketing', name: 'التسويق', icon: 'mdi-bullhorn' },
    { key: 'system', name: 'النظام', icon: 'mdi-server' }
  ])

  // Getters
  const settingsByCategory = computed(() => {
    const grouped = {}
    categories.value.forEach(category => {
      grouped[category.key] = Object.entries(settings.value)
        .filter(([key, setting]) => setting.category === category.key)
        .reduce((obj, [key, setting]) => {
          obj[key] = setting
          return obj
        }, {})
    })
    return grouped
  })

  const featureFlags = computed(() => {
    return Object.entries(settings.value)
      .filter(([key, setting]) => setting.type === 'boolean' && key.endsWith('_enabled'))
      .reduce((obj, [key, setting]) => {
        obj[key] = setting.value
        return obj
      }, {})
  })

  // Actions
  const fetchAllSettings = async () => {
    loading.value = true
    try {
      const response = await axios.get('/api/v1/admin/settings')
      
      if (response.data.success) {
        // Flatten the grouped settings
        const flatSettings = {}
        Object.values(response.data.data).forEach(categorySettings => {
          Object.values(categorySettings).forEach(setting => {
            flatSettings[setting.key] = setting
          })
        })
        settings.value = flatSettings
        return { success: true }
      } else {
        throw new Error(response.data.message || 'فشل في جلب الإعدادات')
      }
    } catch (error) {
      console.error('Fetch settings error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'حدث خطأ أثناء جلب الإعدادات'
      }
    } finally {
      loading.value = false
    }
  }

  const updateSettings = async (settingsToUpdate) => {
    loading.value = true
    try {
      const response = await axios.put('/api/v1/admin/settings', {
        settings: settingsToUpdate
      })
      
      if (response.data.success) {
        // Update local settings
        Object.entries(response.data.data).forEach(([key, value]) => {
          if (settings.value[key]) {
            settings.value[key].value = value
          }
        })
        return { success: true, message: 'تم تحديث الإعدادات بنجاح' }
      } else {
        throw new Error(response.data.message || 'فشل في تحديث الإعدادات')
      }
    } catch (error) {
      console.error('Update settings error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'حدث خطأ أثناء تحديث الإعدادات'
      }
    } finally {
      loading.value = false
    }
  }

  const toggleFeature = async (feature, enabled = null) => {
    loading.value = true
    try {
      const response = await axios.post(`/api/v1/admin/settings/toggle/${feature}`, {
        enabled
      })
      
      if (response.data.success) {
        const { feature: featureName, enabled: isEnabled } = response.data.data
        const settingKey = `${featureName}_enabled`
        
        if (settings.value[settingKey]) {
          settings.value[settingKey].value = isEnabled
        }
        
        return { 
          success: true, 
          message: `تم ${isEnabled ? 'تفعيل' : 'تعطيل'} ${featureName} بنجاح`,
          enabled: isEnabled
        }
      } else {
        throw new Error(response.data.message || 'فشل في تغيير حالة الميزة')
      }
    } catch (error) {
      console.error('Toggle feature error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'حدث خطأ أثناء تغيير حالة الميزة'
      }
    } finally {
      loading.value = false
    }
  }

  const clearCache = async () => {
    loading.value = true
    try {
      const response = await axios.post('/api/v1/admin/settings/clear-cache')
      
      if (response.data.success) {
        return { success: true, message: 'تم مسح ذاكرة التخزين المؤقت بنجاح' }
      } else {
        throw new Error(response.data.message || 'فشل في مسح ذاكرة التخزين المؤقت')
      }
    } catch (error) {
      console.error('Clear cache error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'حدث خطأ أثناء مسح ذاكرة التخزين المؤقت'
      }
    } finally {
      loading.value = false
    }
  }

  const exportSettings = async () => {
    loading.value = true
    try {
      const response = await axios.get('/api/v1/admin/settings/export')
      
      if (response.data.success) {
        // Create and download file
        const dataStr = JSON.stringify(response.data.data, null, 2)
        const dataBlob = new Blob([dataStr], { type: 'application/json' })
        const url = URL.createObjectURL(dataBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `settings-export-${new Date().toISOString().split('T')[0]}.json`
        link.click()
        URL.revokeObjectURL(url)
        
        return { success: true, message: 'تم تصدير الإعدادات بنجاح' }
      } else {
        throw new Error(response.data.message || 'فشل في تصدير الإعدادات')
      }
    } catch (error) {
      console.error('Export settings error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'حدث خطأ أثناء تصدير الإعدادات'
      }
    } finally {
      loading.value = false
    }
  }

  const getSetting = (key, defaultValue = null) => {
    return settings.value[key]?.value ?? defaultValue
  }

  const getSettingsByCategory = (category) => {
    return Object.entries(settings.value)
      .filter(([key, setting]) => setting.category === category)
      .reduce((obj, [key, setting]) => {
        obj[key] = setting
        return obj
      }, {})
  }

  const isFeatureEnabled = (feature) => {
    const key = feature.endsWith('_enabled') ? feature : `${feature}_enabled`
    return getSetting(key, false)
  }

  const getCategoryInfo = (categoryKey) => {
    return categories.value.find(cat => cat.key === categoryKey)
  }

  return {
    // State
    settings,
    loading,
    categories,
    
    // Getters
    settingsByCategory,
    featureFlags,
    
    // Actions
    fetchAllSettings,
    updateSettings,
    toggleFeature,
    clearCache,
    exportSettings,
    getSetting,
    getSettingsByCategory,
    isFeatureEnabled,
    getCategoryInfo
  }
})
