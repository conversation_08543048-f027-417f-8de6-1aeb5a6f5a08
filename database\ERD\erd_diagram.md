# مخطط قاعدة البيانات - ERD

## العلاقات الأساسية بين الجداول

```mermaid
erDiagram
    USERS {
        bigint id PK
        string name
        string email UK
        string phone UK
        timestamp email_verified_at
        timestamp phone_verified_at
        string password
        string avatar
        date date_of_birth
        enum gender
        bigint city_id FK
        text address
        decimal latitude
        decimal longitude
        boolean is_active
        bigint vip_subscription_id FK
        int loyalty_points
        int total_bookings
        timestamp created_at
        timestamp updated_at
    }

    BARBERS {
        bigint id PK
        string name
        string email UK
        string phone UK
        string password
        string avatar
        date date_of_birth
        int experience_years
        text bio
        bigint city_id FK
        text address
        decimal latitude
        decimal longitude
        boolean is_verified
        boolean is_active
        boolean is_vip
        boolean is_available
        decimal rating
        int total_ratings
        int total_bookings
        decimal commission_rate
        decimal wallet_balance
        json documents
        json working_hours
        json services
        timestamp created_at
        timestamp updated_at
    }

    CITIES {
        bigint id PK
        string name
        string name_en
        string governorate
        string country
        decimal latitude
        decimal longitude
        boolean is_active
        decimal delivery_fee
        decimal min_order_amount
        json features
        timestamp created_at
        timestamp updated_at
    }

    SERVICES {
        bigint id PK
        string name
        string name_en
        text description
        decimal price
        int duration_minutes
        bigint category_id FK
        string image
        boolean is_active
        boolean is_vip_only
        timestamp created_at
        timestamp updated_at
    }

    SERVICE_CATEGORIES {
        bigint id PK
        string name
        string name_en
        text description
        string icon
        int sort_order
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    BOOKINGS {
        bigint id PK
        string booking_number UK
        bigint user_id FK
        bigint barber_id FK
        bigint service_id FK
        date booking_date
        time booking_time
        enum status
        decimal total_amount
        decimal discount_amount
        decimal final_amount
        enum payment_method
        enum payment_status
        text address
        decimal latitude
        decimal longitude
        text notes
        boolean is_vip
        int rating
        text review
        timestamp created_at
        timestamp updated_at
    }

    VIP_SUBSCRIPTIONS {
        bigint id PK
        bigint user_id FK
        enum plan_type
        date start_date
        date end_date
        int remaining_bookings
        decimal total_amount
        decimal discount_percentage
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    SETTINGS {
        bigint id PK
        string key UK
        text value
        enum type
        string category
        text description
        boolean is_public
        timestamp created_at
        timestamp updated_at
    }

    ROLES {
        bigint id PK
        string name UK
        string display_name
        text description
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    PERMISSIONS {
        bigint id PK
        string name UK
        string display_name
        text description
        string module
        timestamp created_at
        timestamp updated_at
    }

    ROLE_PERMISSIONS {
        bigint role_id FK
        bigint permission_id FK
    }

    ADMINS {
        bigint id PK
        string name
        string email UK
        string password
        string avatar
        bigint role_id FK
        boolean is_active
        timestamp last_login_at
        timestamp created_at
        timestamp updated_at
    }

    WALLET_TRANSACTIONS {
        bigint id PK
        bigint user_id FK
        bigint barber_id FK
        bigint booking_id FK
        enum type
        decimal amount
        text description
        string reference_number
        enum status
        timestamp created_at
        timestamp updated_at
    }

    COUPONS {
        bigint id PK
        string code UK
        enum type
        decimal value
        decimal min_amount
        decimal max_discount
        int usage_limit
        int used_count
        boolean is_active
        date valid_from
        date valid_until
        json applicable_services
        json applicable_cities
        timestamp created_at
        timestamp updated_at
    }

    NOTIFICATIONS {
        bigint id PK
        bigint user_id FK
        bigint barber_id FK
        bigint admin_id FK
        string title
        text message
        enum type
        json data
        boolean is_read
        timestamp sent_at
        timestamp read_at
    }

    STORE_PRODUCTS {
        bigint id PK
        string name
        string name_en
        text description
        decimal price
        decimal discount_price
        bigint category_id FK
        string brand
        string sku UK
        int stock_quantity
        json images
        boolean is_active
        boolean is_featured
        timestamp created_at
        timestamp updated_at
    }

    PRODUCT_CATEGORIES {
        bigint id PK
        string name
        string name_en
        text description
        string image
        bigint parent_id FK
        int sort_order
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    %% العلاقات الأساسية
    USERS ||--o{ BOOKINGS : "يحجز"
    BARBERS ||--o{ BOOKINGS : "يخدم"
    SERVICES ||--o{ BOOKINGS : "يُحجز"
    CITIES ||--o{ USERS : "يسكن في"
    CITIES ||--o{ BARBERS : "يعمل في"
    SERVICE_CATEGORIES ||--o{ SERVICES : "يحتوي على"
    USERS ||--o| VIP_SUBSCRIPTIONS : "يشترك في"
    ROLES ||--o{ ADMINS : "له دور"
    ROLES ||--o{ ROLE_PERMISSIONS : "له صلاحيات"
    PERMISSIONS ||--o{ ROLE_PERMISSIONS : "مرتبط بدور"
    USERS ||--o{ WALLET_TRANSACTIONS : "معاملة مالية"
    BARBERS ||--o{ WALLET_TRANSACTIONS : "معاملة مالية"
    BOOKINGS ||--o{ WALLET_TRANSACTIONS : "مرتبط بحجز"
    USERS ||--o{ NOTIFICATIONS : "يستقبل"
    BARBERS ||--o{ NOTIFICATIONS : "يستقبل"
    ADMINS ||--o{ NOTIFICATIONS : "يرسل"
    PRODUCT_CATEGORIES ||--o{ STORE_PRODUCTS : "يحتوي على"
    PRODUCT_CATEGORIES ||--o{ PRODUCT_CATEGORIES : "فئة فرعية"
```
