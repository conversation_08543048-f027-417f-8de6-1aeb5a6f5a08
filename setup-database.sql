-- إعد<PERSON> قاعدة البيانات لمشروع "حلاق على بابك"
-- Database Setup for Barber App Project

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS barber_app 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE barber_app;

-- إنشاء مستخدم قاعدة البيانات (اختياري)
-- CREATE USER IF NOT EXISTS 'barber_user'@'localhost' IDENTIFIED BY 'barber_password';
-- GRANT ALL PRIVILEGES ON barber_app.* TO 'barber_user'@'localhost';
-- FLUSH PRIVILEGES;

-- إ<PERSON>د<PERSON> المتغيرات
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `phone_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `user_type` enum('customer','barber','admin') NOT NULL DEFAULT 'customer',
  `avatar` varchar(255) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `location` json DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city_id` bigint(20) UNSIGNED DEFAULT NULL,
  `area_id` bigint(20) UNSIGNED DEFAULT NULL,
  `preferences` json DEFAULT NULL,
  `status` enum('active','inactive','suspended','pending_verification') NOT NULL DEFAULT 'pending_verification',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `last_login_ip` varchar(45) DEFAULT NULL,
  `last_login_user_agent` text DEFAULT NULL,
  `registration_ip` varchar(45) DEFAULT NULL,
  `registration_user_agent` text DEFAULT NULL,
  `two_factor_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `two_factor_secret` varchar(255) DEFAULT NULL,
  `quantum_key_id` varchar(255) DEFAULT NULL,
  `security_level` enum('basic','enhanced','quantum_enhanced','cosmic_level') NOT NULL DEFAULT 'basic',
  `biometric_data` text DEFAULT NULL,
  `neural_pattern_id` varchar(255) DEFAULT NULL,
  `telepathic_network_id` varchar(255) DEFAULT NULL,
  `holographic_profile_id` varchar(255) DEFAULT NULL,
  `space_computing_access` tinyint(1) NOT NULL DEFAULT 0,
  `neuro_ai_profile` json DEFAULT NULL,
  `carbon_footprint_score` decimal(8,2) DEFAULT 0.00,
  `sustainability_level` varchar(50) DEFAULT NULL,
  `loyalty_points` int(11) NOT NULL DEFAULT 0,
  `vip_level` enum('bronze','silver','gold','platinum','diamond','cosmic') DEFAULT NULL,
  `referral_code` varchar(20) DEFAULT NULL,
  `referred_by` bigint(20) UNSIGNED DEFAULT NULL,
  `language` varchar(5) NOT NULL DEFAULT 'ar',
  `timezone` varchar(50) NOT NULL DEFAULT 'Africa/Cairo',
  `notification_preferences` json DEFAULT NULL,
  `privacy_settings` json DEFAULT NULL,
  `marketing_consent` tinyint(1) NOT NULL DEFAULT 0,
  `data_processing_consent` tinyint(1) NOT NULL DEFAULT 0,
  `terms_accepted_at` timestamp NULL DEFAULT NULL,
  `privacy_policy_accepted_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  UNIQUE KEY `users_phone_unique` (`phone`),
  UNIQUE KEY `users_referral_code_unique` (`referral_code`),
  KEY `users_city_id_foreign` (`city_id`),
  KEY `users_area_id_foreign` (`area_id`),
  KEY `users_referred_by_foreign` (`referred_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المدن
CREATE TABLE IF NOT EXISTS `cities` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name_ar` varchar(255) NOT NULL,
  `name_en` varchar(255) NOT NULL,
  `country` varchar(100) NOT NULL DEFAULT 'Egypt',
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المناطق
CREATE TABLE IF NOT EXISTS `areas` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `city_id` bigint(20) UNSIGNED NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `name_en` varchar(255) NOT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `areas_city_id_foreign` (`city_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الخدمات
CREATE TABLE IF NOT EXISTS `services` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name_ar` varchar(255) NOT NULL,
  `name_en` varchar(255) NOT NULL,
  `description_ar` text DEFAULT NULL,
  `description_en` text DEFAULT NULL,
  `category` varchar(100) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `duration` int(11) NOT NULL COMMENT 'Duration in minutes',
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بيانات المدن الأساسية
INSERT INTO `cities` (`name_ar`, `name_en`, `country`, `latitude`, `longitude`, `is_active`) VALUES
('القاهرة', 'Cairo', 'Egypt', 30.0444, 31.2357, 1),
('الجيزة', 'Giza', 'Egypt', 30.0131, 31.2089, 1),
('الإسكندرية', 'Alexandria', 'Egypt', 31.2001, 29.9187, 1),
('الأقصر', 'Luxor', 'Egypt', 25.6872, 32.6396, 1),
('أسوان', 'Aswan', 'Egypt', 24.0889, 32.8998, 1);

-- إدراج بيانات المناطق الأساسية للقاهرة
INSERT INTO `areas` (`city_id`, `name_ar`, `name_en`, `latitude`, `longitude`, `is_active`) VALUES
(1, 'مدينة نصر', 'Nasr City', 30.0626, 31.3219, 1),
(1, 'المعادي', 'Maadi', 29.9602, 31.2569, 1),
(1, 'الزمالك', 'Zamalek', 30.0626, 31.2197, 1),
(1, 'مصر الجديدة', 'Heliopolis', 30.0808, 31.3220, 1),
(1, 'المهندسين', 'Mohandessin', 30.0626, 31.2000, 1);

-- إدراج الخدمات الأساسية
INSERT INTO `services` (`name_ar`, `name_en`, `description_ar`, `description_en`, `category`, `price`, `duration`, `is_active`) VALUES
('قص شعر رجالي', 'Men\'s Haircut', 'قص شعر احترافي للرجال', 'Professional men\'s haircut', 'haircut', 50.00, 30, 1),
('حلاقة ذقن', 'Beard Trim', 'تهذيب وحلاقة الذقن', 'Beard trimming and shaving', 'shaving', 30.00, 20, 1),
('قص شعر + ذقن', 'Haircut + Beard', 'قص شعر مع تهذيب الذقن', 'Haircut with beard trimming', 'combo', 70.00, 45, 1),
('غسيل شعر', 'Hair Wash', 'غسيل وتنظيف الشعر', 'Hair washing and cleaning', 'wash', 20.00, 15, 1),
('تصفيف شعر', 'Hair Styling', 'تصفيف الشعر بأحدث الطرق', 'Modern hair styling', 'styling', 40.00, 25, 1);

-- إنشاء مستخدم إداري افتراضي
INSERT INTO `users` (
  `name`, `email`, `password`, `user_type`, `status`, 
  `email_verified_at`, `security_level`, `language`, 
  `created_at`, `updated_at`
) VALUES (
  'مدير النظام', 
  '<EMAIL>', 
  '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
  'admin', 
  'active', 
  NOW(), 
  'quantum_enhanced', 
  'ar', 
  NOW(), 
  NOW()
);

-- إضافة القيود الخارجية
ALTER TABLE `users`
  ADD CONSTRAINT `users_city_id_foreign` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `users_area_id_foreign` FOREIGN KEY (`area_id`) REFERENCES `areas` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `users_referred_by_foreign` FOREIGN KEY (`referred_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `areas`
  ADD CONSTRAINT `areas_city_id_foreign` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`) ON DELETE CASCADE;

-- إعادة تفعيل فحص القيود الخارجية
SET FOREIGN_KEY_CHECKS = 1;
COMMIT;

-- رسالة نجاح
SELECT 'تم إعداد قاعدة البيانات بنجاح!' as message;
