name: barber_customer_app
description: تطبيق العملاء لحجز خدمات الحلاقة المنزلية
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  
  # State Management
  provider: ^6.1.1
  get: ^4.6.6
  
  # Navigation
  go_router: ^12.1.3
  
  # Network & API
  http: ^1.1.0
  dio: ^5.3.3
  connectivity_plus: ^5.0.2
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Maps & Location
  google_maps_flutter: ^2.5.0
  location: ^5.0.3
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  
  # Authentication & Security
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  firebase_messaging: ^14.7.10
  local_auth: ^2.1.7
  
  # Payment
  flutter_stripe: ^9.5.0
  
  # Media & Files
  image_picker: ^1.0.4
  file_picker: ^6.1.1
  permission_handler: ^11.1.0
  
  # Utils
  intl: ^0.18.1
  url_launcher: ^6.2.1
  share_plus: ^7.2.1
  package_info_plus: ^4.2.0
  device_info_plus: ^9.1.1
  
  # Animations
  flutter_animate: ^4.3.0
  rive: ^0.12.4
  
  # Rating & Reviews
  flutter_rating_bar: ^4.0.1
  
  # QR Code
  qr_flutter: ^4.1.0
  qr_code_scanner: ^1.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
  
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
