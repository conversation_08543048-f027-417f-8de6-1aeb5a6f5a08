<?php

namespace App\Services;

use App\Models\User;
use App\Models\Booking;
use App\Models\Payment;
use App\Models\Review;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BlockchainService
{
    protected $blockchainApiUrl;
    protected $blockchainApiKey;
    protected $contractAddress;
    protected $walletAddress;
    protected $privateKey;

    public function __construct()
    {
        $this->blockchainApiUrl = config('services.blockchain.api_url');
        $this->blockchainApiKey = config('services.blockchain.api_key');
        $this->contractAddress = config('services.blockchain.contract_address');
        $this->walletAddress = config('services.blockchain.wallet_address');
        $this->privateKey = config('services.blockchain.private_key');
    }

    /**
     * Create a blockchain record for a booking
     */
    public function createBookingRecord(Booking $booking): array
    {
        try {
            $bookingData = [
                'booking_id' => $booking->id,
                'customer_id' => $booking->customer_id,
                'barber_id' => $booking->barber_id,
                'service_ids' => $booking->services->pluck('id')->toArray(),
                'total_amount' => $booking->total_amount,
                'scheduled_date' => $booking->scheduled_date->toISOString(),
                'status' => $booking->status,
                'created_at' => $booking->created_at->toISOString(),
                'hash' => $this->generateBookingHash($booking)
            ];

            $response = $this->callBlockchainAPI('bookings/create', $bookingData);

            if ($response['success']) {
                // Store blockchain transaction hash
                $booking->update([
                    'blockchain_hash' => $response['transaction_hash'],
                    'blockchain_block' => $response['block_number']
                ]);

                Log::info('Booking recorded on blockchain', [
                    'booking_id' => $booking->id,
                    'transaction_hash' => $response['transaction_hash']
                ]);

                return [
                    'success' => true,
                    'transaction_hash' => $response['transaction_hash'],
                    'block_number' => $response['block_number']
                ];
            }

            return ['success' => false, 'error' => $response['error'] ?? 'Unknown error'];

        } catch (\Exception $e) {
            Log::error('Blockchain booking creation failed', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Update booking status on blockchain
     */
    public function updateBookingStatus(Booking $booking, string $newStatus): array
    {
        try {
            $updateData = [
                'booking_id' => $booking->id,
                'new_status' => $newStatus,
                'updated_at' => now()->toISOString(),
                'previous_hash' => $booking->blockchain_hash,
                'update_hash' => $this->generateStatusUpdateHash($booking, $newStatus)
            ];

            $response = $this->callBlockchainAPI('bookings/update-status', $updateData);

            if ($response['success']) {
                Log::info('Booking status updated on blockchain', [
                    'booking_id' => $booking->id,
                    'new_status' => $newStatus,
                    'transaction_hash' => $response['transaction_hash']
                ]);

                return [
                    'success' => true,
                    'transaction_hash' => $response['transaction_hash']
                ];
            }

            return ['success' => false, 'error' => $response['error'] ?? 'Unknown error'];

        } catch (\Exception $e) {
            Log::error('Blockchain status update failed', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Record payment on blockchain
     */
    public function recordPayment(Payment $payment): array
    {
        try {
            $paymentData = [
                'payment_id' => $payment->id,
                'booking_id' => $payment->booking_id,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'payment_method' => $payment->payment_method,
                'status' => $payment->status,
                'transaction_id' => $payment->transaction_id,
                'processed_at' => $payment->processed_at?->toISOString(),
                'hash' => $this->generatePaymentHash($payment)
            ];

            $response = $this->callBlockchainAPI('payments/record', $paymentData);

            if ($response['success']) {
                $payment->update([
                    'blockchain_hash' => $response['transaction_hash'],
                    'blockchain_block' => $response['block_number']
                ]);

                Log::info('Payment recorded on blockchain', [
                    'payment_id' => $payment->id,
                    'transaction_hash' => $response['transaction_hash']
                ]);

                return [
                    'success' => true,
                    'transaction_hash' => $response['transaction_hash'],
                    'block_number' => $response['block_number']
                ];
            }

            return ['success' => false, 'error' => $response['error'] ?? 'Unknown error'];

        } catch (\Exception $e) {
            Log::error('Blockchain payment recording failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Record review on blockchain for immutability
     */
    public function recordReview(Review $review): array
    {
        try {
            $reviewData = [
                'review_id' => $review->id,
                'booking_id' => $review->booking_id,
                'customer_id' => $review->customer_id,
                'barber_id' => $review->barber_id,
                'rating' => $review->rating,
                'comment_hash' => hash('sha256', $review->comment),
                'created_at' => $review->created_at->toISOString(),
                'hash' => $this->generateReviewHash($review)
            ];

            $response = $this->callBlockchainAPI('reviews/record', $reviewData);

            if ($response['success']) {
                $review->update([
                    'blockchain_hash' => $response['transaction_hash'],
                    'blockchain_block' => $response['block_number']
                ]);

                Log::info('Review recorded on blockchain', [
                    'review_id' => $review->id,
                    'transaction_hash' => $response['transaction_hash']
                ]);

                return [
                    'success' => true,
                    'transaction_hash' => $response['transaction_hash'],
                    'block_number' => $response['block_number']
                ];
            }

            return ['success' => false, 'error' => $response['error'] ?? 'Unknown error'];

        } catch (\Exception $e) {
            Log::error('Blockchain review recording failed', [
                'review_id' => $review->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Create loyalty tokens for user
     */
    public function createLoyaltyTokens(User $user, int $points, string $reason): array
    {
        try {
            $tokenData = [
                'user_id' => $user->id,
                'points' => $points,
                'reason' => $reason,
                'created_at' => now()->toISOString(),
                'token_type' => 'loyalty',
                'hash' => $this->generateLoyaltyHash($user, $points, $reason)
            ];

            $response = $this->callBlockchainAPI('tokens/create-loyalty', $tokenData);

            if ($response['success']) {
                Log::info('Loyalty tokens created on blockchain', [
                    'user_id' => $user->id,
                    'points' => $points,
                    'transaction_hash' => $response['transaction_hash']
                ]);

                return [
                    'success' => true,
                    'transaction_hash' => $response['transaction_hash'],
                    'token_address' => $response['token_address']
                ];
            }

            return ['success' => false, 'error' => $response['error'] ?? 'Unknown error'];

        } catch (\Exception $e) {
            Log::error('Blockchain loyalty token creation failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Transfer loyalty tokens between users
     */
    public function transferLoyaltyTokens(User $from, User $to, int $points): array
    {
        try {
            $transferData = [
                'from_user_id' => $from->id,
                'to_user_id' => $to->id,
                'points' => $points,
                'transfer_type' => 'loyalty_transfer',
                'timestamp' => now()->toISOString(),
                'hash' => $this->generateTransferHash($from, $to, $points)
            ];

            $response = $this->callBlockchainAPI('tokens/transfer', $transferData);

            if ($response['success']) {
                Log::info('Loyalty tokens transferred on blockchain', [
                    'from_user_id' => $from->id,
                    'to_user_id' => $to->id,
                    'points' => $points,
                    'transaction_hash' => $response['transaction_hash']
                ]);

                return [
                    'success' => true,
                    'transaction_hash' => $response['transaction_hash']
                ];
            }

            return ['success' => false, 'error' => $response['error'] ?? 'Unknown error'];

        } catch (\Exception $e) {
            Log::error('Blockchain token transfer failed', [
                'from_user_id' => $from->id,
                'to_user_id' => $to->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Verify blockchain record integrity
     */
    public function verifyRecord(string $recordType, int $recordId, string $blockchainHash): array
    {
        try {
            $verificationData = [
                'record_type' => $recordType,
                'record_id' => $recordId,
                'blockchain_hash' => $blockchainHash
            ];

            $response = $this->callBlockchainAPI('verify/record', $verificationData);

            if ($response['success']) {
                return [
                    'verified' => $response['verified'],
                    'block_number' => $response['block_number'],
                    'timestamp' => $response['timestamp'],
                    'confirmations' => $response['confirmations']
                ];
            }

            return ['verified' => false, 'error' => $response['error'] ?? 'Verification failed'];

        } catch (\Exception $e) {
            Log::error('Blockchain verification failed', [
                'record_type' => $recordType,
                'record_id' => $recordId,
                'error' => $e->getMessage()
            ]);

            return ['verified' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get blockchain transaction history for a user
     */
    public function getUserTransactionHistory(User $user, int $limit = 50): array
    {
        try {
            $cacheKey = "blockchain:user_history:{$user->id}:{$limit}";
            
            return Cache::remember($cacheKey, 300, function () use ($user, $limit) {
                $response = $this->callBlockchainAPI('users/transaction-history', [
                    'user_id' => $user->id,
                    'limit' => $limit
                ]);

                if ($response['success']) {
                    return [
                        'success' => true,
                        'transactions' => $response['transactions'],
                        'total_count' => $response['total_count']
                    ];
                }

                return ['success' => false, 'error' => $response['error'] ?? 'Failed to fetch history'];
            });

        } catch (\Exception $e) {
            Log::error('Failed to fetch blockchain transaction history', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Create smart contract for service agreement
     */
    public function createServiceContract(Booking $booking): array
    {
        try {
            $contractData = [
                'booking_id' => $booking->id,
                'customer_address' => $this->getUserWalletAddress($booking->customer),
                'barber_address' => $this->getUserWalletAddress($booking->barber->user),
                'service_details' => $booking->services->map(function ($service) {
                    return [
                        'id' => $service->id,
                        'name' => $service->name,
                        'price' => $service->pivot->unit_price,
                        'duration' => $service->duration_minutes
                    ];
                })->toArray(),
                'total_amount' => $booking->total_amount,
                'scheduled_date' => $booking->scheduled_date->toISOString(),
                'terms' => $this->generateServiceTerms($booking),
                'escrow_amount' => $booking->total_amount * 0.1, // 10% escrow
                'contract_hash' => $this->generateContractHash($booking)
            ];

            $response = $this->callBlockchainAPI('contracts/create-service', $contractData);

            if ($response['success']) {
                $booking->update([
                    'smart_contract_address' => $response['contract_address'],
                    'contract_hash' => $response['contract_hash']
                ]);

                Log::info('Smart contract created for booking', [
                    'booking_id' => $booking->id,
                    'contract_address' => $response['contract_address']
                ]);

                return [
                    'success' => true,
                    'contract_address' => $response['contract_address'],
                    'transaction_hash' => $response['transaction_hash']
                ];
            }

            return ['success' => false, 'error' => $response['error'] ?? 'Contract creation failed'];

        } catch (\Exception $e) {
            Log::error('Smart contract creation failed', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Execute smart contract payment release
     */
    public function executeContractPayment(Booking $booking): array
    {
        try {
            if (!$booking->smart_contract_address) {
                return ['success' => false, 'error' => 'No smart contract found for this booking'];
            }

            $executionData = [
                'contract_address' => $booking->smart_contract_address,
                'booking_id' => $booking->id,
                'execution_type' => 'release_payment',
                'conditions_met' => $this->verifyContractConditions($booking),
                'timestamp' => now()->toISOString()
            ];

            $response = $this->callBlockchainAPI('contracts/execute-payment', $executionData);

            if ($response['success']) {
                Log::info('Smart contract payment executed', [
                    'booking_id' => $booking->id,
                    'contract_address' => $booking->smart_contract_address,
                    'transaction_hash' => $response['transaction_hash']
                ]);

                return [
                    'success' => true,
                    'transaction_hash' => $response['transaction_hash'],
                    'amount_released' => $response['amount_released']
                ];
            }

            return ['success' => false, 'error' => $response['error'] ?? 'Payment execution failed'];

        } catch (\Exception $e) {
            Log::error('Smart contract payment execution failed', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get blockchain analytics and insights
     */
    public function getBlockchainAnalytics(): array
    {
        try {
            $cacheKey = 'blockchain:analytics';
            
            return Cache::remember($cacheKey, 600, function () {
                $response = $this->callBlockchainAPI('analytics/overview', []);

                if ($response['success']) {
                    return [
                        'total_transactions' => $response['total_transactions'],
                        'total_value_locked' => $response['total_value_locked'],
                        'active_contracts' => $response['active_contracts'],
                        'token_circulation' => $response['token_circulation'],
                        'network_fees' => $response['network_fees'],
                        'carbon_footprint' => $response['carbon_footprint'],
                        'security_score' => $response['security_score']
                    ];
                }

                return ['error' => $response['error'] ?? 'Analytics unavailable'];
            });

        } catch (\Exception $e) {
            Log::error('Failed to fetch blockchain analytics', [
                'error' => $e->getMessage()
            ]);

            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Call blockchain API
     */
    protected function callBlockchainAPI(string $endpoint, array $data): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->blockchainApiKey,
                'Content-Type' => 'application/json'
            ])->timeout(30)->post("{$this->blockchainApiUrl}/{$endpoint}", $data);

            if ($response->successful()) {
                return $response->json();
            }

            return [
                'success' => false,
                'error' => 'API request failed: ' . $response->status()
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'API call failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate secure hash for booking
     */
    protected function generateBookingHash(Booking $booking): string
    {
        $data = [
            $booking->id,
            $booking->customer_id,
            $booking->barber_id,
            $booking->total_amount,
            $booking->scheduled_date->toISOString(),
            $booking->created_at->toISOString()
        ];

        return hash('sha256', implode('|', $data));
    }

    /**
     * Generate hash for status update
     */
    protected function generateStatusUpdateHash(Booking $booking, string $newStatus): string
    {
        $data = [
            $booking->id,
            $booking->blockchain_hash,
            $newStatus,
            now()->toISOString()
        ];

        return hash('sha256', implode('|', $data));
    }

    /**
     * Generate secure hash for payment
     */
    protected function generatePaymentHash(Payment $payment): string
    {
        $data = [
            $payment->id,
            $payment->booking_id,
            $payment->amount,
            $payment->currency,
            $payment->payment_method,
            $payment->transaction_id
        ];

        return hash('sha256', implode('|', $data));
    }

    /**
     * Generate secure hash for review
     */
    protected function generateReviewHash(Review $review): string
    {
        $data = [
            $review->id,
            $review->booking_id,
            $review->customer_id,
            $review->barber_id,
            $review->rating,
            hash('sha256', $review->comment),
            $review->created_at->toISOString()
        ];

        return hash('sha256', implode('|', $data));
    }

    /**
     * Generate hash for loyalty tokens
     */
    protected function generateLoyaltyHash(User $user, int $points, string $reason): string
    {
        $data = [
            $user->id,
            $points,
            $reason,
            now()->toISOString()
        ];

        return hash('sha256', implode('|', $data));
    }

    /**
     * Generate hash for token transfer
     */
    protected function generateTransferHash(User $from, User $to, int $points): string
    {
        $data = [
            $from->id,
            $to->id,
            $points,
            now()->toISOString()
        ];

        return hash('sha256', implode('|', $data));
    }

    /**
     * Generate hash for smart contract
     */
    protected function generateContractHash(Booking $booking): string
    {
        $data = [
            $booking->id,
            $booking->customer_id,
            $booking->barber_id,
            $booking->total_amount,
            $booking->scheduled_date->toISOString(),
            serialize($booking->services->pluck('id')->toArray())
        ];

        return hash('sha256', implode('|', $data));
    }

    /**
     * Get user wallet address (create if not exists)
     */
    protected function getUserWalletAddress(User $user): string
    {
        if (!$user->blockchain_wallet_address) {
            // Create new wallet for user
            $walletResponse = $this->callBlockchainAPI('wallets/create', [
                'user_id' => $user->id,
                'user_type' => $user->user_type ?? 'customer'
            ]);

            if ($walletResponse['success']) {
                $user->update([
                    'blockchain_wallet_address' => $walletResponse['wallet_address'],
                    'wallet_private_key' => encrypt($walletResponse['private_key'])
                ]);

                return $walletResponse['wallet_address'];
            }

            // Fallback to default wallet
            return $this->walletAddress;
        }

        return $user->blockchain_wallet_address;
    }

    /**
     * Generate service terms for smart contract
     */
    protected function generateServiceTerms(Booking $booking): array
    {
        return [
            'service_completion_required' => true,
            'customer_satisfaction_threshold' => 3.0,
            'cancellation_policy' => '24_hours_notice',
            'refund_policy' => 'full_refund_if_cancelled_24h_before',
            'dispute_resolution' => 'platform_mediation',
            'payment_release_conditions' => [
                'service_marked_complete',
                'no_disputes_within_24h',
                'minimum_rating_threshold_met'
            ]
        ];
    }

    /**
     * Verify smart contract conditions are met
     */
    protected function verifyContractConditions(Booking $booking): array
    {
        return [
            'service_completed' => $booking->status === 'completed',
            'payment_confirmed' => $booking->payment && $booking->payment->status === 'completed',
            'no_active_disputes' => !$booking->disputes()->where('status', 'active')->exists(),
            'minimum_rating' => !$booking->review || $booking->review->rating >= 3.0,
            'time_elapsed' => $booking->completed_at && $booking->completed_at->diffInHours(now()) >= 24
        ];
    }

    /**
     * Get blockchain network status
     */
    public function getNetworkStatus(): array
    {
        try {
            $response = $this->callBlockchainAPI('network/status', []);

            if ($response['success']) {
                return [
                    'network_health' => $response['health'],
                    'block_height' => $response['block_height'],
                    'gas_price' => $response['gas_price'],
                    'transaction_pool_size' => $response['tx_pool_size'],
                    'network_hashrate' => $response['hashrate'],
                    'active_nodes' => $response['active_nodes']
                ];
            }

            return ['error' => $response['error'] ?? 'Network status unavailable'];

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Estimate transaction fees
     */
    public function estimateTransactionFee(string $transactionType, array $data = []): array
    {
        try {
            $response = $this->callBlockchainAPI('fees/estimate', [
                'transaction_type' => $transactionType,
                'data_size' => strlen(json_encode($data)),
                'priority' => 'normal'
            ]);

            if ($response['success']) {
                return [
                    'estimated_fee' => $response['fee'],
                    'currency' => $response['currency'],
                    'confirmation_time' => $response['confirmation_time'],
                    'fee_breakdown' => $response['breakdown']
                ];
            }

            return ['error' => $response['error'] ?? 'Fee estimation failed'];

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Batch process multiple blockchain operations
     */
    public function batchProcess(array $operations): array
    {
        try {
            $response = $this->callBlockchainAPI('batch/process', [
                'operations' => $operations,
                'batch_id' => uniqid('batch_', true)
            ]);

            if ($response['success']) {
                return [
                    'success' => true,
                    'batch_id' => $response['batch_id'],
                    'processed_count' => $response['processed_count'],
                    'failed_count' => $response['failed_count'],
                    'results' => $response['results']
                ];
            }

            return ['success' => false, 'error' => $response['error'] ?? 'Batch processing failed'];

        } catch (\Exception $e) {
            Log::error('Blockchain batch processing failed', [
                'operations_count' => count($operations),
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Enable/disable blockchain features
     */
    public function isBlockchainEnabled(): bool
    {
        return config('services.blockchain.enabled', false) && 
               !empty($this->blockchainApiUrl) && 
               !empty($this->blockchainApiKey);
    }

    /**
     * Get blockchain configuration
     */
    public function getBlockchainConfig(): array
    {
        return [
            'enabled' => $this->isBlockchainEnabled(),
            'network' => config('services.blockchain.network', 'testnet'),
            'contract_address' => $this->contractAddress,
            'features' => [
                'smart_contracts' => config('services.blockchain.features.smart_contracts', true),
                'loyalty_tokens' => config('services.blockchain.features.loyalty_tokens', true),
                'immutable_reviews' => config('services.blockchain.features.immutable_reviews', true),
                'payment_escrow' => config('services.blockchain.features.payment_escrow', true)
            ]
        ];
    }
}
