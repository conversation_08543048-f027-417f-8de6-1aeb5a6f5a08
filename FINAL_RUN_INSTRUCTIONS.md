# 🚀 **تعليمات التشغيل النهائية - مشروع "حلاق على بابك"**

---

## ⚡ **التشغيل الفوري (أسرع طريقة)**

### 🖥️ **Windows**
1. **افتح Command Prompt أو PowerShell**
2. **انتقل لمجلد المشروع**
3. **شغل الأمر:**
```bash
start.bat
```

### 🐧 **Linux/Mac**
1. **افتح Terminal**
2. **انتقل لمجلد المشروع**
3. **شغل الأوامر:**
```bash
chmod +x start.sh
./start.sh
```

---

## 📋 **قبل التشغيل - تأكد من وجود:**

### ✅ **البرامج المطلوبة:**
- **PHP 8.1+** - [تحميل](https://www.php.net/downloads)
- **Composer** - [تحميل](https://getcomposer.org/download/)
- **Node.js 18+** - [تحميل](https://nodejs.org/)
- **MySQL 8.0+** - [تحميل](https://dev.mysql.com/downloads/)

### 🔧 **تثبيت سريع للمتطلبات:**

#### Windows (XAMPP):
```bash
# تحميل وتثبيت XAMPP (يحتوي على PHP + MySQL)
# من: https://www.apachefriends.org/

# تحميل وتثبيت Node.js
# من: https://nodejs.org/

# تحميل وتثبيت Composer
# من: https://getcomposer.org/
```

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install php8.1 php8.1-mysql php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip
sudo apt install mysql-server nodejs npm composer
```

#### macOS:
```bash
# تثبيت Homebrew أولاً
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# تثبيت المتطلبات
brew install php@8.1 mysql node composer
```

---

## 🗄️ **إعداد قاعدة البيانات (مهم جداً)**

### 1️⃣ **تشغيل MySQL**
```bash
# Windows (XAMPP): تشغيل XAMPP Control Panel وتفعيل MySQL
# Linux: sudo systemctl start mysql
# macOS: brew services start mysql
```

### 2️⃣ **إنشاء قاعدة البيانات**
```bash
# الطريقة الأولى: استخدام السكريبت الجاهز (الأسهل)
mysql -u root -p < setup-database.sql

# الطريقة الثانية: يدوياً
mysql -u root -p
CREATE DATABASE barber_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
exit
```

---

## 🎯 **خطوات التشغيل اليدوي (إذا لم تعمل السكريبتات)**

### 1️⃣ **Backend (Laravel)**
```bash
# الانتقال لمجلد Backend
cd backend

# تثبيت التبعيات
composer install

# إعداد البيئة
cp .env.example .env

# توليد مفتاح التطبيق
php artisan key:generate

# تحديث إعدادات قاعدة البيانات في .env
# DB_DATABASE=barber_app
# DB_USERNAME=root
# DB_PASSWORD=your_password

# تشغيل المايجريشن
php artisan migrate --seed

# إنشاء رابط التخزين
php artisan storage:link

# تشغيل الخادم
php artisan serve --port=8000
```

### 2️⃣ **Admin Dashboard (Vue.js)**
```bash
# فتح terminal جديد والانتقال للمجلد
cd admin-dashboard

# تثبيت التبعيات
npm install

# إعداد البيئة
cp .env.example .env

# تشغيل خادم التطوير
npm run dev
```

---

## 🌐 **الوصول للنظام بعد التشغيل**

### 🔗 **الروابط:**
- **🖥️ لوحة الإدارة**: http://localhost:3000
- **📡 Backend API**: http://localhost:8000

### 🔐 **تسجيل الدخول:**
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123

---

## 🔍 **حل المشاكل الشائعة**

### ❌ **خطأ: "Class not found"**
```bash
cd backend
composer dump-autoload
php artisan config:clear
php artisan cache:clear
```

### ❌ **خطأ: "Permission denied"**
```bash
# Linux/Mac
sudo chown -R $USER:$USER backend/storage backend/bootstrap/cache
chmod -R 775 backend/storage backend/bootstrap/cache

# Windows (تشغيل CMD كمدير)
icacls backend\storage /grant Everyone:F /T
icacls backend\bootstrap\cache /grant Everyone:F /T
```

### ❌ **خطأ: "Port already in use"**
```bash
# تغيير المنفذ
php artisan serve --port=8001  # للـ backend
npm run dev -- --port 3001     # للـ admin dashboard
```

### ❌ **خطأ: "Database connection failed"**
```bash
# تحقق من إعدادات .env في مجلد backend
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=barber_app
DB_USERNAME=root
DB_PASSWORD=your_password

# إعادة تشغيل MySQL
sudo systemctl restart mysql  # Linux
brew services restart mysql   # macOS
```

### ❌ **خطأ: "npm install failed"**
```bash
cd admin-dashboard
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

---

## ✅ **علامات النجاح**

### 🎉 **إذا رأيت هذه الرسائل فالنظام يعمل:**
- **Backend**: "Laravel development server started: http://127.0.0.1:8000"
- **Admin Dashboard**: "Local: http://localhost:3000/"
- **لوحة الإدارة تفتح في المتصفح** وتظهر صفحة تسجيل الدخول

---

## 🎯 **الخطوات التالية بعد التشغيل الناجح**

### 1️⃣ **تسجيل الدخول للوحة الإدارة**
- اذهب إلى http://localhost:3000
- سجل دخول بالبيانات المذكورة أعلاه

### 2️⃣ **إضافة البيانات الأساسية**
- أضف مدن ومناطق جديدة
- أضف خدمات إضافية
- أنشئ حسابات حلاقين تجريبية

### 3️⃣ **اختبار النظام**
- اختبر عملية إنشاء حجز
- اختبر النظام بشكل عام

---

## 📞 **للمساعدة الإضافية**

### 📚 **ملفات مفيدة:**
- **START_HERE.md** - دليل مبسط
- **QUICK_START_GUIDE.md** - دليل شامل
- **RUN_PROJECT.md** - تعليمات مفصلة

### 🆘 **إذا واجهت مشاكل:**
1. تأكد من تشغيل MySQL
2. تأكد من إنشاء قاعدة البيانات
3. تحقق من ملفات .env
4. راجع ملفات اللوج للأخطاء

---

## 🎉 **مبروك!**

**إذا وصلت هنا ورأيت لوحة الإدارة تعمل، فقد نجحت في تشغيل المشروع! 🚀**

**الآن يمكنك البدء في استكشاف جميع الميزات المتقدمة للنظام.**

---

**💡 نصيحة أخيرة**: احفظ هذا الملف كمرجع سريع للتشغيل في المستقبل!
