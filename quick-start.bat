@echo off
chcp 65001 >nul
title مشروع حلاق على بابك - تشغيل سريع

echo.
echo ========================================
echo 🚀 مرحباً بك في مشروع "حلاق على بابك"
echo ========================================
echo.

echo 📋 التحقق من المتطلبات...

:: التحقق من PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ PHP غير مثبت!
    echo.
    echo 📥 يرجى تثبيت XAMPP أولاً من:
    echo https://www.apachefriends.org/download.html
    echo.
    echo 📖 راجع ملف SIMPLE_START_GUIDE.md للتفاصيل
    echo.
    pause
    exit /b 1
)
echo ✅ PHP مثبت

:: التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ Node.js غير مثبت!
    echo.
    echo 📥 يرجى تثبيت Node.js من:
    echo https://nodejs.org/
    echo.
    echo 📖 راجع ملف SIMPLE_START_GUIDE.md للتفاصيل
    echo.
    pause
    exit /b 1
)
echo ✅ Node.js مثبت

:: التحقق من Composer
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ Composer غير مثبت!
    echo.
    echo 📖 راجع ملف SIMPLE_START_GUIDE.md لتثبيت Composer
    echo.
    pause
    exit /b 1
)
echo ✅ Composer مثبت

echo.
echo 🗄️ التحقق من قاعدة البيانات...

:: التحقق من MySQL
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ⚠️ MySQL غير متاح من Command Line
    echo 💡 تأكد من تشغيل MySQL في XAMPP Control Panel
    echo.
)

echo.
echo 🏗️ إعداد Backend (Laravel)...

:: الانتقال إلى مجلد Backend
if not exist "backend" (
    echo ❌ مجلد backend غير موجود
    pause
    exit /b 1
)

cd backend

:: تثبيت تبعيات Laravel
echo 📦 تثبيت تبعيات Laravel...
composer install --no-dev --optimize-autoloader --quiet

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت تبعيات Laravel
    echo 💡 تأكد من اتصال الإنترنت
    pause
    exit /b 1
)

:: التحقق من وجود ملف .env
if not exist ".env" (
    echo 📝 إنشاء ملف .env...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
    )
)

:: توليد مفتاح التطبيق
echo 🔑 توليد مفتاح التطبيق...
php artisan key:generate --force --quiet

:: إنشاء رابط التخزين
echo 🔗 إنشاء رابط التخزين...
php artisan storage:link --quiet

:: إنشاء مجلدات التخزين
if not exist "storage\app\public\avatars" mkdir storage\app\public\avatars
if not exist "storage\app\public\services" mkdir storage\app\public\services
if not exist "storage\app\public\uploads" mkdir storage\app\public\uploads

echo.
echo 🚀 تشغيل خادم Laravel...
echo 📡 Backend سيعمل على: http://localhost:8000
echo.

:: تشغيل خادم Laravel في الخلفية
start "Laravel Server" cmd /k "php artisan serve --port=8000"

:: انتظار قصير
timeout /t 3 /nobreak >nul

:: العودة للمجلد الرئيسي
cd ..

echo.
echo 🖥️ إعداد لوحة الإدارة...

:: التحقق من مجلد لوحة الإدارة
if not exist "admin-dashboard" (
    echo ❌ مجلد admin-dashboard غير موجود
    pause
    exit /b 1
)

cd admin-dashboard

:: التحقق من وجود ملف .env
if not exist ".env" (
    echo 📝 إنشاء ملف .env للوحة الإدارة...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
    )
)

:: تثبيت تبعيات Node.js
echo 📦 تثبيت تبعيات لوحة الإدارة...
npm install --silent

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت تبعيات لوحة الإدارة
    echo 💡 تأكد من اتصال الإنترنت
    pause
    exit /b 1
)

echo.
echo 🚀 تشغيل لوحة الإدارة...
echo 🖥️ لوحة الإدارة ستعمل على: http://localhost:3000
echo.

:: تشغيل خادم التطوير في الخلفية
start "Admin Dashboard" cmd /k "npm run dev"

:: العودة للمجلد الرئيسي
cd ..

echo.
echo ✅ تم تشغيل جميع الخدمات بنجاح!
echo.
echo 🌐 الروابط المتاحة:
echo ========================================
echo 📡 Backend API: http://localhost:8000
echo 🖥️ لوحة الإدارة: http://localhost:3000
echo ========================================
echo.
echo 🔐 بيانات تسجيل الدخول للوحة الإدارة:
echo البريد الإلكتروني: <EMAIL>
echo كلمة المرور: admin123
echo.
echo 💡 نصائح مهمة:
echo - تأكد من تشغيل MySQL في XAMPP Control Panel
echo - اذهب إلى http://localhost/phpmyadmin وأنشئ قاعدة بيانات 'barber_app'
echo - إذا لم تعمل لوحة الإدارة، انتظر قليلاً حتى يكتمل التحميل
echo.

:: انتظار لمدة 5 ثوانٍ ثم فتح المتصفح
echo ⏳ سيتم فتح لوحة الإدارة في المتصفح خلال 5 ثوانٍ...
timeout /t 5 /nobreak >nul

:: فتح لوحة الإدارة في المتصفح
start http://localhost:3000

echo.
echo 🎉 مبروك! مشروع "حلاق على بابك" جاهز للاستخدام
echo.
echo 📖 للمساعدة الإضافية، راجع:
echo - SIMPLE_START_GUIDE.md
echo - QUICK_START_GUIDE.md
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
