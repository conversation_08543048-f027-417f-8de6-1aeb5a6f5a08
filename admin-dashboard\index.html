<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>لوحة التحكم - حلاق على بابك</title>
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Material Design Icons -->
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css" rel="stylesheet">
    
    <style>
      /* Base RTL styles */
      * {
        font-family: 'Cairo', sans-serif;
      }
      
      body {
        margin: 0;
        padding: 0;
        direction: rtl;
        text-align: right;
      }
      
      /* Loading spinner */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 18px;
        font-weight: 500;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading when app is ready */
      .app-ready .loading-container {
        display: none;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div class="loading-container">
      <div style="text-align: center;">
        <div class="loading-spinner"></div>
        <div class="loading-text">جاري تحميل لوحة التحكم...</div>
      </div>
    </div>
    
    <!-- Vue App -->
    <div id="app"></div>
    
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
