<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Barber;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Booking>
 */
class BookingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $scheduledDate = $this->faker->dateTimeBetween('now', '+30 days');
        $totalAmount = $this->faker->randomFloat(2, 50, 300);
        $commissionRate = 15; // 15%
        $commissionAmount = ($totalAmount * $commissionRate) / 100;
        $barberAmount = $totalAmount - $commissionAmount;

        $addresses = [
            'شارع التحرير، وسط البلد، القاهرة',
            'شارع الهرم، الجيزة',
            'كورنيش النيل، المعادي، القاهرة',
            'شارع مصطفى النحاس، مدينة نصر، القاهرة',
            'شارع العروبة، مصر الجديدة، القاهرة',
            'شارع الثورة، الإسكندرية',
            'شارع الجمهورية، وسط البلد، القاهرة',
            'شارع 26 يوليو، الزمالك، القاهرة',
            'شارع الملك فيصل، الجيزة',
            'شارع أحمد عرابي، المهندسين، الجيزة'
        ];

        return [
            'booking_number' => 'BK' . date('Ymd', $scheduledDate->getTimestamp()) . $this->faker->unique()->numberBetween(1000, 9999),
            'customer_id' => User::factory(),
            'barber_id' => Barber::factory(),
            'scheduled_date' => $scheduledDate->format('Y-m-d'),
            'scheduled_time' => $this->faker->randomElement([
                '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
                '12:00', '12:30', '14:00', '14:30', '15:00', '15:30',
                '16:00', '16:30', '17:00', '17:30', '18:00', '18:30',
                '19:00', '19:30', '20:00', '20:30', '21:00'
            ]),
            'status' => $this->faker->randomElement(['pending', 'confirmed', 'in_progress', 'completed', 'cancelled']),
            'total_amount' => $totalAmount,
            'commission_amount' => $commissionAmount,
            'barber_amount' => $barberAmount,
            'discount_amount' => 0.00,
            'customer_address' => $this->faker->randomElement($addresses),
            'customer_latitude' => $this->faker->latitude(29.5, 31.5), // Egypt coordinates
            'customer_longitude' => $this->faker->longitude(29.0, 33.0), // Egypt coordinates
            'notes' => $this->faker->optional(0.7)->sentence(),
            'cancellation_reason' => null,
            'cancelled_at' => null,
            'confirmed_at' => null,
            'started_at' => null,
            'completed_at' => null,
            'is_instant_booking' => $this->faker->boolean(20), // 20% chance
            'vip_discount_applied' => $this->faker->boolean(10), // 10% chance
        ];
    }

    /**
     * Indicate that the booking is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'confirmed_at' => null,
            'started_at' => null,
            'completed_at' => null,
        ]);
    }

    /**
     * Indicate that the booking is confirmed.
     */
    public function confirmed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'confirmed',
            'confirmed_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
            'started_at' => null,
            'completed_at' => null,
        ]);
    }

    /**
     * Indicate that the booking is in progress.
     */
    public function inProgress(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'in_progress',
            'confirmed_at' => $this->faker->dateTimeBetween('-7 days', '-1 hour'),
            'started_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'completed_at' => null,
        ]);
    }

    /**
     * Indicate that the booking is completed.
     */
    public function completed(): static
    {
        $confirmedAt = $this->faker->dateTimeBetween('-30 days', '-2 hours');
        $startedAt = $this->faker->dateTimeBetween($confirmedAt, '-1 hour');
        $completedAt = $this->faker->dateTimeBetween($startedAt, 'now');

        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'confirmed_at' => $confirmedAt,
            'started_at' => $startedAt,
            'completed_at' => $completedAt,
        ]);
    }

    /**
     * Indicate that the booking is cancelled.
     */
    public function cancelled(): static
    {
        $cancelledAt = $this->faker->dateTimeBetween('-30 days', 'now');
        $cancellationReasons = [
            'تغيير في الخطط',
            'ظروف طارئة',
            'عدم توفر الوقت',
            'مرض',
            'سفر مفاجئ',
            'تأجيل لموعد آخر'
        ];

        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'cancellation_reason' => $this->faker->randomElement($cancellationReasons),
            'cancelled_at' => $cancelledAt,
        ]);
    }

    /**
     * Indicate that the booking is a no-show.
     */
    public function noShow(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'no_show',
            'confirmed_at' => $this->faker->dateTimeBetween('-7 days', '-2 hours'),
        ]);
    }

    /**
     * Indicate that the booking is an instant booking.
     */
    public function instant(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_instant_booking' => true,
            'status' => 'confirmed',
            'confirmed_at' => now(),
        ]);
    }

    /**
     * Indicate that VIP discount is applied.
     */
    public function withVipDiscount(): static
    {
        return $this->state(function (array $attributes) {
            $discountPercentage = $this->faker->randomElement([10, 15, 20, 25]);
            $discountAmount = ($attributes['total_amount'] * $discountPercentage) / 100;
            $newTotal = $attributes['total_amount'] - $discountAmount;
            $commissionAmount = ($newTotal * 15) / 100;
            $barberAmount = $newTotal - $commissionAmount;

            return [
                'vip_discount_applied' => true,
                'discount_amount' => $discountAmount,
                'total_amount' => $newTotal,
                'commission_amount' => $commissionAmount,
                'barber_amount' => $barberAmount,
            ];
        });
    }

    /**
     * Indicate that the booking is for today.
     */
    public function today(): static
    {
        return $this->state(fn (array $attributes) => [
            'scheduled_date' => now()->format('Y-m-d'),
            'scheduled_time' => $this->faker->randomElement([
                '14:00', '14:30', '15:00', '15:30', '16:00', '16:30',
                '17:00', '17:30', '18:00', '18:30', '19:00', '19:30'
            ]),
        ]);
    }

    /**
     * Indicate that the booking is for tomorrow.
     */
    public function tomorrow(): static
    {
        return $this->state(fn (array $attributes) => [
            'scheduled_date' => now()->addDay()->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the booking is for this week.
     */
    public function thisWeek(): static
    {
        return $this->state(fn (array $attributes) => [
            'scheduled_date' => $this->faker->dateTimeBetween('now', '+7 days')->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the booking has notes.
     */
    public function withNotes(): static
    {
        $notes = [
            'يرجى الالتزام بالموعد المحدد',
            'أفضل قص شعر قصير',
            'لدي حساسية من بعض المنتجات',
            'أريد نفس القصة السابقة',
            'يرجى إحضار أدوات نظيفة',
            'أفضل الحلاقة بالموس',
            'لا أريد استخدام الجل',
            'يرجى التأكد من نظافة الأدوات'
        ];

        return $this->state(fn (array $attributes) => [
            'notes' => $this->faker->randomElement($notes),
        ]);
    }

    /**
     * Indicate that the booking is high value.
     */
    public function highValue(): static
    {
        return $this->state(function (array $attributes) {
            $totalAmount = $this->faker->randomFloat(2, 200, 500);
            $commissionAmount = ($totalAmount * 15) / 100;
            $barberAmount = $totalAmount - $commissionAmount;

            return [
                'total_amount' => $totalAmount,
                'commission_amount' => $commissionAmount,
                'barber_amount' => $barberAmount,
            ];
        });
    }

    /**
     * Indicate that the booking is low value.
     */
    public function lowValue(): static
    {
        return $this->state(function (array $attributes) {
            $totalAmount = $this->faker->randomFloat(2, 30, 80);
            $commissionAmount = ($totalAmount * 15) / 100;
            $barberAmount = $totalAmount - $commissionAmount;

            return [
                'total_amount' => $totalAmount,
                'commission_amount' => $commissionAmount,
                'barber_amount' => $barberAmount,
            ];
        });
    }
}
