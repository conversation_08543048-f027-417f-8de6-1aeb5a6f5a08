import 'package:json_annotation/json_annotation.dart';

part 'booking_model.g.dart';

@JsonSerializable()
class BookingModel {
  final String id;
  final String customerId;
  final String customerName;
  final String? customerAvatar;
  final String customerPhone;
  final String customerAddress;
  final double? customerRating;
  final bool isVipCustomer;
  
  final String barberId;
  final String serviceId;
  final String serviceName;
  final String? serviceDescription;
  final double servicePrice;
  final int serviceDuration; // in minutes
  final String? serviceNotes;
  
  final DateTime requestedDate;
  final DateTime requestedTime;
  final DateTime createdAt;
  final DateTime? updatedAt;
  
  final String status; // pending, accepted, rejected, in_progress, completed, cancelled
  final String? rejectionReason;
  final String? cancellationReason;
  
  final double? distanceFromBarber;
  final double? latitude;
  final double? longitude;
  
  final String paymentMethod; // cash, online, wallet
  final String paymentStatus; // pending, paid, refunded
  final double? tip;
  final double? totalAmount;
  
  final double? rating;
  final String? review;
  final DateTime? ratingDate;
  
  final bool isEmergency;
  final double? emergencyFee;

  BookingModel({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.customerAvatar,
    required this.customerPhone,
    required this.customerAddress,
    this.customerRating,
    this.isVipCustomer = false,
    required this.barberId,
    required this.serviceId,
    required this.serviceName,
    this.serviceDescription,
    required this.servicePrice,
    required this.serviceDuration,
    this.serviceNotes,
    required this.requestedDate,
    required this.requestedTime,
    required this.createdAt,
    this.updatedAt,
    required this.status,
    this.rejectionReason,
    this.cancellationReason,
    this.distanceFromBarber,
    this.latitude,
    this.longitude,
    required this.paymentMethod,
    required this.paymentStatus,
    this.tip,
    this.totalAmount,
    this.rating,
    this.review,
    this.ratingDate,
    this.isEmergency = false,
    this.emergencyFee,
  });

  factory BookingModel.fromJson(Map<String, dynamic> json) =>
      _$BookingModelFromJson(json);

  Map<String, dynamic> toJson() => _$BookingModelToJson(this);

  BookingModel copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? customerAvatar,
    String? customerPhone,
    String? customerAddress,
    double? customerRating,
    bool? isVipCustomer,
    String? barberId,
    String? serviceId,
    String? serviceName,
    String? serviceDescription,
    double? servicePrice,
    int? serviceDuration,
    String? serviceNotes,
    DateTime? requestedDate,
    DateTime? requestedTime,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? status,
    String? rejectionReason,
    String? cancellationReason,
    double? distanceFromBarber,
    double? latitude,
    double? longitude,
    String? paymentMethod,
    String? paymentStatus,
    double? tip,
    double? totalAmount,
    double? rating,
    String? review,
    DateTime? ratingDate,
    bool? isEmergency,
    double? emergencyFee,
  }) {
    return BookingModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerAvatar: customerAvatar ?? this.customerAvatar,
      customerPhone: customerPhone ?? this.customerPhone,
      customerAddress: customerAddress ?? this.customerAddress,
      customerRating: customerRating ?? this.customerRating,
      isVipCustomer: isVipCustomer ?? this.isVipCustomer,
      barberId: barberId ?? this.barberId,
      serviceId: serviceId ?? this.serviceId,
      serviceName: serviceName ?? this.serviceName,
      serviceDescription: serviceDescription ?? this.serviceDescription,
      servicePrice: servicePrice ?? this.servicePrice,
      serviceDuration: serviceDuration ?? this.serviceDuration,
      serviceNotes: serviceNotes ?? this.serviceNotes,
      requestedDate: requestedDate ?? this.requestedDate,
      requestedTime: requestedTime ?? this.requestedTime,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      distanceFromBarber: distanceFromBarber ?? this.distanceFromBarber,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      tip: tip ?? this.tip,
      totalAmount: totalAmount ?? this.totalAmount,
      rating: rating ?? this.rating,
      review: review ?? this.review,
      ratingDate: ratingDate ?? this.ratingDate,
      isEmergency: isEmergency ?? this.isEmergency,
      emergencyFee: emergencyFee ?? this.emergencyFee,
    );
  }

  bool get isPending => status == 'pending';
  bool get isAccepted => status == 'accepted';
  bool get isRejected => status == 'rejected';
  bool get isInProgress => status == 'in_progress';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';

  bool get canAccept => isPending;
  bool get canReject => isPending;
  bool get canStart => isAccepted;
  bool get canComplete => isInProgress;
  bool get canCancel => isPending || isAccepted;

  String get formattedDateTime {
    final date = requestedDate;
    final time = requestedTime;
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final bookingDate = DateTime(date.year, date.month, date.day);
    
    String dateStr;
    if (bookingDate == today) {
      dateStr = 'اليوم';
    } else if (bookingDate == tomorrow) {
      dateStr = 'غداً';
    } else {
      dateStr = '${date.day}/${date.month}/${date.year}';
    }
    
    final hour = time.hour;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    
    return '$dateStr - $displayHour:$minute $period';
  }

  String get statusText {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'في الانتظار';
      case 'accepted':
        return 'مقبول';
      case 'rejected':
        return 'مرفوض';
      case 'in_progress':
        return 'جاري التنفيذ';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  String get paymentMethodText {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return 'نقداً';
      case 'online':
        return 'دفع إلكتروني';
      case 'wallet':
        return 'محفظة إلكترونية';
      default:
        return paymentMethod;
    }
  }

  String get paymentStatusText {
    switch (paymentStatus.toLowerCase()) {
      case 'pending':
        return 'في الانتظار';
      case 'paid':
        return 'مدفوع';
      case 'refunded':
        return 'مسترد';
      default:
        return paymentStatus;
    }
  }

  double get finalAmount {
    double amount = servicePrice;
    
    if (emergencyFee != null) {
      amount += emergencyFee!;
    }
    
    if (tip != null) {
      amount += tip!;
    }
    
    return amount;
  }

  Duration get estimatedDuration {
    return Duration(minutes: serviceDuration);
  }

  DateTime get estimatedEndTime {
    return requestedTime.add(estimatedDuration);
  }

  bool get isToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final bookingDate = DateTime(requestedDate.year, requestedDate.month, requestedDate.day);
    
    return bookingDate == today;
  }

  bool get isTomorrow {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day).add(const Duration(days: 1));
    final bookingDate = DateTime(requestedDate.year, requestedDate.month, requestedDate.day);
    
    return bookingDate == tomorrow;
  }

  bool get isUpcoming {
    final now = DateTime.now();
    final bookingDateTime = DateTime(
      requestedDate.year,
      requestedDate.month,
      requestedDate.day,
      requestedTime.hour,
      requestedTime.minute,
    );
    
    return bookingDateTime.isAfter(now);
  }

  bool get isOverdue {
    final now = DateTime.now();
    final bookingDateTime = DateTime(
      requestedDate.year,
      requestedDate.month,
      requestedDate.day,
      requestedTime.hour,
      requestedTime.minute,
    );
    
    return bookingDateTime.isBefore(now) && (isPending || isAccepted);
  }
}
