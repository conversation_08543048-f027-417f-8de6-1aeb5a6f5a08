import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF667eea);
  static const Color primaryDark = Color(0xFF5a6fd8);
  static const Color primaryLight = Color(0xFF7a8ef0);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF764ba2);
  static const Color secondaryDark = Color(0xFF6a4190);
  static const Color secondaryLight = Color(0xFF8355b4);
  
  // Accent Colors
  static const Color accent = Color(0xFFf093fb);
  static const Color accentDark = Color(0xFFee82f0);
  static const Color accentLight = Color(0xFFf2a4fc);
  
  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF9E9E9E);
  static const Color greyLight = Color(0xFFF5F5F5);
  static const Color greyDark = Color(0xFF424242);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF2c3e50);
  static const Color textSecondary = Color(0xFF7f8c8d);
  static const Color textLight = Color(0xFFbdc3c7);
  static const Color textWhite = Color(0xFFFFFFFF);
  
  // Background Colors
  static const Color background = Color(0xFFF8F9FA);
  static const Color backgroundDark = Color(0xFF1a1a1a);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF2c2c2c);
  
  // Status Colors
  static const Color success = Color(0xFF27ae60);
  static const Color successLight = Color(0xFF2ecc71);
  static const Color successDark = Color(0xFF229954);
  
  static const Color error = Color(0xFFe74c3c);
  static const Color errorLight = Color(0xFFec7063);
  static const Color errorDark = Color(0xFFc0392b);
  
  static const Color warning = Color(0xFFf39c12);
  static const Color warningLight = Color(0xFFf4d03f);
  static const Color warningDark = Color(0xFFd68910);
  
  static const Color info = Color(0xFF3498db);
  static const Color infoLight = Color(0xFF5dade2);
  static const Color infoDark = Color(0xFF2980b9);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderLight = Color(0xFFF0F0F0);
  static const Color borderDark = Color(0xFFBDBDBD);
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x33000000);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, secondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, successLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient errorGradient = LinearGradient(
    colors: [error, errorLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient warningGradient = LinearGradient(
    colors: [warning, warningLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient infoGradient = LinearGradient(
    colors: [info, infoLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Special Colors
  static const Color vip = Color(0xFFFFD700);
  static const Color vipDark = Color(0xFFDAA520);
  static const Color vipLight = Color(0xFFFFF8DC);
  
  static const Color rating = Color(0xFFFFB300);
  static const Color ratingLight = Color(0xFFFFC947);
  static const Color ratingDark = Color(0xFFFF8F00);
  
  static const Color online = Color(0xFF4CAF50);
  static const Color offline = Color(0xFF9E9E9E);
  static const Color busy = Color(0xFFFF9800);
  
  // Booking Status Colors
  static const Color bookingPending = Color(0xFFFF9800);
  static const Color bookingConfirmed = Color(0xFF2196F3);
  static const Color bookingInProgress = Color(0xFF9C27B0);
  static const Color bookingCompleted = Color(0xFF4CAF50);
  static const Color bookingCancelled = Color(0xFFF44336);
  
  // Payment Status Colors
  static const Color paymentPending = Color(0xFFFF9800);
  static const Color paymentPaid = Color(0xFF4CAF50);
  static const Color paymentRefunded = Color(0xFFF44336);
  
  // Service Category Colors
  static const Color categoryHaircut = Color(0xFF3F51B5);
  static const Color categoryBeard = Color(0xFF795548);
  static const Color categorySkincare = Color(0xFFE91E63);
  static const Color categoryPackage = Color(0xFF9C27B0);
  
  // Social Media Colors
  static const Color facebook = Color(0xFF1877F2);
  static const Color instagram = Color(0xFFE4405F);
  static const Color twitter = Color(0xFF1DA1F2);
  static const Color whatsapp = Color(0xFF25D366);
  
  // Utility Methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }
  
  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }
  
  static MaterialColor createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map<int, Color> swatch = {};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    
    return MaterialColor(color.value, swatch);
  }
  
  // Color Schemes
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primary,
    onPrimary: white,
    secondary: secondary,
    onSecondary: white,
    error: error,
    onError: white,
    background: background,
    onBackground: textPrimary,
    surface: surface,
    onSurface: textPrimary,
  );
  
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: primaryLight,
    onPrimary: black,
    secondary: secondaryLight,
    onSecondary: black,
    error: errorLight,
    onError: black,
    background: backgroundDark,
    onBackground: white,
    surface: surfaceDark,
    onSurface: white,
  );
}
