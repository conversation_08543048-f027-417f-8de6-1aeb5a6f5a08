<?php

namespace App\Services;

use App\Models\User;
use App\Models\Barber;
use App\Models\Booking;
use App\Models\Payment;
use App\Models\Review;
use App\Models\Service;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class BusinessIntelligenceService
{
    protected $cachePrefix = 'bi:';
    protected $cacheTtl = 3600; // 1 hour

    /**
     * Get comprehensive business dashboard data
     */
    public function getDashboardData(array $dateRange = []): array
    {
        $startDate = $dateRange['start'] ?? now()->subDays(30);
        $endDate = $dateRange['end'] ?? now();

        return [
            'overview' => $this->getOverviewMetrics($startDate, $endDate),
            'revenue' => $this->getRevenueAnalytics($startDate, $endDate),
            'customers' => $this->getCustomerAnalytics($startDate, $endDate),
            'barbers' => $this->getBarberAnalytics($startDate, $endDate),
            'services' => $this->getServiceAnalytics($startDate, $endDate),
            'geographic' => $this->getGeographicAnalytics($startDate, $endDate),
            'trends' => $this->getTrendAnalytics($startDate, $endDate),
            'predictions' => $this->getPredictiveAnalytics($startDate, $endDate)
        ];
    }

    /**
     * Get overview metrics
     */
    public function getOverviewMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $cacheKey = $this->cachePrefix . "overview:" . $startDate->format('Y-m-d') . ":" . $endDate->format('Y-m-d');
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($startDate, $endDate) {
            $previousPeriod = $startDate->diffInDays($endDate);
            $previousStart = $startDate->copy()->subDays($previousPeriod);
            $previousEnd = $startDate->copy()->subDay();

            // Current period metrics
            $currentBookings = Booking::whereBetween('created_at', [$startDate, $endDate])->count();
            $currentRevenue = Payment::where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount');
            $currentCustomers = User::whereBetween('created_at', [$startDate, $endDate])->count();
            $currentBarbers = Barber::whereBetween('created_at', [$startDate, $endDate])->count();

            // Previous period metrics
            $previousBookings = Booking::whereBetween('created_at', [$previousStart, $previousEnd])->count();
            $previousRevenue = Payment::where('status', 'completed')
                ->whereBetween('created_at', [$previousStart, $previousEnd])
                ->sum('amount');
            $previousCustomers = User::whereBetween('created_at', [$previousStart, $previousEnd])->count();
            $previousBarbers = Barber::whereBetween('created_at', [$previousStart, $previousEnd])->count();

            return [
                'total_bookings' => [
                    'current' => $currentBookings,
                    'previous' => $previousBookings,
                    'growth' => $this->calculateGrowthRate($currentBookings, $previousBookings)
                ],
                'total_revenue' => [
                    'current' => $currentRevenue,
                    'previous' => $previousRevenue,
                    'growth' => $this->calculateGrowthRate($currentRevenue, $previousRevenue)
                ],
                'new_customers' => [
                    'current' => $currentCustomers,
                    'previous' => $previousCustomers,
                    'growth' => $this->calculateGrowthRate($currentCustomers, $previousCustomers)
                ],
                'new_barbers' => [
                    'current' => $currentBarbers,
                    'previous' => $previousBarbers,
                    'growth' => $this->calculateGrowthRate($currentBarbers, $previousBarbers)
                ],
                'avg_booking_value' => [
                    'current' => $currentBookings > 0 ? $currentRevenue / $currentBookings : 0,
                    'previous' => $previousBookings > 0 ? $previousRevenue / $previousBookings : 0
                ],
                'completion_rate' => $this->getCompletionRate($startDate, $endDate),
                'customer_satisfaction' => $this->getCustomerSatisfaction($startDate, $endDate)
            ];
        });
    }

    /**
     * Get revenue analytics
     */
    public function getRevenueAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        $cacheKey = $this->cachePrefix . "revenue:" . $startDate->format('Y-m-d') . ":" . $endDate->format('Y-m-d');
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($startDate, $endDate) {
            // Daily revenue breakdown
            $dailyRevenue = Payment::select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('SUM(amount) as revenue'),
                    DB::raw('COUNT(*) as transactions')
                )
                ->where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy(DB::raw('DATE(created_at)'))
                ->orderBy('date')
                ->get();

            // Revenue by payment method
            $revenueByPaymentMethod = Payment::join('payment_methods', 'payments.payment_method_id', '=', 'payment_methods.id')
                ->select('payment_methods.name', DB::raw('SUM(payments.amount) as revenue'))
                ->where('payments.status', 'completed')
                ->whereBetween('payments.created_at', [$startDate, $endDate])
                ->groupBy('payment_methods.id', 'payment_methods.name')
                ->get();

            // Revenue by service category
            $revenueByCategory = DB::table('booking_services')
                ->join('bookings', 'booking_services.booking_id', '=', 'bookings.id')
                ->join('services', 'booking_services.service_id', '=', 'services.id')
                ->join('service_categories', 'services.category_id', '=', 'service_categories.id')
                ->select('service_categories.name', DB::raw('SUM(booking_services.total_price) as revenue'))
                ->whereBetween('bookings.created_at', [$startDate, $endDate])
                ->where('bookings.status', 'completed')
                ->groupBy('service_categories.id', 'service_categories.name')
                ->get();

            // Commission analytics
            $commissionData = Booking::select(
                    DB::raw('SUM(total_amount) as gross_revenue'),
                    DB::raw('SUM(commission_amount) as total_commission'),
                    DB::raw('SUM(barber_amount) as barber_earnings')
                )
                ->where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->first();

            return [
                'daily_revenue' => $dailyRevenue,
                'payment_methods' => $revenueByPaymentMethod,
                'service_categories' => $revenueByCategory,
                'commission_breakdown' => $commissionData,
                'revenue_trends' => $this->getRevenueTrends($startDate, $endDate),
                'top_earning_barbers' => $this->getTopEarningBarbers($startDate, $endDate),
                'revenue_forecasting' => $this->getRevenueForecast($startDate, $endDate)
            ];
        });
    }

    /**
     * Get customer analytics
     */
    public function getCustomerAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        $cacheKey = $this->cachePrefix . "customers:" . $startDate->format('Y-m-d') . ":" . $endDate->format('Y-m-d');
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($startDate, $endDate) {
            // Customer acquisition
            $newCustomers = User::whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy(DB::raw('DATE(created_at)'))
                ->get();

            // Customer retention
            $retentionData = $this->calculateCustomerRetention($startDate, $endDate);

            // Customer lifetime value
            $clvData = $this->calculateCustomerLifetimeValue();

            // Customer segmentation
            $segmentation = $this->getCustomerSegmentation();

            // Booking frequency analysis
            $bookingFrequency = DB::table('bookings')
                ->select('customer_id', DB::raw('COUNT(*) as booking_count'))
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('customer_id')
                ->get()
                ->groupBy(function ($item) {
                    if ($item->booking_count == 1) return 'one_time';
                    if ($item->booking_count <= 3) return 'occasional';
                    if ($item->booking_count <= 6) return 'regular';
                    return 'frequent';
                })
                ->map->count();

            // Geographic distribution
            $customersByCity = User::join('user_profiles', 'users.id', '=', 'user_profiles.user_id')
                ->join('cities', 'user_profiles.city_id', '=', 'cities.id')
                ->select('cities.name', DB::raw('COUNT(*) as count'))
                ->whereBetween('users.created_at', [$startDate, $endDate])
                ->groupBy('cities.id', 'cities.name')
                ->get();

            return [
                'acquisition' => $newCustomers,
                'retention' => $retentionData,
                'lifetime_value' => $clvData,
                'segmentation' => $segmentation,
                'booking_frequency' => $bookingFrequency,
                'geographic_distribution' => $customersByCity,
                'churn_analysis' => $this->getChurnAnalysis($startDate, $endDate),
                'satisfaction_trends' => $this->getSatisfactionTrends($startDate, $endDate)
            ];
        });
    }

    /**
     * Get barber analytics
     */
    public function getBarberAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        $cacheKey = $this->cachePrefix . "barbers:" . $startDate->format('Y-m-d') . ":" . $endDate->format('Y-m-d');
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($startDate, $endDate) {
            // Barber performance metrics
            $barberPerformance = Barber::select(
                    'barbers.*',
                    'users.name',
                    DB::raw('COUNT(bookings.id) as total_bookings'),
                    DB::raw('SUM(bookings.barber_amount) as total_earnings'),
                    DB::raw('AVG(reviews.rating) as avg_rating'),
                    DB::raw('COUNT(reviews.id) as review_count')
                )
                ->join('users', 'barbers.user_id', '=', 'users.id')
                ->leftJoin('bookings', function ($join) use ($startDate, $endDate) {
                    $join->on('barbers.id', '=', 'bookings.barber_id')
                         ->whereBetween('bookings.created_at', [$startDate, $endDate])
                         ->where('bookings.status', 'completed');
                })
                ->leftJoin('reviews', 'bookings.id', '=', 'reviews.booking_id')
                ->groupBy('barbers.id', 'users.name')
                ->orderByDesc('total_earnings')
                ->get();

            // Barber utilization rates
            $utilizationRates = $this->calculateBarberUtilization($startDate, $endDate);

            // Service popularity by barbers
            $servicePopularity = DB::table('booking_services')
                ->join('bookings', 'booking_services.booking_id', '=', 'bookings.id')
                ->join('services', 'booking_services.service_id', '=', 'services.id')
                ->select('services.name', DB::raw('COUNT(*) as booking_count'))
                ->whereBetween('bookings.created_at', [$startDate, $endDate])
                ->where('bookings.status', 'completed')
                ->groupBy('services.id', 'services.name')
                ->orderByDesc('booking_count')
                ->get();

            return [
                'performance_metrics' => $barberPerformance,
                'utilization_rates' => $utilizationRates,
                'service_popularity' => $servicePopularity,
                'rating_distribution' => $this->getRatingDistribution($startDate, $endDate),
                'earnings_analysis' => $this->getEarningsAnalysis($startDate, $endDate),
                'availability_patterns' => $this->getAvailabilityPatterns($startDate, $endDate)
            ];
        });
    }

    /**
     * Get service analytics
     */
    public function getServiceAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        // Service demand analysis
        $serviceDemand = DB::table('booking_services')
            ->join('bookings', 'booking_services.booking_id', '=', 'bookings.id')
            ->join('services', 'booking_services.service_id', '=', 'services.id')
            ->select(
                'services.name',
                DB::raw('COUNT(*) as booking_count'),
                DB::raw('SUM(booking_services.total_price) as revenue'),
                DB::raw('AVG(booking_services.unit_price) as avg_price')
            )
            ->whereBetween('bookings.created_at', [$startDate, $endDate])
            ->where('bookings.status', 'completed')
            ->groupBy('services.id', 'services.name')
            ->orderByDesc('booking_count')
            ->get();

        // Service combinations
        $serviceCombinations = $this->getServiceCombinations($startDate, $endDate);

        // Price optimization opportunities
        $priceOptimization = $this->getPriceOptimizationData($startDate, $endDate);

        return [
            'demand_analysis' => $serviceDemand,
            'combinations' => $serviceCombinations,
            'price_optimization' => $priceOptimization,
            'seasonal_trends' => $this->getSeasonalServiceTrends($startDate, $endDate),
            'profitability_analysis' => $this->getServiceProfitability($startDate, $endDate)
        ];
    }

    /**
     * Get geographic analytics
     */
    public function getGeographicAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        // Bookings by city
        $bookingsByCity = Booking::join('barbers', 'bookings.barber_id', '=', 'barbers.id')
            ->join('users', 'barbers.user_id', '=', 'users.id')
            ->join('user_profiles', 'users.id', '=', 'user_profiles.user_id')
            ->join('cities', 'user_profiles.city_id', '=', 'cities.id')
            ->select('cities.name', DB::raw('COUNT(*) as booking_count'))
            ->whereBetween('bookings.created_at', [$startDate, $endDate])
            ->groupBy('cities.id', 'cities.name')
            ->get();

        // Revenue by area
        $revenueByArea = Booking::join('barbers', 'bookings.barber_id', '=', 'barbers.id')
            ->join('users', 'barbers.user_id', '=', 'users.id')
            ->join('user_profiles', 'users.id', '=', 'user_profiles.user_id')
            ->join('areas', 'user_profiles.area_id', '=', 'areas.id')
            ->select('areas.name', DB::raw('SUM(bookings.total_amount) as revenue'))
            ->whereBetween('bookings.created_at', [$startDate, $endDate])
            ->where('bookings.status', 'completed')
            ->groupBy('areas.id', 'areas.name')
            ->get();

        return [
            'bookings_by_city' => $bookingsByCity,
            'revenue_by_area' => $revenueByArea,
            'expansion_opportunities' => $this->getExpansionOpportunities(),
            'market_penetration' => $this->getMarketPenetration($startDate, $endDate)
        ];
    }

    /**
     * Get trend analytics
     */
    public function getTrendAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        // Hourly booking patterns
        $hourlyPatterns = Booking::select(
                DB::raw('HOUR(created_at) as hour'),
                DB::raw('COUNT(*) as booking_count')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('HOUR(created_at)'))
            ->orderBy('hour')
            ->get();

        // Daily patterns
        $dailyPatterns = Booking::select(
                DB::raw('DAYOFWEEK(created_at) as day_of_week'),
                DB::raw('COUNT(*) as booking_count')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DAYOFWEEK(created_at)'))
            ->orderBy('day_of_week')
            ->get();

        // Monthly trends
        $monthlyTrends = Booking::select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('COUNT(*) as booking_count'),
                DB::raw('SUM(total_amount) as revenue')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('YEAR(created_at)'), DB::raw('MONTH(created_at)'))
            ->orderBy('year', 'month')
            ->get();

        return [
            'hourly_patterns' => $hourlyPatterns,
            'daily_patterns' => $dailyPatterns,
            'monthly_trends' => $monthlyTrends,
            'seasonal_analysis' => $this->getSeasonalAnalysis($startDate, $endDate),
            'growth_trends' => $this->getGrowthTrends($startDate, $endDate)
        ];
    }

    /**
     * Get predictive analytics
     */
    public function getPredictiveAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'demand_forecast' => $this->getDemandForecast($startDate, $endDate),
            'revenue_prediction' => $this->getRevenuePrediction($startDate, $endDate),
            'churn_prediction' => $this->getChurnPrediction(),
            'capacity_planning' => $this->getCapacityPlanning($startDate, $endDate),
            'market_opportunities' => $this->getMarketOpportunities()
        ];
    }

    // Helper methods for calculations

    protected function calculateGrowthRate($current, $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        return (($current - $previous) / $previous) * 100;
    }

    protected function getCompletionRate(Carbon $startDate, Carbon $endDate): float
    {
        $totalBookings = Booking::whereBetween('created_at', [$startDate, $endDate])->count();
        $completedBookings = Booking::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')->count();

        return $totalBookings > 0 ? ($completedBookings / $totalBookings) * 100 : 0;
    }

    protected function getCustomerSatisfaction(Carbon $startDate, Carbon $endDate): float
    {
        $avgRating = Review::whereBetween('created_at', [$startDate, $endDate])
            ->avg('rating');

        return $avgRating ? ($avgRating / 5) * 100 : 0;
    }

    protected function calculateCustomerRetention(Carbon $startDate, Carbon $endDate): array
    {
        // Simplified retention calculation
        $newCustomers = User::whereBetween('created_at', [$startDate, $endDate])->count();
        $returningCustomers = User::whereHas('bookings', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->where('created_at', '<', $startDate)->count();

        $totalActiveCustomers = $newCustomers + $returningCustomers;
        $retentionRate = $totalActiveCustomers > 0 ? ($returningCustomers / $totalActiveCustomers) * 100 : 0;

        return [
            'new_customers' => $newCustomers,
            'returning_customers' => $returningCustomers,
            'retention_rate' => $retentionRate
        ];
    }

    protected function calculateCustomerLifetimeValue(): array
    {
        // Simplified CLV calculation
        $avgBookingValue = Booking::where('status', 'completed')->avg('total_amount') ?? 0;
        $avgBookingsPerCustomer = DB::table('bookings')
            ->select('customer_id', DB::raw('COUNT(*) as booking_count'))
            ->where('status', 'completed')
            ->groupBy('customer_id')
            ->avg('booking_count') ?? 0;

        $clv = $avgBookingValue * $avgBookingsPerCustomer;

        return [
            'average_booking_value' => $avgBookingValue,
            'average_bookings_per_customer' => $avgBookingsPerCustomer,
            'customer_lifetime_value' => $clv
        ];
    }

    protected function getCustomerSegmentation(): array
    {
        // Segment customers based on booking frequency and value
        $segments = DB::table('bookings')
            ->select(
                'customer_id',
                DB::raw('COUNT(*) as booking_count'),
                DB::raw('SUM(total_amount) as total_spent'),
                DB::raw('AVG(total_amount) as avg_booking_value')
            )
            ->where('status', 'completed')
            ->groupBy('customer_id')
            ->get()
            ->groupBy(function ($customer) {
                if ($customer->booking_count >= 10 && $customer->total_spent >= 1000) {
                    return 'VIP';
                } elseif ($customer->booking_count >= 5 && $customer->total_spent >= 500) {
                    return 'Loyal';
                } elseif ($customer->booking_count >= 2) {
                    return 'Regular';
                } else {
                    return 'New';
                }
            })
            ->map->count();

        return $segments->toArray();
    }

    // Additional helper methods would be implemented here...
    // Due to space constraints, I'm showing the structure and key methods

    protected function getRevenueTrends(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for revenue trends
        return [];
    }

    protected function getTopEarningBarbers(Carbon $startDate, Carbon $endDate): Collection
    {
        return collect();
    }

    protected function getRevenueForecast(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for revenue forecasting
        return [];
    }

    protected function getChurnAnalysis(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for churn analysis
        return [];
    }

    protected function getSatisfactionTrends(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for satisfaction trends
        return [];
    }

    protected function calculateBarberUtilization(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for barber utilization calculation
        return [];
    }

    protected function getRatingDistribution(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for rating distribution
        return [];
    }

    protected function getEarningsAnalysis(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for earnings analysis
        return [];
    }

    protected function getAvailabilityPatterns(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for availability patterns
        return [];
    }

    protected function getServiceCombinations(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for service combinations analysis
        return [];
    }

    protected function getPriceOptimizationData(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for price optimization
        return [];
    }

    protected function getSeasonalServiceTrends(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for seasonal service trends
        return [];
    }

    protected function getServiceProfitability(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for service profitability analysis
        return [];
    }

    protected function getExpansionOpportunities(): array
    {
        // Implementation for expansion opportunities
        return [];
    }

    protected function getMarketPenetration(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for market penetration analysis
        return [];
    }

    protected function getSeasonalAnalysis(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for seasonal analysis
        return [];
    }

    protected function getGrowthTrends(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for growth trends
        return [];
    }

    protected function getDemandForecast(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for demand forecasting
        return [];
    }

    protected function getRevenuePrediction(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for revenue prediction
        return [];
    }

    protected function getChurnPrediction(): array
    {
        // Implementation for churn prediction
        return [];
    }

    protected function getCapacityPlanning(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for capacity planning
        return [];
    }

    protected function getMarketOpportunities(): array
    {
        // Implementation for market opportunities analysis
        return [];
    }
}
