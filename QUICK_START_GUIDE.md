# 🚀 **دليل التشغيل السريع - مشروع "حلاق على بابك"**

---

## 📋 **متطلبات النظام**

### 🖥️ **متطلبات الخادم (Backend)**
- **PHP**: 8.1 أو أحدث
- **Composer**: 2.0 أو أحدث
- **Node.js**: 18.0 أو أحدث
- **MySQL**: 8.0 أو أحدث
- **Redis**: 6.0 أو أحدث (اختياري)

### 🌐 **متطلبات لوحة الإدارة (Admin Dashboard)**
- **Node.js**: 18.0 أو أحدث
- **npm** أو **yarn**
- **Vue CLI**: 5.0 أو أحدث

### 📱 **متطلبات التطبيقات المحمولة**
- **Flutter**: 3.10 أو أحدث
- **Dart**: 3.0 أو أحدث
- **Android Studio** أو **VS Code**

---

## 🏗️ **خطوات تشغيل Backend (Laravel)**

### 1️⃣ **إعداد البيئة**
```bash
# الانتقال إلى مجلد Backend
cd backend

# تثبيت التبعيات
composer install

# نسخ ملف البيئة
cp .env.example .env

# توليد مفتاح التطبيق
php artisan key:generate
```

### 2️⃣ **إعداد قاعدة البيانات**
```bash
# إنشاء قاعدة البيانات
mysql -u root -p
CREATE DATABASE barber_app;
exit

# تحديث ملف .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=barber_app
DB_USERNAME=root
DB_PASSWORD=your_password

# تشغيل المايجريشن
php artisan migrate

# تشغيل السيدرز
php artisan db:seed
```

### 3️⃣ **إعداد التخزين**
```bash
# إنشاء رابط التخزين
php artisan storage:link

# إنشاء مجلدات التخزين
mkdir -p storage/app/public/avatars
mkdir -p storage/app/public/services
mkdir -p storage/app/public/uploads
```

### 4️⃣ **تشغيل الخادم**
```bash
# تشغيل خادم Laravel
php artisan serve

# أو تشغيل على منفذ محدد
php artisan serve --port=8000
```

**✅ Backend جاهز على: http://localhost:8000**

---

## 🖥️ **خطوات تشغيل لوحة الإدارة (Admin Dashboard)**

### 1️⃣ **إعداد المشروع**
```bash
# الانتقال إلى مجلد لوحة الإدارة
cd admin-dashboard

# تثبيت التبعيات
npm install
# أو
yarn install
```

### 2️⃣ **إعداد متغيرات البيئة**
```bash
# إنشاء ملف البيئة
cp .env.example .env

# تحديث المتغيرات
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_APP_NAME="حلاق على بابك - لوحة التحكم"
VITE_APP_VERSION=1.0.0
```

### 3️⃣ **تشغيل خادم التطوير**
```bash
# تشغيل خادم التطوير
npm run dev
# أو
yarn dev

# للتشغيل على منفذ محدد
npm run dev -- --port 3000
```

**✅ لوحة الإدارة جاهزة على: http://localhost:3000**

---

## 📱 **خطوات تشغيل التطبيقات المحمولة**

### 1️⃣ **تطبيق العملاء**
```bash
# الانتقال إلى مجلد تطبيق العملاء
cd mobile-apps/customer-app

# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run

# أو للويب
flutter run -d chrome
```

### 2️⃣ **تطبيق الحلاقين**
```bash
# الانتقال إلى مجلد تطبيق الحلاقين
cd barber-app

# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run
```

---

## 🔧 **إعداد الخدمات الإضافية**

### 📧 **إعداد البريد الإلكتروني**
```env
# في ملف .env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="حلاق على بابك"
```

### 📱 **إعداد الإشعارات**
```env
# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-key-id
FIREBASE_PRIVATE_KEY="your-private-key"
FIREBASE_CLIENT_EMAIL=your-client-email
FIREBASE_CLIENT_ID=your-client-id

# Twilio SMS
TWILIO_SID=your-twilio-sid
TWILIO_TOKEN=your-twilio-token
TWILIO_FROM=your-twilio-number
```

### 💳 **إعداد أنظمة الدفع**
```env
# Stripe
STRIPE_KEY=pk_test_your-stripe-key
STRIPE_SECRET=sk_test_your-stripe-secret

# PayMob
PAYMOB_API_KEY=your-paymob-api-key
PAYMOB_INTEGRATION_ID=your-integration-id
```

---

## 🗄️ **إعداد قاعدة البيانات المتقدم**

### 1️⃣ **إنشاء المستخدم الإداري**
```bash
# تشغيل Tinker
php artisan tinker

# إنشاء مستخدم إداري
$admin = App\Models\User::create([
    'name' => 'مدير النظام',
    'email' => '<EMAIL>',
    'password' => bcrypt('admin123'),
    'user_type' => 'admin',
    'status' => 'active',
    'email_verified_at' => now()
]);

# إعطاء صلاحيات الإدارة
$admin->assignRole('admin');
```

### 2️⃣ **إضافة بيانات تجريبية**
```bash
# تشغيل السيدرز المتقدمة
php artisan db:seed --class=CitiesSeeder
php artisan db:seed --class=ServicesSeeder
php artisan db:seed --class=UsersSeeder
php artisan db:seed --class=BarbersSeeder
```

---

## 🚀 **تشغيل النظام الكامل**

### 📝 **سكريبت التشغيل السريع**
```bash
#!/bin/bash
# حفظ هذا السكريبت كـ start.sh

echo "🚀 بدء تشغيل مشروع حلاق على بابك..."

# تشغيل Backend
echo "📡 تشغيل Backend..."
cd backend
php artisan serve --port=8000 &
BACKEND_PID=$!

# تشغيل Admin Dashboard
echo "🖥️ تشغيل لوحة الإدارة..."
cd ../admin-dashboard
npm run dev -- --port=3000 &
ADMIN_PID=$!

# تشغيل Customer App (Web)
echo "📱 تشغيل تطبيق العملاء..."
cd ../mobile-apps/customer-app
flutter run -d chrome --web-port=8080 &
CUSTOMER_PID=$!

echo "✅ جميع الخدمات تعمل الآن:"
echo "📡 Backend: http://localhost:8000"
echo "🖥️ لوحة الإدارة: http://localhost:3000"
echo "📱 تطبيق العملاء: http://localhost:8080"

# انتظار إيقاف العمليات
wait $BACKEND_PID $ADMIN_PID $CUSTOMER_PID
```

### 🔧 **تشغيل السكريبت**
```bash
# إعطاء صلاحية التنفيذ
chmod +x start.sh

# تشغيل السكريبت
./start.sh
```

---

## 🌐 **الوصول للنظام**

### 🖥️ **لوحة الإدارة**
- **الرابط**: http://localhost:3000
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123

### 📡 **API Backend**
- **الرابط**: http://localhost:8000/api/v1
- **التوثيق**: http://localhost:8000/api/documentation

### 📱 **تطبيق العملاء (Web)**
- **الرابط**: http://localhost:8080

---

## 🔍 **استكشاف الأخطاء**

### ❌ **مشاكل شائعة وحلولها**

#### 1️⃣ **خطأ في قاعدة البيانات**
```bash
# التحقق من الاتصال
php artisan tinker
DB::connection()->getPdo();

# إعادة تشغيل المايجريشن
php artisan migrate:fresh --seed
```

#### 2️⃣ **خطأ في التبعيات**
```bash
# Backend
composer install --no-dev --optimize-autoloader

# Admin Dashboard
rm -rf node_modules package-lock.json
npm install

# Flutter
flutter clean
flutter pub get
```

#### 3️⃣ **خطأ في الصلاحيات**
```bash
# Linux/Mac
sudo chown -R $USER:$USER storage bootstrap/cache
chmod -R 775 storage bootstrap/cache

# Windows (PowerShell as Admin)
icacls storage /grant Everyone:F /T
icacls bootstrap/cache /grant Everyone:F /T
```

#### 4️⃣ **خطأ في المنافذ**
```bash
# التحقق من المنافذ المستخدمة
netstat -tulpn | grep :8000
netstat -tulpn | grep :3000

# قتل العمليات
kill -9 $(lsof -t -i:8000)
kill -9 $(lsof -t -i:3000)
```

---

## 📊 **مراقبة الأداء**

### 📈 **أدوات المراقبة**
```bash
# مراقبة اللوجز
tail -f storage/logs/laravel.log

# مراقبة قاعدة البيانات
php artisan db:monitor

# مراقبة الذاكرة
php artisan queue:monitor
```

---

## 🎯 **الخطوات التالية**

### ✅ **بعد التشغيل الناجح:**
1. **تسجيل الدخول للوحة الإدارة**
2. **إضافة المدن والمناطق**
3. **إضافة الخدمات**
4. **إضافة حلاقين تجريبيين**
5. **اختبار عملية الحجز**
6. **اختبار أنظمة الدفع**

### 🚀 **للإنتاج:**
1. **تحديث متغيرات البيئة**
2. **إعداد SSL**
3. **تحسين الأداء**
4. **إعداد النسخ الاحتياطي**
5. **مراقبة الأمان**

---

**🎉 مبروك! مشروع "حلاق على بابك" جاهز للعمل! 🚀**

**📞 للدعم التقني: تواصل معي في أي وقت**
