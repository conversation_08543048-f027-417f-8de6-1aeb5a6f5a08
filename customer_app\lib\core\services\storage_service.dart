import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class StorageService {
  static SharedPreferences? _prefs;

  // Initialize storage service
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Get SharedPreferences instance
  static SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call init() first.');
    }
    return _prefs!;
  }

  // String operations
  static Future<bool> setString(String key, String value) async {
    return await prefs.setString(key, value);
  }

  static String? getString(String key) {
    return prefs.getString(key);
  }

  // Integer operations
  static Future<bool> setInt(String key, int value) async {
    return await prefs.setInt(key, value);
  }

  static int? getInt(String key) {
    return prefs.getInt(key);
  }

  // Double operations
  static Future<bool> setDouble(String key, double value) async {
    return await prefs.setDouble(key, value);
  }

  static double? getDouble(String key) {
    return prefs.getDouble(key);
  }

  // Boolean operations
  static Future<bool> setBool(String key, bool value) async {
    return await prefs.setBool(key, value);
  }

  static bool? getBool(String key) {
    return prefs.getBool(key);
  }

  // String list operations
  static Future<bool> setStringList(String key, List<String> value) async {
    return await prefs.setStringList(key, value);
  }

  static List<String>? getStringList(String key) {
    return prefs.getStringList(key);
  }

  // JSON operations
  static Future<bool> setJson(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    return await setString(key, jsonString);
  }

  static Map<String, dynamic>? getJson(String key) {
    final jsonString = getString(key);
    if (jsonString != null) {
      try {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // List of JSON operations
  static Future<bool> setJsonList(String key, List<Map<String, dynamic>> value) async {
    final jsonString = jsonEncode(value);
    return await setString(key, jsonString);
  }

  static List<Map<String, dynamic>>? getJsonList(String key) {
    final jsonString = getString(key);
    if (jsonString != null) {
      try {
        final List<dynamic> decoded = jsonDecode(jsonString);
        return decoded.cast<Map<String, dynamic>>();
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Remove operations
  static Future<bool> remove(String key) async {
    return await prefs.remove(key);
  }

  // Clear all data
  static Future<bool> clear() async {
    return await prefs.clear();
  }

  // Check if key exists
  static bool containsKey(String key) {
    return prefs.containsKey(key);
  }

  // Get all keys
  static Set<String> getKeys() {
    return prefs.getKeys();
  }

  // Reload preferences
  static Future<void> reload() async {
    await prefs.reload();
  }

  // Storage keys constants
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String isFirstTimeKey = 'is_first_time';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';
  static const String notificationSettingsKey = 'notification_settings';
  static const String locationPermissionKey = 'location_permission';
  static const String biometricEnabledKey = 'biometric_enabled';
  static const String autoLoginKey = 'auto_login';
  static const String savedAddressesKey = 'saved_addresses';
  static const String favoriteBarbers = 'favorite_barbers';
  static const String recentSearchesKey = 'recent_searches';
  static const String cartItemsKey = 'cart_items';
  static const String wishlistItemsKey = 'wishlist_items';
  static const String bookingHistoryKey = 'booking_history';
  static const String paymentMethodsKey = 'payment_methods';
  static const String appSettingsKey = 'app_settings';

  // Helper methods for common operations
  static Future<void> saveUserSession({
    required String token,
    required String userData,
  }) async {
    await Future.wait([
      setString(authTokenKey, token),
      setString(userDataKey, userData),
    ]);
  }

  static Future<void> clearUserSession() async {
    await Future.wait([
      remove(authTokenKey),
      remove(userDataKey),
      remove(autoLoginKey),
    ]);
  }

  static bool isUserLoggedIn() {
    return containsKey(authTokenKey) && getString(authTokenKey) != null;
  }

  static Future<void> saveAppSettings(Map<String, dynamic> settings) async {
    await setJson(appSettingsKey, settings);
  }

  static Map<String, dynamic> getAppSettings() {
    return getJson(appSettingsKey) ?? {};
  }

  static Future<void> addToRecentSearches(String search) async {
    final searches = getStringList(recentSearchesKey) ?? [];
    searches.remove(search); // Remove if already exists
    searches.insert(0, search); // Add to beginning
    
    // Keep only last 10 searches
    if (searches.length > 10) {
      searches.removeRange(10, searches.length);
    }
    
    await setStringList(recentSearchesKey, searches);
  }

  static List<String> getRecentSearches() {
    return getStringList(recentSearchesKey) ?? [];
  }

  static Future<void> clearRecentSearches() async {
    await remove(recentSearchesKey);
  }

  static Future<void> addFavoriteBarber(String barberId) async {
    final favorites = getStringList(favoriteBarbers) ?? [];
    if (!favorites.contains(barberId)) {
      favorites.add(barberId);
      await setStringList(favoriteBarbers, favorites);
    }
  }

  static Future<void> removeFavoriteBarber(String barberId) async {
    final favorites = getStringList(favoriteBarbers) ?? [];
    favorites.remove(barberId);
    await setStringList(favoriteBarbers, favorites);
  }

  static List<String> getFavoriteBarbers() {
    return getStringList(favoriteBarbers) ?? [];
  }

  static bool isFavoriteBarber(String barberId) {
    final favorites = getFavoriteBarbers();
    return favorites.contains(barberId);
  }

  // Cache management
  static Future<void> setCacheData(String key, dynamic data, {Duration? expiry}) async {
    final cacheData = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiry': expiry?.inMilliseconds,
    };
    await setJson('cache_$key', cacheData);
  }

  static T? getCacheData<T>(String key) {
    final cacheData = getJson('cache_$key');
    if (cacheData != null) {
      final timestamp = cacheData['timestamp'] as int?;
      final expiry = cacheData['expiry'] as int?;
      
      if (timestamp != null) {
        final now = DateTime.now().millisecondsSinceEpoch;
        if (expiry != null && (now - timestamp) > expiry) {
          // Cache expired
          remove('cache_$key');
          return null;
        }
        return cacheData['data'] as T?;
      }
    }
    return null;
  }

  static Future<void> clearCache() async {
    final keys = getKeys();
    final cacheKeys = keys.where((key) => key.startsWith('cache_'));
    for (final key in cacheKeys) {
      await remove(key);
    }
  }

  // Backup and restore
  static Map<String, dynamic> exportData() {
    final data = <String, dynamic>{};
    final keys = getKeys();
    
    for (final key in keys) {
      if (!key.startsWith('cache_')) {
        final value = prefs.get(key);
        data[key] = value;
      }
    }
    
    return data;
  }

  static Future<void> importData(Map<String, dynamic> data) async {
    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;
      
      if (value is String) {
        await setString(key, value);
      } else if (value is int) {
        await setInt(key, value);
      } else if (value is double) {
        await setDouble(key, value);
      } else if (value is bool) {
        await setBool(key, value);
      } else if (value is List<String>) {
        await setStringList(key, value);
      }
    }
  }
}
