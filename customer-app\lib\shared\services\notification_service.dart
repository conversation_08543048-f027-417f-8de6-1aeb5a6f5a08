import 'dart:convert';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../core/constants/app_constants.dart';
import '../../core/network/api_client.dart';
import '../../core/network/api_endpoints.dart';
import '../../core/utils/app_router.dart';
import '../models/notification_model.dart';
import 'storage_service.dart';

abstract class NotificationService {
  Future<void> initialize();
  Future<void> requestPermissions();
  Future<String?> getDeviceToken();
  Future<void> subscribeToTopic(String topic);
  Future<void> unsubscribeFromTopic(String topic);
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    NotificationChannel? channel,
  });
  Future<List<NotificationModel>> getNotifications({
    int page = 1,
    int limit = 20,
    bool unreadOnly = false,
  });
  Future<bool> markAsRead(String notificationId);
  Future<bool> markAllAsRead();
  Future<bool> deleteNotification(String notificationId);
  Future<int> getUnreadCount();
  void handleNotificationTap(Map<String, dynamic> payload);
}

class NotificationServiceImpl implements NotificationService {
  final ApiClient _apiClient;
  final StorageService _storageService;
  
  late FirebaseMessaging _firebaseMessaging;
  late FlutterLocalNotificationsPlugin _localNotifications;
  
  String? _deviceToken;
  bool _isInitialized = false;

  NotificationServiceImpl({
    required ApiClient apiClient,
    required StorageService storageService,
  }) : _apiClient = apiClient,
       _storageService = storageService;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Initialize Firebase Messaging
      _firebaseMessaging = FirebaseMessaging.instance;
      
      // Initialize Local Notifications
      _localNotifications = FlutterLocalNotificationsPlugin();
      
      // Request permissions
      await requestPermissions();
      
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Get device token
      _deviceToken = await getDeviceToken();
      
      // Send token to server
      if (_deviceToken != null) {
        await _sendTokenToServer(_deviceToken!);
      }
      
      // Setup message handlers
      _setupMessageHandlers();
      
      // Subscribe to default topics
      await _subscribeToDefaultTopics();
      
      _isInitialized = true;
    } catch (e) {
      // Handle initialization error
    }
  }

  @override
  Future<void> requestPermissions() async {
    try {
      // Request Firebase Messaging permissions
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        // Request local notification permissions for Android
        if (Platform.isAndroid) {
          await Permission.notification.request();
        }
      }
    } catch (e) {
      // Handle permission error
    }
  }

  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (details) {
        if (details.payload != null) {
          final payload = jsonDecode(details.payload!);
          handleNotificationTap(payload);
        }
      },
    );

    // Create notification channels for Android
    if (Platform.isAndroid) {
      await _createNotificationChannels();
    }
  }

  Future<void> _createNotificationChannels() async {
    const channels = [
      AndroidNotificationChannel(
        'booking_updates',
        'تحديثات الحجوزات',
        description: 'إشعارات تحديثات حالة الحجوزات',
        importance: Importance.high,
        sound: RawResourceAndroidNotificationSound('booking_notification'),
      ),
      AndroidNotificationChannel(
        'promotions',
        'العروض والخصومات',
        description: 'إشعارات العروض والخصومات الخاصة',
        importance: Importance.defaultImportance,
      ),
      AndroidNotificationChannel(
        'general',
        'إشعارات عامة',
        description: 'الإشعارات العامة للتطبيق',
        importance: Importance.defaultImportance,
      ),
      AndroidNotificationChannel(
        'vip',
        'خدمات VIP',
        description: 'إشعارات خاصة بخدمات VIP',
        importance: Importance.high,
        sound: RawResourceAndroidNotificationSound('vip_notification'),
      ),
    ];

    for (final channel in channels) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }
  }

  void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _handleForegroundMessage(message);
    });

    // Handle background message taps
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _handleMessageTap(message);
    });

    // Handle app launch from terminated state
    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        _handleMessageTap(message);
      }
    });
  }

  void _handleForegroundMessage(RemoteMessage message) {
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      showLocalNotification(
        title: notification.title ?? 'إشعار جديد',
        body: notification.body ?? '',
        payload: jsonEncode(data),
        channel: _getNotificationChannel(data['type']),
      );
    }
  }

  void _handleMessageTap(RemoteMessage message) {
    final data = message.data;
    handleNotificationTap(data);
  }

  @override
  Future<String?> getDeviceToken() async {
    try {
      return await _firebaseMessaging.getToken();
    } catch (e) {
      return null;
    }
  }

  Future<void> _sendTokenToServer(String token) async {
    try {
      await _apiClient.post(
        ApiEndpoints.updateDeviceToken,
        data: {
          'device_token': token,
          'platform': Platform.isIOS ? 'ios' : 'android',
        },
      );
    } catch (e) {
      // Handle token update error
    }
  }

  Future<void> _subscribeToDefaultTopics() async {
    try {
      await subscribeToTopic('all_users');
      await subscribeToTopic('customers');
      
      // Subscribe to city-specific topic if available
      final cityId = await _storageService.getString('user_city_id');
      if (cityId != null) {
        await subscribeToTopic('city_$cityId');
      }
    } catch (e) {
      // Handle subscription error
    }
  }

  @override
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
    } catch (e) {
      // Handle subscription error
    }
  }

  @override
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
    } catch (e) {
      // Handle unsubscription error
    }
  }

  @override
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    NotificationChannel? channel,
  }) async {
    try {
      final channelId = channel?.id ?? 'general';
      final channelName = channel?.name ?? 'إشعارات عامة';
      
      final androidDetails = AndroidNotificationDetails(
        channelId,
        channelName,
        channelDescription: channel?.description,
        importance: channel?.importance ?? Importance.defaultImportance,
        priority: Priority.high,
        sound: channel?.sound,
        icon: '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        details,
        payload: payload,
      );
    } catch (e) {
      // Handle notification display error
    }
  }

  @override
  Future<List<NotificationModel>> getNotifications({
    int page = 1,
    int limit = 20,
    bool unreadOnly = false,
  }) async {
    try {
      final response = await _apiClient.get(
        ApiEndpoints.notifications,
        queryParameters: {
          'page': page,
          'limit': limit,
          if (unreadOnly) 'unread_only': true,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> notificationsJson = response.data['data'];
        return notificationsJson
            .map((json) => NotificationModel.fromJson(json))
            .toList();
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<bool> markAsRead(String notificationId) async {
    try {
      final response = await _apiClient.post(
        '${ApiEndpoints.markNotificationAsRead}/$notificationId',
      );

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> markAllAsRead() async {
    try {
      final response = await _apiClient.post(ApiEndpoints.markAllNotificationsAsRead);
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> deleteNotification(String notificationId) async {
    try {
      final response = await _apiClient.delete(
        '${ApiEndpoints.deleteNotification}/$notificationId',
      );

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<int> getUnreadCount() async {
    try {
      final response = await _apiClient.get(ApiEndpoints.unreadNotificationsCount);
      
      if (response.statusCode == 200) {
        return response.data['data']['count'] ?? 0;
      }

      return 0;
    } catch (e) {
      return 0;
    }
  }

  @override
  void handleNotificationTap(Map<String, dynamic> payload) {
    final type = payload['type'];
    final id = payload['id'];

    switch (type) {
      case 'booking_confirmed':
      case 'booking_cancelled':
      case 'booking_completed':
        if (id != null) {
          AppRouter.toBookingDetails(bookingId: id);
        } else {
          AppRouter.toBookings();
        }
        break;
        
      case 'promotion':
      case 'offer':
        AppRouter.toOffers();
        break;
        
      case 'vip_upgrade':
        AppRouter.toVipUpgrade();
        break;
        
      case 'wallet_update':
        AppRouter.toWallet();
        break;
        
      case 'rating_request':
        if (id != null) {
          AppRouter.toBookingDetails(bookingId: id);
        }
        break;
        
      default:
        AppRouter.toNotifications();
        break;
    }
  }

  NotificationChannel? _getNotificationChannel(String? type) {
    switch (type) {
      case 'booking_confirmed':
      case 'booking_cancelled':
      case 'booking_completed':
        return const NotificationChannel(
          id: 'booking_updates',
          name: 'تحديثات الحجوزات',
          importance: Importance.high,
        );
        
      case 'promotion':
      case 'offer':
        return const NotificationChannel(
          id: 'promotions',
          name: 'العروض والخصومات',
          importance: Importance.defaultImportance,
        );
        
      case 'vip_upgrade':
      case 'vip_benefit':
        return const NotificationChannel(
          id: 'vip',
          name: 'خدمات VIP',
          importance: Importance.high,
        );
        
      default:
        return const NotificationChannel(
          id: 'general',
          name: 'إشعارات عامة',
          importance: Importance.defaultImportance,
        );
    }
  }
}

class NotificationChannel {
  final String id;
  final String name;
  final String? description;
  final Importance importance;
  final RawResourceAndroidNotificationSound? sound;

  const NotificationChannel({
    required this.id,
    required this.name,
    this.description,
    this.importance = Importance.defaultImportance,
    this.sound,
  });
}
