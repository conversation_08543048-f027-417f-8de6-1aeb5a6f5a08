import 'package:get/get.dart';
import '../../features/splash/views/splash_screen.dart';
import '../../features/onboarding/views/onboarding_screen.dart';
import '../../features/auth/views/login_screen.dart';
import '../../features/auth/views/register_screen.dart';
import '../../features/auth/views/forgot_password_screen.dart';
import '../../features/auth/views/verify_phone_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../features/home/<USER>/main_navigation_screen.dart';
import '../../features/booking/views/booking_screen.dart';
import '../../features/booking/views/booking_details_screen.dart';
import '../../features/booking/views/barber_selection_screen.dart';
import '../../features/booking/views/service_selection_screen.dart';
import '../../features/booking/views/date_time_selection_screen.dart';
import '../../features/booking/views/location_selection_screen.dart';
import '../../features/booking/views/payment_screen.dart';
import '../../features/booking/views/booking_confirmation_screen.dart';
import '../../features/profile/views/profile_screen.dart';
import '../../features/profile/views/edit_profile_screen.dart';
import '../../features/profile/views/change_password_screen.dart';
import '../../features/profile/views/address_management_screen.dart';
import '../../features/profile/views/notification_settings_screen.dart';
import '../../features/vip/views/vip_screen.dart';
import '../../features/vip/views/vip_subscription_screen.dart';
import '../../features/store/views/store_screen.dart';
import '../../features/store/views/product_details_screen.dart';
import '../../features/store/views/cart_screen.dart';
import '../../features/store/views/checkout_screen.dart';
import '../../features/wallet/views/wallet_screen.dart';
import '../../features/wallet/views/add_funds_screen.dart';
import '../../features/loyalty/views/loyalty_screen.dart';
import '../../features/support/views/support_screen.dart';
import '../../features/support/views/chat_screen.dart';
import '../../features/notifications/views/notifications_screen.dart';

class AppRouter {
  // Route Names
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String verifyPhone = '/verify-phone';
  static const String home = '/home';
  static const String mainNavigation = '/main-navigation';
  
  // Booking Routes
  static const String booking = '/booking';
  static const String bookingDetails = '/booking-details';
  static const String barberSelection = '/barber-selection';
  static const String serviceSelection = '/service-selection';
  static const String dateTimeSelection = '/date-time-selection';
  static const String locationSelection = '/location-selection';
  static const String payment = '/payment';
  static const String bookingConfirmation = '/booking-confirmation';
  
  // Profile Routes
  static const String profile = '/profile';
  static const String editProfile = '/edit-profile';
  static const String changePassword = '/change-password';
  static const String addressManagement = '/address-management';
  static const String notificationSettings = '/notification-settings';
  
  // VIP Routes
  static const String vip = '/vip';
  static const String vipSubscription = '/vip-subscription';
  
  // Store Routes
  static const String store = '/store';
  static const String productDetails = '/product-details';
  static const String cart = '/cart';
  static const String checkout = '/checkout';
  
  // Wallet Routes
  static const String wallet = '/wallet';
  static const String addFunds = '/add-funds';
  
  // Loyalty Routes
  static const String loyalty = '/loyalty';
  
  // Support Routes
  static const String support = '/support';
  static const String chat = '/chat';
  
  // Notification Routes
  static const String notifications = '/notifications';

  // Route List
  static List<GetPage> routes = [
    // Splash & Onboarding
    GetPage(
      name: splash,
      page: () => const SplashScreen(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: onboarding,
      page: () => const OnboardingScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Authentication
    GetPage(
      name: login,
      page: () => const LoginScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: register,
      page: () => const RegisterScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: forgotPassword,
      page: () => const ForgotPasswordScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: verifyPhone,
      page: () => const VerifyPhoneScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Main Navigation
    GetPage(
      name: home,
      page: () => const HomeScreen(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: mainNavigation,
      page: () => const MainNavigationScreen(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Booking Flow
    GetPage(
      name: booking,
      page: () => const BookingScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: bookingDetails,
      page: () => const BookingDetailsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: barberSelection,
      page: () => const BarberSelectionScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: serviceSelection,
      page: () => const ServiceSelectionScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: dateTimeSelection,
      page: () => const DateTimeSelectionScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: locationSelection,
      page: () => const LocationSelectionScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: payment,
      page: () => const PaymentScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: bookingConfirmation,
      page: () => const BookingConfirmationScreen(),
      transition: Transition.zoom,
      transitionDuration: const Duration(milliseconds: 500),
    ),
    
    // Profile
    GetPage(
      name: profile,
      page: () => const ProfileScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: editProfile,
      page: () => const EditProfileScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: changePassword,
      page: () => const ChangePasswordScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: addressManagement,
      page: () => const AddressManagementScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: notificationSettings,
      page: () => const NotificationSettingsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // VIP
    GetPage(
      name: vip,
      page: () => const VipScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: vipSubscription,
      page: () => const VipSubscriptionScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Store
    GetPage(
      name: store,
      page: () => const StoreScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: productDetails,
      page: () => const ProductDetailsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: cart,
      page: () => const CartScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: checkout,
      page: () => const CheckoutScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Wallet
    GetPage(
      name: wallet,
      page: () => const WalletScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: addFunds,
      page: () => const AddFundsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Loyalty
    GetPage(
      name: loyalty,
      page: () => const LoyaltyScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Support
    GetPage(
      name: support,
      page: () => const SupportScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: chat,
      page: () => const ChatScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Notifications
    GetPage(
      name: notifications,
      page: () => const NotificationsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
  ];

  // Navigation Methods
  static void toSplash() => Get.offAllNamed(splash);
  static void toOnboarding() => Get.offAllNamed(onboarding);
  static void toLogin() => Get.offAllNamed(login);
  static void toRegister() => Get.toNamed(register);
  static void toForgotPassword() => Get.toNamed(forgotPassword);
  static void toVerifyPhone({Map<String, dynamic>? arguments}) => 
      Get.toNamed(verifyPhone, arguments: arguments);
  static void toHome() => Get.offAllNamed(mainNavigation);
  
  // Booking Navigation
  static void toBooking() => Get.toNamed(booking);
  static void toBookingDetails({required String bookingId}) => 
      Get.toNamed(bookingDetails, arguments: {'bookingId': bookingId});
  static void toBarberSelection({Map<String, dynamic>? arguments}) => 
      Get.toNamed(barberSelection, arguments: arguments);
  static void toServiceSelection({Map<String, dynamic>? arguments}) => 
      Get.toNamed(serviceSelection, arguments: arguments);
  static void toDateTimeSelection({Map<String, dynamic>? arguments}) => 
      Get.toNamed(dateTimeSelection, arguments: arguments);
  static void toLocationSelection({Map<String, dynamic>? arguments}) => 
      Get.toNamed(locationSelection, arguments: arguments);
  static void toPayment({Map<String, dynamic>? arguments}) => 
      Get.toNamed(payment, arguments: arguments);
  static void toBookingConfirmation({Map<String, dynamic>? arguments}) => 
      Get.offAllNamed(bookingConfirmation, arguments: arguments);
  
  // Profile Navigation
  static void toProfile() => Get.toNamed(profile);
  static void toEditProfile() => Get.toNamed(editProfile);
  static void toChangePassword() => Get.toNamed(changePassword);
  static void toAddressManagement() => Get.toNamed(addressManagement);
  static void toNotificationSettings() => Get.toNamed(notificationSettings);
  
  // VIP Navigation
  static void toVip() => Get.toNamed(vip);
  static void toVipSubscription({Map<String, dynamic>? arguments}) => 
      Get.toNamed(vipSubscription, arguments: arguments);
  
  // Store Navigation
  static void toStore() => Get.toNamed(store);
  static void toProductDetails({required String productId}) => 
      Get.toNamed(productDetails, arguments: {'productId': productId});
  static void toCart() => Get.toNamed(cart);
  static void toCheckout() => Get.toNamed(checkout);
  
  // Wallet Navigation
  static void toWallet() => Get.toNamed(wallet);
  static void toAddFunds() => Get.toNamed(addFunds);
  
  // Other Navigation
  static void toLoyalty() => Get.toNamed(loyalty);
  static void toSupport() => Get.toNamed(support);
  static void toChat({Map<String, dynamic>? arguments}) => 
      Get.toNamed(chat, arguments: arguments);
  static void toNotifications() => Get.toNamed(notifications);
  
  // Utility Methods
  static void back() => Get.back();
  static void backUntil(String routeName) => Get.until((route) => route.settings.name == routeName);
  static void offAll(String routeName) => Get.offAllNamed(routeName);
}
