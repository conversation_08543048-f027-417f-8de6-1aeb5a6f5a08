#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# رموز
ROCKET="🚀"
CHECK="✅"
CROSS="❌"
GEAR="⚙️"
COMPUTER="🖥️"
PHONE="📱"
DATABASE="🗄️"
LOCK="🔐"

echo -e "${BLUE}========================================"
echo -e "${ROCKET} مرحباً بك في مشروع \"حلاق على بابك\""
echo -e "========================================${NC}"
echo ""

# دالة للتحقق من وجود الأوامر
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${CHECK} $1 مثبت"
        return 0
    else
        echo -e "${CROSS} $1 غير مثبت. يرجى تثبيت $1"
        return 1
    fi
}

# دالة لإيقاف العمليات عند الخروج
cleanup() {
    echo -e "\n${YELLOW}إيقاف الخدمات...${NC}"
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
    fi
    if [ ! -z "$ADMIN_PID" ]; then
        kill $ADMIN_PID 2>/dev/null
    fi
    if [ ! -z "$CUSTOMER_PID" ]; then
        kill $CUSTOMER_PID 2>/dev/null
    fi
    echo -e "${CHECK} تم إيقاف جميع الخدمات"
    exit 0
}

# تسجيل دالة التنظيف للتشغيل عند الخروج
trap cleanup SIGINT SIGTERM

echo -e "${GEAR} التحقق من المتطلبات..."

# التحقق من المتطلبات
REQUIREMENTS_MET=true

if ! check_command "php"; then
    REQUIREMENTS_MET=false
fi

if ! check_command "composer"; then
    REQUIREMENTS_MET=false
fi

if ! check_command "node"; then
    REQUIREMENTS_MET=false
fi

if ! check_command "npm"; then
    REQUIREMENTS_MET=false
fi

if [ "$REQUIREMENTS_MET" = false ]; then
    echo -e "\n${CROSS} بعض المتطلبات غير متوفرة. يرجى تثبيتها أولاً."
    exit 1
fi

echo ""
echo -e "${COMPUTER} إعداد Backend (Laravel)..."
echo ""

# التحقق من وجود مجلد backend
if [ ! -d "backend" ]; then
    echo -e "${CROSS} مجلد backend غير موجود"
    exit 1
fi

cd backend

# التحقق من وجود ملف .env
if [ ! -f ".env" ]; then
    echo -e "${GEAR} إنشاء ملف .env..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
    else
        echo -e "${CROSS} ملف .env.example غير موجود"
        exit 1
    fi
fi

# تثبيت تبعيات Laravel
echo -e "${GEAR} تثبيت تبعيات Laravel..."
composer install --no-dev --optimize-autoloader --quiet

# توليد مفتاح التطبيق
echo -e "${LOCK} توليد مفتاح التطبيق..."
php artisan key:generate --force --quiet

# إنشاء رابط التخزين
echo -e "${GEAR} إنشاء رابط التخزين..."
php artisan storage:link --quiet

# إنشاء مجلدات التخزين المطلوبة
mkdir -p storage/app/public/avatars
mkdir -p storage/app/public/services
mkdir -p storage/app/public/uploads

# تعيين الصلاحيات
chmod -R 775 storage bootstrap/cache

# تشغيل خادم Laravel في الخلفية
echo -e "${ROCKET} تشغيل خادم Laravel..."
php artisan serve --port=8000 --quiet &
BACKEND_PID=$!

# انتظار قصير للتأكد من تشغيل الخادم
sleep 3

# التحقق من تشغيل الخادم
if kill -0 $BACKEND_PID 2>/dev/null; then
    echo -e "${CHECK} خادم Laravel يعمل على المنفذ 8000"
else
    echo -e "${CROSS} فشل في تشغيل خادم Laravel"
    exit 1
fi

# العودة للمجلد الرئيسي
cd ..

echo ""
echo -e "${COMPUTER} إعداد لوحة الإدارة..."
echo ""

# التحقق من وجود مجلد لوحة الإدارة
if [ ! -d "admin-dashboard" ]; then
    echo -e "${CROSS} مجلد admin-dashboard غير موجود"
    cleanup
    exit 1
fi

cd admin-dashboard

# التحقق من وجود ملف .env
if [ ! -f ".env" ]; then
    echo -e "${GEAR} إنشاء ملف .env للوحة الإدارة..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
    fi
fi

# تثبيت تبعيات Node.js
echo -e "${GEAR} تثبيت تبعيات لوحة الإدارة..."
npm install --silent

# تشغيل خادم التطوير في الخلفية
echo -e "${ROCKET} تشغيل لوحة الإدارة..."
npm run dev -- --port 3000 --silent &
ADMIN_PID=$!

# انتظار قصير للتأكد من تشغيل الخادم
sleep 5

# التحقق من تشغيل الخادم
if kill -0 $ADMIN_PID 2>/dev/null; then
    echo -e "${CHECK} لوحة الإدارة تعمل على المنفذ 3000"
else
    echo -e "${CROSS} فشل في تشغيل لوحة الإدارة"
    cleanup
    exit 1
fi

# العودة للمجلد الرئيسي
cd ..

echo ""
echo -e "${CHECK} تم تشغيل جميع الخدمات بنجاح!"
echo ""
echo -e "${BLUE}🌐 الروابط المتاحة:"
echo "========================================"
echo -e "📡 Backend API: ${CYAN}http://localhost:8000${NC}"
echo -e "🖥️ لوحة الإدارة: ${CYAN}http://localhost:3000${NC}"
echo -e "📚 توثيق API: ${CYAN}http://localhost:8000/api/documentation${NC}"
echo -e "========================================${NC}"
echo ""
echo -e "${LOCK} بيانات تسجيل الدخول للوحة الإدارة:"
echo -e "البريد الإلكتروني: ${GREEN}<EMAIL>${NC}"
echo -e "كلمة المرور: ${GREEN}admin123${NC}"
echo ""
echo -e "${YELLOW}💡 نصائح:"
echo "- تأكد من تشغيل MySQL قبل استخدام النظام"
echo "- قم بإنشاء قاعدة بيانات باسم 'barber_app'"
echo "- راجع ملف QUICK_START_GUIDE.md للمزيد من التفاصيل${NC}"
echo ""

# فتح المتصفح (إذا كان متاحاً)
if command -v xdg-open &> /dev/null; then
    echo -e "${GEAR} فتح لوحة الإدارة في المتصفح..."
    xdg-open http://localhost:3000 &
elif command -v open &> /dev/null; then
    echo -e "${GEAR} فتح لوحة الإدارة في المتصفح..."
    open http://localhost:3000 &
fi

echo -e "${ROCKET} مبروك! مشروع \"حلاق على بابك\" جاهز للاستخدام"
echo ""
echo -e "${YELLOW}اضغط Ctrl+C لإيقاف جميع الخدمات${NC}"

# انتظار إشارة الإيقاف
wait
