{"dashboard": {"id": null, "title": "Barber App - Main Dashboard", "tags": ["barber-app", "monitoring"], "timezone": "Africa/Cairo", "panels": [{"id": 1, "title": "System Overview", "type": "stat", "targets": [{"expr": "up{job=\"barber-app\"}", "legendFormat": "App Status"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"barber-app\"}[5m])", "legendFormat": "{{method}} {{status}}"}], "yAxes": [{"label": "Requests/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 6, "y": 0}}, {"id": 3, "title": "Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"barber-app\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"barber-app\"}[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"label": "Seconds", "min": 0}], "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 4, "title": "Active Users", "type": "stat", "targets": [{"expr": "barber_app_active_users", "legendFormat": "Active Users"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 8}}, {"id": 5, "title": "Total Bookings Today", "type": "stat", "targets": [{"expr": "barber_app_bookings_total", "legendFormat": "Bookings"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 8}}, {"id": 6, "title": "Revenue Today", "type": "stat", "targets": [{"expr": "barber_app_revenue_total", "legendFormat": "Revenue"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "currencyEGP"}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 8}}, {"id": 7, "title": "Active Barbers", "type": "stat", "targets": [{"expr": "barber_app_active_barbers", "legendFormat": "Active Barbers"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 8}}, {"id": 8, "title": "Database Connections", "type": "graph", "targets": [{"expr": "mysql_global_status_threads_connected", "legendFormat": "Connected"}, {"expr": "mysql_global_variables_max_connections", "legendFormat": "Max Connections"}], "yAxes": [{"label": "Connections", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}}, {"id": 9, "title": "Redis Memory Usage", "type": "graph", "targets": [{"expr": "redis_memory_used_bytes", "legendFormat": "Used Memory"}, {"expr": "redis_memory_max_bytes", "legendFormat": "Max Memory"}], "yAxes": [{"label": "Bytes", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}}, {"id": 10, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"barber-app\",status=~\"5..\"}[5m])", "legendFormat": "5xx Errors"}, {"expr": "rate(http_requests_total{job=\"barber-app\",status=~\"4..\"}[5m])", "legendFormat": "4xx Errors"}], "yAxes": [{"label": "Errors/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}}, {"id": 11, "title": "Queue Jobs", "type": "graph", "targets": [{"expr": "barber_app_queue_jobs_pending", "legendFormat": "Pending Jobs"}, {"expr": "barber_app_queue_jobs_failed", "legendFormat": "Failed Jobs"}, {"expr": "barber_app_queue_jobs_processed", "legendFormat": "Processed Jobs"}], "yAxes": [{"label": "Jobs", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}}, {"id": 12, "title": "Booking Status Distribution", "type": "piechart", "targets": [{"expr": "barber_app_bookings_by_status", "legendFormat": "{{status}}"}], "gridPos": {"h": 8, "w": 8, "x": 0, "y": 28}}, {"id": 13, "title": "Payment Methods Usage", "type": "piechart", "targets": [{"expr": "barber_app_payments_by_method", "legendFormat": "{{method}}"}], "gridPos": {"h": 8, "w": 8, "x": 8, "y": 28}}, {"id": 14, "title": "Top Services", "type": "bargauge", "targets": [{"expr": "topk(5, barber_app_services_bookings)", "legendFormat": "{{service}}"}], "gridPos": {"h": 8, "w": 8, "x": 16, "y": 28}}, {"id": 15, "title": "CPU Usage", "type": "graph", "targets": [{"expr": "100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "{{instance}}"}], "yAxes": [{"label": "Percent", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}}, {"id": 16, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "100 * (1 - ((node_memory_MemAvailable_bytes) / (node_memory_MemTotal_bytes)))", "legendFormat": "{{instance}}"}], "yAxes": [{"label": "Percent", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 36}}, {"id": 17, "title": "Disk Usage", "type": "graph", "targets": [{"expr": "100 - ((node_filesystem_avail_bytes{mountpoint=\"/\"} * 100) / node_filesystem_size_bytes{mountpoint=\"/\"})", "legendFormat": "{{instance}}"}], "yAxes": [{"label": "Percent", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 44}}, {"id": 18, "title": "Network I/O", "type": "graph", "targets": [{"expr": "rate(node_network_receive_bytes_total{device!=\"lo\"}[5m])", "legendFormat": "{{device}} - Receive"}, {"expr": "rate(node_network_transmit_bytes_total{device!=\"lo\"}[5m])", "legendFormat": "{{device}} - Transmit"}], "yAxes": [{"label": "Bytes/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 44}}, {"id": 19, "title": "Live Stream Sessions", "type": "stat", "targets": [{"expr": "barber_app_live_streams_active", "legendFormat": "Active Streams"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 52}}, {"id": 20, "title": "VIP Members", "type": "stat", "targets": [{"expr": "barber_app_vip_members_total", "legendFormat": "VIP Members"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 52}}, {"id": 21, "title": "Average Rating", "type": "stat", "targets": [{"expr": "barber_app_average_rating", "legendFormat": "Avg <PERSON>ing"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 3}, {"color": "green", "value": 4}]}, "unit": "short", "min": 0, "max": 5}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 52}}, {"id": 22, "title": "Notification Delivery Rate", "type": "stat", "targets": [{"expr": "barber_app_notifications_delivered / barber_app_notifications_sent * 100", "legendFormat": "Delivery Rate"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "unit": "percent"}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 52}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "schemaVersion": 30, "version": 1, "links": [{"title": "Business Dashboard", "url": "/d/barber-business/barber-app-business-dashboard", "type": "dashboards"}, {"title": "Security Dashboard", "url": "/d/barber-security/barber-app-security-dashboard", "type": "dashboards"}]}}