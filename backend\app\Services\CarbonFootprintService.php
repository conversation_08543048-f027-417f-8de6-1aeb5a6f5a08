<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

class CarbonFootprintService
{
    protected $carbonApiUrl;
    protected $carbonApiKey;
    protected $emissionFactors;
    protected $offsetProviders;
    
    public function __construct()
    {
        $this->carbonApiUrl = config('services.carbon.api_url');
        $this->carbonApiKey = config('services.carbon.api_key');
        $this->initializeEmissionFactors();
        $this->initializeOffsetProviders();
    }

    /**
     * Initialize emission factors for different activities
     */
    protected function initializeEmissionFactors()
    {
        $this->emissionFactors = [
            // Transportation (kg CO2 per km)
            'car_petrol' => 0.21,
            'car_diesel' => 0.17,
            'car_electric' => 0.05,
            'motorcycle' => 0.14,
            'bus' => 0.08,
            'metro' => 0.03,
            'taxi' => 0.25,
            'walking' => 0.0,
            'bicycle' => 0.0,
            
            // Energy (kg CO2 per kWh)
            'electricity_grid' => 0.45,
            'electricity_solar' => 0.02,
            'electricity_wind' => 0.01,
            'natural_gas' => 0.18,
            
            // Digital services (kg CO2 per unit)
            'server_hour' => 0.5,
            'data_transfer_gb' => 0.006,
            'video_streaming_hour' => 0.036,
            'mobile_app_usage_hour' => 0.012,
            'api_request' => 0.0001,
            'database_query' => 0.00005,
            'notification_sent' => 0.00001,
            
            // Business operations (kg CO2 per unit)
            'office_space_m2_day' => 0.02,
            'employee_day' => 2.5,
            'paper_sheet' => 0.005,
            'plastic_packaging_g' => 0.003,
        ];
    }

    /**
     * Initialize carbon offset providers
     */
    protected function initializeOffsetProviders()
    {
        $this->offsetProviders = [
            'reforestation_egypt' => [
                'name' => 'Egypt Reforestation Project',
                'cost_per_ton' => 15.0, // USD
                'certification' => 'VCS',
                'location' => 'Egypt',
                'project_type' => 'Forestry',
                'available' => true
            ],
            'renewable_energy_mena' => [
                'name' => 'MENA Renewable Energy',
                'cost_per_ton' => 12.0,
                'certification' => 'Gold Standard',
                'location' => 'Middle East',
                'project_type' => 'Renewable Energy',
                'available' => true
            ],
            'waste_management_cairo' => [
                'name' => 'Cairo Waste Management',
                'cost_per_ton' => 18.0,
                'certification' => 'CDM',
                'location' => 'Cairo, Egypt',
                'project_type' => 'Waste Management',
                'available' => true
            ]
        ];
    }

    /**
     * Calculate carbon footprint for a booking
     */
    public function calculateBookingFootprint(array $bookingData): array
    {
        try {
            $totalEmissions = 0;
            $breakdown = [];

            // Transportation emissions
            if (isset($bookingData['transportation'])) {
                $transportEmissions = $this->calculateTransportationEmissions($bookingData['transportation']);
                $totalEmissions += $transportEmissions['total'];
                $breakdown['transportation'] = $transportEmissions;
            }

            // Service delivery emissions
            if (isset($bookingData['service_delivery'])) {
                $serviceEmissions = $this->calculateServiceEmissions($bookingData['service_delivery']);
                $totalEmissions += $serviceEmissions['total'];
                $breakdown['service_delivery'] = $serviceEmissions;
            }

            // Digital platform emissions
            $digitalEmissions = $this->calculateDigitalEmissions($bookingData);
            $totalEmissions += $digitalEmissions['total'];
            $breakdown['digital_platform'] = $digitalEmissions;

            // Equipment and supplies emissions
            if (isset($bookingData['equipment'])) {
                $equipmentEmissions = $this->calculateEquipmentEmissions($bookingData['equipment']);
                $totalEmissions += $equipmentEmissions['total'];
                $breakdown['equipment'] = $equipmentEmissions;
            }

            return [
                'total_emissions_kg' => round($totalEmissions, 4),
                'total_emissions_tons' => round($totalEmissions / 1000, 6),
                'breakdown' => $breakdown,
                'calculation_date' => now()->toISOString(),
                'methodology' => 'GHG Protocol Scope 1, 2, 3',
                'confidence_level' => 0.85
            ];

        } catch (\Exception $e) {
            Log::error('Carbon footprint calculation failed', [
                'booking_data' => $bookingData,
                'error' => $e->getMessage()
            ]);

            return [
                'total_emissions_kg' => 0,
                'total_emissions_tons' => 0,
                'breakdown' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Calculate transportation emissions
     */
    protected function calculateTransportationEmissions(array $transportData): array
    {
        $emissions = 0;
        $details = [];

        foreach ($transportData as $trip) {
            $mode = $trip['mode'] ?? 'car_petrol';
            $distance = $trip['distance_km'] ?? 0;
            $factor = $this->emissionFactors[$mode] ?? $this->emissionFactors['car_petrol'];
            
            $tripEmissions = $distance * $factor;
            $emissions += $tripEmissions;
            
            $details[] = [
                'mode' => $mode,
                'distance_km' => $distance,
                'emission_factor' => $factor,
                'emissions_kg' => round($tripEmissions, 4)
            ];
        }

        return [
            'total' => $emissions,
            'details' => $details
        ];
    }

    /**
     * Calculate service delivery emissions
     */
    protected function calculateServiceEmissions(array $serviceData): array
    {
        $emissions = 0;
        $details = [];

        // Barber travel to customer
        if (isset($serviceData['barber_travel'])) {
            $travel = $serviceData['barber_travel'];
            $mode = $travel['mode'] ?? 'car_petrol';
            $distance = $travel['distance_km'] ?? 0;
            $factor = $this->emissionFactors[$mode] ?? $this->emissionFactors['car_petrol'];
            
            $travelEmissions = $distance * $factor;
            $emissions += $travelEmissions;
            
            $details['barber_travel'] = [
                'mode' => $mode,
                'distance_km' => $distance,
                'emissions_kg' => round($travelEmissions, 4)
            ];
        }

        // Equipment transportation
        if (isset($serviceData['equipment_transport'])) {
            $equipment = $serviceData['equipment_transport'];
            $weight = $equipment['weight_kg'] ?? 5; // Default equipment weight
            $distance = $equipment['distance_km'] ?? 0;
            
            // Emissions based on weight and distance
            $equipmentEmissions = ($weight * 0.001) * $distance * 0.5; // Simplified calculation
            $emissions += $equipmentEmissions;
            
            $details['equipment_transport'] = [
                'weight_kg' => $weight,
                'distance_km' => $distance,
                'emissions_kg' => round($equipmentEmissions, 4)
            ];
        }

        return [
            'total' => $emissions,
            'details' => $details
        ];
    }

    /**
     * Calculate digital platform emissions
     */
    protected function calculateDigitalEmissions(array $bookingData): array
    {
        $emissions = 0;
        $details = [];

        // API requests
        $apiRequests = $bookingData['api_requests'] ?? 50; // Estimated requests per booking
        $apiEmissions = $apiRequests * $this->emissionFactors['api_request'];
        $emissions += $apiEmissions;
        $details['api_requests'] = [
            'count' => $apiRequests,
            'emissions_kg' => round($apiEmissions, 6)
        ];

        // Database queries
        $dbQueries = $bookingData['db_queries'] ?? 100; // Estimated queries per booking
        $dbEmissions = $dbQueries * $this->emissionFactors['database_query'];
        $emissions += $dbEmissions;
        $details['database_queries'] = [
            'count' => $dbQueries,
            'emissions_kg' => round($dbEmissions, 6)
        ];

        // Mobile app usage
        $appUsageHours = $bookingData['app_usage_hours'] ?? 0.5; // Estimated usage time
        $appEmissions = $appUsageHours * $this->emissionFactors['mobile_app_usage_hour'];
        $emissions += $appEmissions;
        $details['mobile_app_usage'] = [
            'hours' => $appUsageHours,
            'emissions_kg' => round($appEmissions, 4)
        ];

        // Notifications sent
        $notifications = $bookingData['notifications_sent'] ?? 10;
        $notificationEmissions = $notifications * $this->emissionFactors['notification_sent'];
        $emissions += $notificationEmissions;
        $details['notifications'] = [
            'count' => $notifications,
            'emissions_kg' => round($notificationEmissions, 6)
        ];

        // Live streaming (if applicable)
        if (isset($bookingData['live_streaming_hours'])) {
            $streamingHours = $bookingData['live_streaming_hours'];
            $streamingEmissions = $streamingHours * $this->emissionFactors['video_streaming_hour'];
            $emissions += $streamingEmissions;
            $details['live_streaming'] = [
                'hours' => $streamingHours,
                'emissions_kg' => round($streamingEmissions, 4)
            ];
        }

        return [
            'total' => $emissions,
            'details' => $details
        ];
    }

    /**
     * Calculate equipment emissions
     */
    protected function calculateEquipmentEmissions(array $equipmentData): array
    {
        $emissions = 0;
        $details = [];

        foreach ($equipmentData as $equipment) {
            $type = $equipment['type'] ?? 'electric_clipper';
            $usageHours = $equipment['usage_hours'] ?? 1;
            $powerConsumption = $equipment['power_kw'] ?? 0.1; // Default 100W
            
            // Energy consumption emissions
            $energyEmissions = $usageHours * $powerConsumption * $this->emissionFactors['electricity_grid'];
            $emissions += $energyEmissions;
            
            $details[] = [
                'type' => $type,
                'usage_hours' => $usageHours,
                'power_kw' => $powerConsumption,
                'emissions_kg' => round($energyEmissions, 4)
            ];
        }

        return [
            'total' => $emissions,
            'details' => $details
        ];
    }

    /**
     * Calculate monthly carbon footprint for user
     */
    public function calculateUserMonthlyFootprint(int $userId): array
    {
        try {
            $startDate = now()->startOfMonth();
            $endDate = now()->endOfMonth();
            
            // Get user's bookings for the month
            $bookings = \App\Models\Booking::where('customer_id', $userId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->with(['services', 'barber'])
                ->get();

            $totalEmissions = 0;
            $bookingFootprints = [];

            foreach ($bookings as $booking) {
                $bookingData = $this->prepareBookingDataForCalculation($booking);
                $footprint = $this->calculateBookingFootprint($bookingData);
                
                $totalEmissions += $footprint['total_emissions_kg'];
                $bookingFootprints[] = [
                    'booking_id' => $booking->id,
                    'date' => $booking->created_at->toDateString(),
                    'emissions_kg' => $footprint['total_emissions_kg'],
                    'breakdown' => $footprint['breakdown']
                ];
            }

            return [
                'user_id' => $userId,
                'month' => $startDate->format('Y-m'),
                'total_emissions_kg' => round($totalEmissions, 4),
                'total_emissions_tons' => round($totalEmissions / 1000, 6),
                'bookings_count' => $bookings->count(),
                'average_per_booking' => $bookings->count() > 0 ? round($totalEmissions / $bookings->count(), 4) : 0,
                'bookings' => $bookingFootprints,
                'offset_cost_usd' => $this->calculateOffsetCost($totalEmissions / 1000),
                'comparison' => $this->getEmissionComparison($totalEmissions)
            ];

        } catch (\Exception $e) {
            Log::error('User monthly footprint calculation failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Calculate platform-wide carbon footprint
     */
    public function calculatePlatformFootprint(Carbon $startDate, Carbon $endDate): array
    {
        try {
            $cacheKey = "platform_footprint_{$startDate->format('Y-m-d')}_{$endDate->format('Y-m-d')}";
            
            return Cache::remember($cacheKey, 3600, function () use ($startDate, $endDate) {
                // Server infrastructure emissions
                $serverEmissions = $this->calculateServerEmissions($startDate, $endDate);
                
                // Data transfer emissions
                $dataEmissions = $this->calculateDataTransferEmissions($startDate, $endDate);
                
                // User activity emissions
                $userEmissions = $this->calculateUserActivityEmissions($startDate, $endDate);
                
                // Business operations emissions
                $operationsEmissions = $this->calculateOperationsEmissions($startDate, $endDate);

                $totalEmissions = $serverEmissions['total'] + $dataEmissions['total'] + 
                                $userEmissions['total'] + $operationsEmissions['total'];

                return [
                    'period' => [
                        'start' => $startDate->toDateString(),
                        'end' => $endDate->toDateString(),
                        'days' => $startDate->diffInDays($endDate) + 1
                    ],
                    'total_emissions_kg' => round($totalEmissions, 2),
                    'total_emissions_tons' => round($totalEmissions / 1000, 4),
                    'breakdown' => [
                        'server_infrastructure' => $serverEmissions,
                        'data_transfer' => $dataEmissions,
                        'user_activity' => $userEmissions,
                        'business_operations' => $operationsEmissions
                    ],
                    'offset_cost_usd' => $this->calculateOffsetCost($totalEmissions / 1000),
                    'carbon_intensity' => $this->calculateCarbonIntensity($totalEmissions, $startDate, $endDate)
                ];
            });

        } catch (\Exception $e) {
            Log::error('Platform footprint calculation failed', [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
                'error' => $e->getMessage()
            ]);

            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Purchase carbon offsets
     */
    public function purchaseOffsets(float $emissionsTons, string $providerId, int $userId): array
    {
        try {
            if (!isset($this->offsetProviders[$providerId])) {
                throw new \Exception('Invalid offset provider');
            }

            $provider = $this->offsetProviders[$providerId];
            $cost = $emissionsTons * $provider['cost_per_ton'];

            // Call external offset provider API
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->carbonApiKey,
                'Content-Type' => 'application/json'
            ])->post($this->carbonApiUrl . '/offsets/purchase', [
                'provider_id' => $providerId,
                'emissions_tons' => $emissionsTons,
                'cost_usd' => $cost,
                'user_id' => $userId,
                'metadata' => [
                    'platform' => 'barber-app',
                    'purchase_date' => now()->toISOString()
                ]
            ]);

            if ($response->successful()) {
                $offsetData = $response->json();
                
                // Store offset record
                $this->storeOffsetRecord($userId, $emissionsTons, $cost, $providerId, $offsetData);

                return [
                    'success' => true,
                    'offset_id' => $offsetData['offset_id'],
                    'emissions_offset_tons' => $emissionsTons,
                    'cost_usd' => $cost,
                    'provider' => $provider['name'],
                    'certificate_url' => $offsetData['certificate_url'] ?? null,
                    'retirement_date' => $offsetData['retirement_date'] ?? null
                ];
            } else {
                throw new \Exception('Offset purchase failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('Carbon offset purchase failed', [
                'emissions_tons' => $emissionsTons,
                'provider_id' => $providerId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get carbon reduction recommendations
     */
    public function getReductionRecommendations(array $footprintData): array
    {
        $recommendations = [];

        // Transportation recommendations
        if (isset($footprintData['breakdown']['transportation'])) {
            $transportEmissions = $footprintData['breakdown']['transportation']['total'];
            if ($transportEmissions > 5) { // kg CO2
                $recommendations[] = [
                    'category' => 'transportation',
                    'priority' => 'high',
                    'title' => 'Use eco-friendly transportation',
                    'description' => 'Consider using electric vehicles, public transport, or walking/cycling for shorter distances',
                    'potential_reduction_kg' => $transportEmissions * 0.6,
                    'implementation_difficulty' => 'medium'
                ];
            }
        }

        // Digital platform recommendations
        if (isset($footprintData['breakdown']['digital_platform'])) {
            $recommendations[] = [
                'category' => 'digital',
                'priority' => 'medium',
                'title' => 'Optimize app usage',
                'description' => 'Use app features efficiently and enable power-saving modes',
                'potential_reduction_kg' => 0.1,
                'implementation_difficulty' => 'easy'
            ];
        }

        // Service delivery recommendations
        $recommendations[] = [
            'category' => 'service',
            'priority' => 'medium',
            'title' => 'Group bookings',
            'description' => 'Book multiple services in one visit to reduce travel emissions',
            'potential_reduction_kg' => 2.0,
            'implementation_difficulty' => 'easy'
        ];

        return $recommendations;
    }

    // Helper methods

    protected function prepareBookingDataForCalculation($booking): array
    {
        // Prepare booking data for carbon footprint calculation
        return [
            'transportation' => [
                [
                    'mode' => 'car_petrol', // Default assumption
                    'distance_km' => $this->estimateDistance($booking)
                ]
            ],
            'service_delivery' => [
                'barber_travel' => [
                    'mode' => 'car_petrol',
                    'distance_km' => $this->estimateDistance($booking)
                ]
            ],
            'api_requests' => 50,
            'db_queries' => 100,
            'app_usage_hours' => 0.5,
            'notifications_sent' => 10,
            'equipment' => [
                [
                    'type' => 'electric_clipper',
                    'usage_hours' => 1,
                    'power_kw' => 0.1
                ]
            ]
        ];
    }

    protected function estimateDistance($booking): float
    {
        // Estimate distance based on booking location
        // This would use actual geolocation data in production
        return 10.0; // Default 10km
    }

    protected function calculateOffsetCost(float $emissionsTons): array
    {
        $costs = [];
        foreach ($this->offsetProviders as $id => $provider) {
            if ($provider['available']) {
                $costs[$id] = [
                    'provider' => $provider['name'],
                    'cost_usd' => round($emissionsTons * $provider['cost_per_ton'], 2),
                    'project_type' => $provider['project_type'],
                    'location' => $provider['location']
                ];
            }
        }
        return $costs;
    }

    protected function getEmissionComparison(float $emissionsKg): array
    {
        return [
            'equivalent_to' => [
                'km_driven_petrol_car' => round($emissionsKg / 0.21, 1),
                'hours_laptop_usage' => round($emissionsKg / 0.05, 1),
                'trees_needed_to_offset' => round($emissionsKg / 22, 2) // Average tree absorbs 22kg CO2/year
            ]
        ];
    }

    protected function calculateServerEmissions(Carbon $startDate, Carbon $endDate): array
    {
        $days = $startDate->diffInDays($endDate) + 1;
        $serverHours = $days * 24;
        $emissions = $serverHours * $this->emissionFactors['server_hour'];
        
        return [
            'total' => $emissions,
            'server_hours' => $serverHours,
            'emission_factor' => $this->emissionFactors['server_hour']
        ];
    }

    protected function calculateDataTransferEmissions(Carbon $startDate, Carbon $endDate): array
    {
        // Estimate data transfer based on platform usage
        $estimatedDataGB = 1000; // Placeholder
        $emissions = $estimatedDataGB * $this->emissionFactors['data_transfer_gb'];
        
        return [
            'total' => $emissions,
            'data_transfer_gb' => $estimatedDataGB,
            'emission_factor' => $this->emissionFactors['data_transfer_gb']
        ];
    }

    protected function calculateUserActivityEmissions(Carbon $startDate, Carbon $endDate): array
    {
        // Calculate based on user activity metrics
        $totalBookings = \App\Models\Booking::whereBetween('created_at', [$startDate, $endDate])->count();
        $emissions = $totalBookings * 0.5; // Estimated emissions per booking
        
        return [
            'total' => $emissions,
            'total_bookings' => $totalBookings,
            'emissions_per_booking' => 0.5
        ];
    }

    protected function calculateOperationsEmissions(Carbon $startDate, Carbon $endDate): array
    {
        $days = $startDate->diffInDays($endDate) + 1;
        $employees = 50; // Estimated employee count
        $emissions = $days * $employees * $this->emissionFactors['employee_day'];
        
        return [
            'total' => $emissions,
            'days' => $days,
            'employees' => $employees,
            'emission_factor' => $this->emissionFactors['employee_day']
        ];
    }

    protected function calculateCarbonIntensity(float $totalEmissions, Carbon $startDate, Carbon $endDate): array
    {
        $bookings = \App\Models\Booking::whereBetween('created_at', [$startDate, $endDate])->count();
        $users = \App\Models\User::whereBetween('created_at', [$startDate, $endDate])->count();
        
        return [
            'kg_per_booking' => $bookings > 0 ? round($totalEmissions / $bookings, 4) : 0,
            'kg_per_user' => $users > 0 ? round($totalEmissions / $users, 4) : 0,
            'kg_per_day' => round($totalEmissions / ($startDate->diffInDays($endDate) + 1), 2)
        ];
    }

    protected function storeOffsetRecord(int $userId, float $emissionsTons, float $cost, string $providerId, array $offsetData): void
    {
        // Store offset record in database
        \App\Models\CarbonOffset::create([
            'user_id' => $userId,
            'emissions_tons' => $emissionsTons,
            'cost_usd' => $cost,
            'provider_id' => $providerId,
            'offset_id' => $offsetData['offset_id'],
            'certificate_url' => $offsetData['certificate_url'] ?? null,
            'retirement_date' => $offsetData['retirement_date'] ?? null,
            'status' => 'completed'
        ]);
    }
}
