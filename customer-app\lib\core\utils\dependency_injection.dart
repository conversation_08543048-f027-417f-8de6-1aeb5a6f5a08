import 'package:get/get.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';

import '../network/api_client.dart';
import '../network/network_info.dart';
import '../../shared/services/auth_service.dart';
import '../../shared/services/settings_service.dart';
import '../../shared/services/notification_service.dart';
import '../../shared/services/location_service.dart';
import '../../shared/services/storage_service.dart';
import '../../shared/services/cache_service.dart';
import '../../shared/services/image_service.dart';
import '../../shared/services/payment_service.dart';
import '../../shared/services/analytics_service.dart';
import '../../features/auth/controllers/auth_controller.dart';
import '../../features/home/<USER>/home_controller.dart';
import '../../features/booking/controllers/booking_controller.dart';
import '../../features/profile/controllers/profile_controller.dart';
import '../../features/vip/controllers/vip_controller.dart';
import '../../features/store/controllers/store_controller.dart';
import '../../features/wallet/controllers/wallet_controller.dart';
import '../../features/loyalty/controllers/loyalty_controller.dart';
import '../../features/support/controllers/support_controller.dart';
import '../../features/notifications/controllers/notifications_controller.dart';

class DependencyInjection {
  static Future<void> init() async {
    // Initialize Core Dependencies
    await _initCore();
    
    // Initialize Network Dependencies
    await _initNetwork();
    
    // Initialize Storage Dependencies
    await _initStorage();
    
    // Initialize Services
    await _initServices();
    
    // Initialize Controllers
    await _initControllers();
  }

  static Future<void> _initCore() async {
    // Shared Preferences
    final sharedPreferences = await SharedPreferences.getInstance();
    Get.put<SharedPreferences>(sharedPreferences, permanent: true);
    
    // Hive Boxes
    await Hive.openBox('user_box');
    await Hive.openBox('settings_box');
    await Hive.openBox('cache_box');
    await Hive.openBox('booking_box');
  }

  static Future<void> _initNetwork() async {
    // Dio Instance
    final dio = Dio();
    Get.put<Dio>(dio, permanent: true);
    
    // Network Info
    Get.put<NetworkInfo>(NetworkInfoImpl(), permanent: true);
    
    // API Client
    Get.put<ApiClient>(
      ApiClient(Get.find<Dio>()),
      permanent: true,
    );
  }

  static Future<void> _initStorage() async {
    // Storage Service
    Get.put<StorageService>(
      StorageServiceImpl(Get.find<SharedPreferences>()),
      permanent: true,
    );
    
    // Cache Service
    Get.put<CacheService>(
      CacheServiceImpl(),
      permanent: true,
    );
  }

  static Future<void> _initServices() async {
    // Settings Service
    Get.put<SettingsService>(
      SettingsServiceImpl(
        apiClient: Get.find<ApiClient>(),
        storageService: Get.find<StorageService>(),
        cacheService: Get.find<CacheService>(),
      ),
      permanent: true,
    );
    
    // Auth Service
    Get.put<AuthService>(
      AuthServiceImpl(
        apiClient: Get.find<ApiClient>(),
        storageService: Get.find<StorageService>(),
        settingsService: Get.find<SettingsService>(),
      ),
      permanent: true,
    );
    
    // Location Service
    Get.put<LocationService>(
      LocationServiceImpl(
        storageService: Get.find<StorageService>(),
      ),
      permanent: true,
    );
    
    // Notification Service
    Get.put<NotificationService>(
      NotificationServiceImpl(
        storageService: Get.find<StorageService>(),
        settingsService: Get.find<SettingsService>(),
      ),
      permanent: true,
    );
    
    // Image Service
    Get.put<ImageService>(
      ImageServiceImpl(),
      permanent: true,
    );
    
    // Payment Service
    Get.put<PaymentService>(
      PaymentServiceImpl(
        apiClient: Get.find<ApiClient>(),
        settingsService: Get.find<SettingsService>(),
      ),
      permanent: true,
    );
    
    // Analytics Service
    Get.put<AnalyticsService>(
      AnalyticsServiceImpl(
        settingsService: Get.find<SettingsService>(),
      ),
      permanent: true,
    );
  }

  static Future<void> _initControllers() async {
    // Auth Controller
    Get.put<AuthController>(
      AuthController(
        authService: Get.find<AuthService>(),
        settingsService: Get.find<SettingsService>(),
        notificationService: Get.find<NotificationService>(),
      ),
      permanent: true,
    );
    
    // Home Controller
    Get.put<HomeController>(
      HomeController(
        authService: Get.find<AuthService>(),
        settingsService: Get.find<SettingsService>(),
        locationService: Get.find<LocationService>(),
        apiClient: Get.find<ApiClient>(),
      ),
      permanent: true,
    );
    
    // Booking Controller
    Get.put<BookingController>(
      BookingController(
        apiClient: Get.find<ApiClient>(),
        authService: Get.find<AuthService>(),
        locationService: Get.find<LocationService>(),
        paymentService: Get.find<PaymentService>(),
        notificationService: Get.find<NotificationService>(),
      ),
      permanent: true,
    );
    
    // Profile Controller
    Get.put<ProfileController>(
      ProfileController(
        authService: Get.find<AuthService>(),
        apiClient: Get.find<ApiClient>(),
        imageService: Get.find<ImageService>(),
        storageService: Get.find<StorageService>(),
      ),
      permanent: true,
    );
    
    // VIP Controller
    Get.put<VipController>(
      VipController(
        apiClient: Get.find<ApiClient>(),
        authService: Get.find<AuthService>(),
        paymentService: Get.find<PaymentService>(),
        settingsService: Get.find<SettingsService>(),
      ),
      permanent: true,
    );
    
    // Store Controller
    Get.put<StoreController>(
      StoreController(
        apiClient: Get.find<ApiClient>(),
        authService: Get.find<AuthService>(),
        cacheService: Get.find<CacheService>(),
        settingsService: Get.find<SettingsService>(),
      ),
      permanent: true,
    );
    
    // Wallet Controller
    Get.put<WalletController>(
      WalletController(
        apiClient: Get.find<ApiClient>(),
        authService: Get.find<AuthService>(),
        paymentService: Get.find<PaymentService>(),
      ),
      permanent: true,
    );
    
    // Loyalty Controller
    Get.put<LoyaltyController>(
      LoyaltyController(
        apiClient: Get.find<ApiClient>(),
        authService: Get.find<AuthService>(),
        settingsService: Get.find<SettingsService>(),
      ),
      permanent: true,
    );
    
    // Support Controller
    Get.put<SupportController>(
      SupportController(
        apiClient: Get.find<ApiClient>(),
        authService: Get.find<AuthService>(),
      ),
      permanent: true,
    );
    
    // Notifications Controller
    Get.put<NotificationsController>(
      NotificationsController(
        apiClient: Get.find<ApiClient>(),
        authService: Get.find<AuthService>(),
        notificationService: Get.find<NotificationService>(),
      ),
      permanent: true,
    );
  }

  // Utility Methods
  static T find<T>() => Get.find<T>();
  
  static void reset() {
    Get.reset();
  }
  
  static void delete<T>() {
    Get.delete<T>();
  }
  
  static bool isRegistered<T>() {
    return Get.isRegistered<T>();
  }
}
