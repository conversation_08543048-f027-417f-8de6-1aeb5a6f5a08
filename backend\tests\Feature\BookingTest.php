<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Barber;
use App\Models\Service;
use App\Models\Booking;
use App\Models\City;
use App\Models\ServiceCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

class BookingTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $customer;
    protected $barber;
    protected $barberUser;
    protected $service;
    protected $city;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->city = City::factory()->create();
        
        $this->customer = User::factory()->create([
            'email' => '<EMAIL>',
            'phone' => '+201234567890'
        ]);

        $this->barberUser = User::factory()->create([
            'email' => '<EMAIL>',
            'phone' => '+201234567891'
        ]);

        $this->barber = Barber::factory()->create([
            'user_id' => $this->barberUser->id,
            'is_verified' => true,
            'is_available' => true
        ]);

        $category = ServiceCategory::factory()->create();
        $this->service = Service::factory()->create([
            'category_id' => $category->id,
            'base_price' => 50.00
        ]);

        // Associate service with barber
        $this->barber->services()->attach($this->service->id);
    }

    /** @test */
    public function customer_can_create_booking()
    {
        Sanctum::actingAs($this->customer);

        $bookingData = [
            'barber_id' => $this->barber->id,
            'services' => [$this->service->id],
            'scheduled_date' => now()->addDays(1)->format('Y-m-d'),
            'scheduled_time' => '14:00',
            'customer_address' => '123 Test Street, Cairo',
            'customer_latitude' => 30.0444,
            'customer_longitude' => 31.2357,
            'notes' => 'Please be on time'
        ];

        $response = $this->postJson('/api/bookings', $bookingData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'booking_number',
                        'status',
                        'total_amount',
                        'scheduled_date',
                        'scheduled_time'
                    ]
                ]);

        $this->assertDatabaseHas('bookings', [
            'customer_id' => $this->customer->id,
            'barber_id' => $this->barber->id,
            'status' => 'pending'
        ]);
    }

    /** @test */
    public function booking_requires_valid_barber()
    {
        Sanctum::actingAs($this->customer);

        $bookingData = [
            'barber_id' => 999, // Non-existent barber
            'services' => [$this->service->id],
            'scheduled_date' => now()->addDays(1)->format('Y-m-d'),
            'scheduled_time' => '14:00',
            'customer_address' => '123 Test Street, Cairo'
        ];

        $response = $this->postJson('/api/bookings', $bookingData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['barber_id']);
    }

    /** @test */
    public function booking_requires_future_date()
    {
        Sanctum::actingAs($this->customer);

        $bookingData = [
            'barber_id' => $this->barber->id,
            'services' => [$this->service->id],
            'scheduled_date' => now()->subDays(1)->format('Y-m-d'), // Past date
            'scheduled_time' => '14:00',
            'customer_address' => '123 Test Street, Cairo'
        ];

        $response = $this->postJson('/api/bookings', $bookingData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['scheduled_date']);
    }

    /** @test */
    public function customer_can_view_their_bookings()
    {
        Sanctum::actingAs($this->customer);

        // Create test bookings
        $booking1 = Booking::factory()->create([
            'customer_id' => $this->customer->id,
            'barber_id' => $this->barber->id
        ]);

        $booking2 = Booking::factory()->create([
            'customer_id' => $this->customer->id,
            'barber_id' => $this->barber->id
        ]);

        $response = $this->getJson('/api/bookings');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'booking_number',
                            'status',
                            'total_amount',
                            'barber' => [
                                'id',
                                'user' => ['name']
                            ]
                        ]
                    ]
                ]);

        $this->assertCount(2, $response->json('data'));
    }

    /** @test */
    public function customer_can_cancel_booking()
    {
        Sanctum::actingAs($this->customer);

        $booking = Booking::factory()->create([
            'customer_id' => $this->customer->id,
            'barber_id' => $this->barber->id,
            'status' => 'confirmed',
            'scheduled_date' => now()->addDays(1),
            'scheduled_time' => '14:00'
        ]);

        $response = $this->putJson("/api/bookings/{$booking->id}/cancel", [
            'reason' => 'Change of plans'
        ]);

        $response->assertStatus(200);

        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'cancelled',
            'cancellation_reason' => 'Change of plans'
        ]);
    }

    /** @test */
    public function barber_can_view_their_bookings()
    {
        Sanctum::actingAs($this->barberUser);

        $booking = Booking::factory()->create([
            'customer_id' => $this->customer->id,
            'barber_id' => $this->barber->id
        ]);

        $response = $this->getJson('/api/barber/bookings');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'booking_number',
                            'status',
                            'customer' => ['name']
                        ]
                    ]
                ]);
    }

    /** @test */
    public function barber_can_confirm_booking()
    {
        Sanctum::actingAs($this->barberUser);

        $booking = Booking::factory()->create([
            'customer_id' => $this->customer->id,
            'barber_id' => $this->barber->id,
            'status' => 'pending'
        ]);

        $response = $this->putJson("/api/barber/bookings/{$booking->id}/confirm");

        $response->assertStatus(200);

        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'confirmed'
        ]);
    }

    /** @test */
    public function barber_can_start_service()
    {
        Sanctum::actingAs($this->barberUser);

        $booking = Booking::factory()->create([
            'customer_id' => $this->customer->id,
            'barber_id' => $this->barber->id,
            'status' => 'confirmed'
        ]);

        $response = $this->putJson("/api/barber/bookings/{$booking->id}/start");

        $response->assertStatus(200);

        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'in_progress'
        ]);
    }

    /** @test */
    public function barber_can_complete_service()
    {
        Sanctum::actingAs($this->barberUser);

        $booking = Booking::factory()->create([
            'customer_id' => $this->customer->id,
            'barber_id' => $this->barber->id,
            'status' => 'in_progress'
        ]);

        $response = $this->putJson("/api/barber/bookings/{$booking->id}/complete");

        $response->assertStatus(200);

        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function booking_calculates_correct_total_amount()
    {
        Sanctum::actingAs($this->customer);

        // Create additional service
        $service2 = Service::factory()->create([
            'base_price' => 30.00
        ]);
        $this->barber->services()->attach($service2->id);

        $bookingData = [
            'barber_id' => $this->barber->id,
            'services' => [$this->service->id, $service2->id],
            'scheduled_date' => now()->addDays(1)->format('Y-m-d'),
            'scheduled_time' => '14:00',
            'customer_address' => '123 Test Street, Cairo'
        ];

        $response = $this->postJson('/api/bookings', $bookingData);

        $response->assertStatus(201);

        $booking = Booking::latest()->first();
        $expectedTotal = $this->service->base_price + $service2->base_price;

        $this->assertEquals($expectedTotal, $booking->total_amount);
    }

    /** @test */
    public function cannot_book_unavailable_barber()
    {
        Sanctum::actingAs($this->customer);

        // Make barber unavailable
        $this->barber->update(['is_available' => false]);

        $bookingData = [
            'barber_id' => $this->barber->id,
            'services' => [$this->service->id],
            'scheduled_date' => now()->addDays(1)->format('Y-m-d'),
            'scheduled_time' => '14:00',
            'customer_address' => '123 Test Street, Cairo'
        ];

        $response = $this->postJson('/api/bookings', $bookingData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['barber_id']);
    }

    /** @test */
    public function cannot_book_service_not_offered_by_barber()
    {
        Sanctum::actingAs($this->customer);

        // Create service not offered by barber
        $otherService = Service::factory()->create();

        $bookingData = [
            'barber_id' => $this->barber->id,
            'services' => [$otherService->id],
            'scheduled_date' => now()->addDays(1)->format('Y-m-d'),
            'scheduled_time' => '14:00',
            'customer_address' => '123 Test Street, Cairo'
        ];

        $response = $this->postJson('/api/bookings', $bookingData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['services.0']);
    }

    /** @test */
    public function booking_generates_unique_booking_number()
    {
        Sanctum::actingAs($this->customer);

        $bookingData = [
            'barber_id' => $this->barber->id,
            'services' => [$this->service->id],
            'scheduled_date' => now()->addDays(1)->format('Y-m-d'),
            'scheduled_time' => '14:00',
            'customer_address' => '123 Test Street, Cairo'
        ];

        $response1 = $this->postJson('/api/bookings', $bookingData);
        $response2 = $this->postJson('/api/bookings', $bookingData);

        $booking1 = Booking::find($response1->json('data.id'));
        $booking2 = Booking::find($response2->json('data.id'));

        $this->assertNotEquals($booking1->booking_number, $booking2->booking_number);
    }

    /** @test */
    public function cannot_cancel_booking_too_close_to_appointment()
    {
        Sanctum::actingAs($this->customer);

        $booking = Booking::factory()->create([
            'customer_id' => $this->customer->id,
            'barber_id' => $this->barber->id,
            'status' => 'confirmed',
            'scheduled_date' => now()->format('Y-m-d'),
            'scheduled_time' => now()->addHour()->format('H:i') // 1 hour from now
        ]);

        $response = $this->putJson("/api/bookings/{$booking->id}/cancel", [
            'reason' => 'Change of plans'
        ]);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'لا يمكن إلغاء الحجز قبل أقل من ساعتين من الموعد'
                ]);
    }
}
