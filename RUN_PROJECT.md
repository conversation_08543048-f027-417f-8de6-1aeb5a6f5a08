# 🚀 **تشغيل مشروع "حلاق على بابك" - دليل سريع**

---

## ⚡ **التشغيل السريع (خطوة واحدة)**

### 🖥️ **Windows**
```bash
# تشغيل السكريبت التلقائي
start.bat
```

### 🐧 **Linux/Mac**
```bash
# إعطاء صلاحية التنفيذ
chmod +x start.sh

# تشغيل السكريبت
./start.sh
```

---

## 📋 **المتطلبات الأساسية**

### ✅ **تأكد من تثبيت:**
- **PHP 8.1+** مع extensions: mbstring, xml, curl, zip, gd, mysql
- **Composer 2.0+**
- **Node.js 18.0+** مع npm
- **MySQL 8.0+** أو MariaDB 10.3+
- **Git** (اختياري)

### 🔧 **تثبيت المتطلبات:**

#### Windows:
```bash
# تحميل وتثبيت XAMPP (يحتوي على PHP + MySQL)
# من: https://www.apachefriends.org/

# تحميل وتثبيت Node.js
# من: https://nodejs.org/

# تحميل وتثبيت Composer
# من: https://getcomposer.org/
```

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install php8.1 php8.1-mysql php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip php8.1-gd
sudo apt install mysql-server nodejs npm composer
```

#### macOS:
```bash
# تثبيت Homebrew أولاً
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# تثبيت المتطلبات
brew install php@8.1 mysql node composer
```

---

## 🗄️ **إعداد قاعدة البيانات**

### 1️⃣ **تشغيل MySQL**
```bash
# Windows (XAMPP)
# تشغيل XAMPP Control Panel وتفعيل MySQL

# Linux
sudo systemctl start mysql

# macOS
brew services start mysql
```

### 2️⃣ **إنشاء قاعدة البيانات**
```bash
# الطريقة الأولى: استخدام السكريبت الجاهز
mysql -u root -p < setup-database.sql

# الطريقة الثانية: يدوياً
mysql -u root -p
CREATE DATABASE barber_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
exit
```

---

## 🏗️ **التشغيل اليدوي (خطوة بخطوة)**

### 1️⃣ **Backend (Laravel)**
```bash
# الانتقال لمجلد Backend
cd backend

# تثبيت التبعيات
composer install

# إعداد البيئة
cp .env.example .env
php artisan key:generate

# إعداد قاعدة البيانات في .env
# DB_DATABASE=barber_app
# DB_USERNAME=root
# DB_PASSWORD=your_password

# تشغيل المايجريشن
php artisan migrate
php artisan db:seed

# إنشاء رابط التخزين
php artisan storage:link

# تشغيل الخادم
php artisan serve --port=8000
```

### 2️⃣ **Admin Dashboard (Vue.js)**
```bash
# فتح terminal جديد
cd admin-dashboard

# تثبيت التبعيات
npm install

# إعداد البيئة
cp .env.example .env

# تشغيل خادم التطوير
npm run dev
```

---

## 🌐 **الوصول للنظام**

### 🔗 **الروابط:**
- **🖥️ لوحة الإدارة**: http://localhost:3000
- **📡 Backend API**: http://localhost:8000
- **📚 توثيق API**: http://localhost:8000/api/documentation

### 🔐 **بيانات تسجيل الدخول:**
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123

---

## 📱 **تشغيل التطبيقات المحمولة (اختياري)**

### 🔧 **متطلبات Flutter:**
```bash
# تثبيت Flutter SDK
# من: https://flutter.dev/docs/get-started/install

# التحقق من التثبيت
flutter doctor
```

### 📱 **تطبيق العملاء:**
```bash
cd mobile-apps/customer-app
flutter pub get
flutter run -d chrome  # للويب
flutter run             # للهاتف (يتطلب محاكي أو جهاز متصل)
```

---

## 🔍 **استكشاف الأخطاء الشائعة**

### ❌ **خطأ: "Class not found"**
```bash
cd backend
composer dump-autoload
php artisan config:clear
php artisan cache:clear
```

### ❌ **خطأ: "Permission denied"**
```bash
# Linux/Mac
sudo chown -R $USER:$USER storage bootstrap/cache
chmod -R 775 storage bootstrap/cache

# Windows
# تشغيل CMD كمدير وتنفيذ:
icacls storage /grant Everyone:F /T
```

### ❌ **خطأ: "Port already in use"**
```bash
# تغيير المنفذ
php artisan serve --port=8001  # للـ backend
npm run dev -- --port 3001     # للـ admin dashboard
```

### ❌ **خطأ: "Database connection failed"**
```bash
# التحقق من إعدادات .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=barber_app
DB_USERNAME=root
DB_PASSWORD=your_password

# إعادة تشغيل MySQL
sudo systemctl restart mysql  # Linux
brew services restart mysql   # macOS
```

---

## 🎯 **الخطوات التالية بعد التشغيل**

### ✅ **قائمة المراجعة:**
1. **✅ تسجيل الدخول للوحة الإدارة**
2. **✅ إضافة مدن ومناطق جديدة**
3. **✅ إضافة خدمات إضافية**
4. **✅ إنشاء حسابات حلاقين تجريبية**
5. **✅ اختبار عملية الحجز**
6. **✅ مراجعة الإعدادات العامة**

### 🔧 **إعدادات إضافية:**
- **📧 إعداد البريد الإلكتروني** (Gmail SMTP)
- **📱 إعداد الإشعارات** (Firebase)
- **💳 إعداد أنظمة الدفع** (Stripe, PayMob)
- **🗺️ إعداد خرائط Google**

---

## 📞 **الدعم التقني**

### 🆘 **في حالة مواجهة مشاكل:**
1. **راجع ملف QUICK_START_GUIDE.md** للتفاصيل الكاملة
2. **تحقق من ملفات اللوج:**
   - Backend: `backend/storage/logs/laravel.log`
   - Admin: متصفح Developer Tools → Console
3. **تأكد من تشغيل جميع الخدمات المطلوبة**

### 📋 **معلومات مفيدة:**
- **إصدار PHP**: `php --version`
- **إصدار Composer**: `composer --version`
- **إصدار Node.js**: `node --version`
- **إصدار MySQL**: `mysql --version`

---

## 🎉 **مبروك!**

**إذا ظهرت لك لوحة الإدارة على http://localhost:3000 فقد تم تشغيل المشروع بنجاح! 🚀**

**الآن يمكنك البدء في استكشاف جميع الميزات المتقدمة للنظام.**

---

**💡 نصيحة**: احفظ هذا الملف كمرجع سريع للتشغيل في المستقبل!
