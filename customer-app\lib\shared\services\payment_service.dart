import 'package:dio/dio.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:get/get.dart';

import '../../core/constants/app_constants.dart';
import '../../core/network/api_client.dart';
import '../../core/network/api_endpoints.dart';
import '../models/payment_model.dart';
import '../models/wallet_model.dart';

abstract class PaymentService {
  Future<void> initialize();
  Future<List<PaymentMethod>> getPaymentMethods();
  Future<PaymentResult> processPayment({
    required String bookingId,
    required double amount,
    required String paymentMethod,
    Map<String, dynamic>? additionalData,
  });
  Future<PaymentResult> processWalletPayment({
    required String bookingId,
    required double amount,
  });
  Future<PaymentResult> processStripePayment({
    required String bookingId,
    required double amount,
    required String paymentMethodId,
  });
  Future<PaymentResult> processPaymobPayment({
    required String bookingId,
    required double amount,
    required String paymentMethod,
  });
  Future<WalletModel> getWalletBalance();
  Future<bool> addToWallet({
    required double amount,
    required String paymentMethod,
  });
  Future<List<TransactionModel>> getTransactionHistory({
    int page = 1,
    int limit = 20,
  });
  Future<bool> refundPayment(String transactionId);
}

class PaymentServiceImpl implements PaymentService {
  final ApiClient _apiClient;
  
  List<PaymentMethod> _paymentMethods = [];
  WalletModel? _walletData;

  PaymentServiceImpl({
    required ApiClient apiClient,
  }) : _apiClient = apiClient;

  @override
  Future<void> initialize() async {
    try {
      // Initialize Stripe
      await _initializeStripe();
      
      // Load payment methods
      await getPaymentMethods();
      
      // Load wallet data
      await getWalletBalance();
    } catch (e) {
      // Handle initialization errors
    }
  }

  Future<void> _initializeStripe() async {
    try {
      // Get Stripe publishable key from API
      final response = await _apiClient.get(ApiEndpoints.stripeConfig);
      
      if (response.statusCode == 200) {
        final config = response.data['data'];
        final publishableKey = config['publishable_key'];
        
        Stripe.publishableKey = publishableKey;
        await Stripe.instance.applySettings();
      }
    } catch (e) {
      // Handle Stripe initialization error
    }
  }

  @override
  Future<List<PaymentMethod>> getPaymentMethods() async {
    try {
      final response = await _apiClient.get(ApiEndpoints.paymentMethods);
      
      if (response.statusCode == 200) {
        final List<dynamic> methodsJson = response.data['data'];
        _paymentMethods = methodsJson
            .map((json) => PaymentMethod.fromJson(json))
            .toList();
        
        return _paymentMethods;
      }
      
      return _paymentMethods;
    } catch (e) {
      throw PaymentException('فشل في جلب طرق الدفع: ${_handleError(e)}');
    }
  }

  @override
  Future<PaymentResult> processPayment({
    required String bookingId,
    required double amount,
    required String paymentMethod,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      switch (paymentMethod.toLowerCase()) {
        case 'cash':
          return await _processCashPayment(bookingId, amount);
        
        case 'wallet':
          return await processWalletPayment(
            bookingId: bookingId,
            amount: amount,
          );
        
        case 'stripe':
          final paymentMethodId = additionalData?['payment_method_id'];
          if (paymentMethodId == null) {
            throw PaymentException('معرف طريقة الدفع مطلوب لـ Stripe');
          }
          return await processStripePayment(
            bookingId: bookingId,
            amount: amount,
            paymentMethodId: paymentMethodId,
          );
        
        case 'paymob_card':
        case 'paymob_wallet':
        case 'paymob_kiosk':
          return await processPaymobPayment(
            bookingId: bookingId,
            amount: amount,
            paymentMethod: paymentMethod,
          );
        
        default:
          throw PaymentException('طريقة دفع غير مدعومة');
      }
    } catch (e) {
      return PaymentResult(
        success: false,
        message: e is PaymentException ? e.message : _handleError(e),
      );
    }
  }

  Future<PaymentResult> _processCashPayment(String bookingId, double amount) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.processCashPayment,
        data: {
          'booking_id': bookingId,
          'amount': amount,
        },
      );

      if (response.statusCode == 200) {
        return PaymentResult(
          success: true,
          message: 'تم تأكيد الدفع نقداً',
          transactionId: response.data['data']['transaction_id'],
        );
      }

      return PaymentResult(
        success: false,
        message: response.data['message'] ?? 'فشل في معالجة الدفع النقدي',
      );
    } catch (e) {
      throw PaymentException('فشل في معالجة الدفع النقدي: ${_handleError(e)}');
    }
  }

  @override
  Future<PaymentResult> processWalletPayment({
    required String bookingId,
    required double amount,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.processWalletPayment,
        data: {
          'booking_id': bookingId,
          'amount': amount,
        },
      );

      if (response.statusCode == 200) {
        // Update wallet balance
        await getWalletBalance();
        
        return PaymentResult(
          success: true,
          message: 'تم الدفع من المحفظة بنجاح',
          transactionId: response.data['data']['transaction_id'],
        );
      }

      return PaymentResult(
        success: false,
        message: response.data['message'] ?? 'فشل في الدفع من المحفظة',
      );
    } catch (e) {
      throw PaymentException('فشل في الدفع من المحفظة: ${_handleError(e)}');
    }
  }

  @override
  Future<PaymentResult> processStripePayment({
    required String bookingId,
    required double amount,
    required String paymentMethodId,
  }) async {
    try {
      // Create payment intent
      final response = await _apiClient.post(
        ApiEndpoints.createStripePaymentIntent,
        data: {
          'booking_id': bookingId,
          'amount': amount,
          'payment_method_id': paymentMethodId,
        },
      );

      if (response.statusCode == 200) {
        final paymentIntentData = response.data['data'];
        final clientSecret = paymentIntentData['client_secret'];
        
        // Confirm payment with Stripe
        final paymentIntent = await Stripe.instance.confirmPayment(
          paymentIntentClientSecret: clientSecret,
          data: PaymentMethodParams.card(
            paymentMethodData: PaymentMethodData(),
          ),
        );

        if (paymentIntent.status == PaymentIntentsStatus.Succeeded) {
          // Confirm payment on server
          final confirmResponse = await _apiClient.post(
            ApiEndpoints.confirmStripePayment,
            data: {
              'booking_id': bookingId,
              'payment_intent_id': paymentIntent.id,
            },
          );

          if (confirmResponse.statusCode == 200) {
            return PaymentResult(
              success: true,
              message: 'تم الدفع بنجاح',
              transactionId: confirmResponse.data['data']['transaction_id'],
            );
          }
        }
      }

      return PaymentResult(
        success: false,
        message: 'فشل في معالجة الدفع',
      );
    } catch (e) {
      throw PaymentException('فشل في الدفع عبر Stripe: ${_handleError(e)}');
    }
  }

  @override
  Future<PaymentResult> processPaymobPayment({
    required String bookingId,
    required double amount,
    required String paymentMethod,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.createPaymobPayment,
        data: {
          'booking_id': bookingId,
          'amount': amount,
          'payment_method': paymentMethod,
        },
      );

      if (response.statusCode == 200) {
        final paymentData = response.data['data'];
        
        return PaymentResult(
          success: true,
          message: 'تم إنشاء رابط الدفع',
          paymentUrl: paymentData['payment_url'],
          transactionId: paymentData['transaction_id'],
        );
      }

      return PaymentResult(
        success: false,
        message: response.data['message'] ?? 'فشل في إنشاء رابط الدفع',
      );
    } catch (e) {
      throw PaymentException('فشل في الدفع عبر Paymob: ${_handleError(e)}');
    }
  }

  @override
  Future<WalletModel> getWalletBalance() async {
    try {
      final response = await _apiClient.get(ApiEndpoints.walletBalance);
      
      if (response.statusCode == 200) {
        _walletData = WalletModel.fromJson(response.data['data']);
        return _walletData!;
      }
      
      return _walletData ?? WalletModel.empty();
    } catch (e) {
      throw PaymentException('فشل في جلب رصيد المحفظة: ${_handleError(e)}');
    }
  }

  @override
  Future<bool> addToWallet({
    required double amount,
    required String paymentMethod,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.addToWallet,
        data: {
          'amount': amount,
          'payment_method': paymentMethod,
        },
      );

      if (response.statusCode == 200) {
        // Refresh wallet balance
        await getWalletBalance();
        return true;
      }

      return false;
    } catch (e) {
      throw PaymentException('فشل في إضافة رصيد للمحفظة: ${_handleError(e)}');
    }
  }

  @override
  Future<List<TransactionModel>> getTransactionHistory({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        ApiEndpoints.transactionHistory,
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> transactionsJson = response.data['data'];
        return transactionsJson
            .map((json) => TransactionModel.fromJson(json))
            .toList();
      }

      return [];
    } catch (e) {
      throw PaymentException('فشل في جلب تاريخ المعاملات: ${_handleError(e)}');
    }
  }

  @override
  Future<bool> refundPayment(String transactionId) async {
    try {
      final response = await _apiClient.post(
        '${ApiEndpoints.refundPayment}/$transactionId',
      );

      return response.statusCode == 200;
    } catch (e) {
      throw PaymentException('فشل في استرداد المبلغ: ${_handleError(e)}');
    }
  }

  String _handleError(dynamic error) {
    if (error is DioException) {
      return error.response?.data['message'] ?? AppConstants.networkError;
    }
    
    return AppConstants.unknownError;
  }
}

class PaymentResult {
  final bool success;
  final String message;
  final String? transactionId;
  final String? paymentUrl;

  PaymentResult({
    required this.success,
    required this.message,
    this.transactionId,
    this.paymentUrl,
  });
}

class PaymentException implements Exception {
  final String message;
  
  PaymentException(this.message);
  
  @override
  String toString() => message;
}
