import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const token = ref(localStorage.getItem('admin_token') || null)
  const permissions = ref([])
  const loading = ref(false)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role?.name || null)
  const userName = computed(() => user.value?.name || '')
  const userAvatar = computed(() => user.value?.avatar || '/default-avatar.png')

  // Actions
  const login = async (credentials) => {
    loading.value = true
    try {
      const response = await axios.post('/api/v1/auth/admin/login', credentials)
      
      if (response.data.success) {
        const { user: userData, token: authToken, permissions: userPermissions } = response.data.data
        
        // Set user data
        user.value = userData
        token.value = authToken
        permissions.value = userPermissions || []
        
        // Store token in localStorage
        localStorage.setItem('admin_token', authToken)
        
        // Set default authorization header
        axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
        
        return { success: true }
      } else {
        throw new Error(response.data.message || 'فشل في تسجيل الدخول')
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'حدث خطأ أثناء تسجيل الدخول'
      }
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    loading.value = true
    try {
      // Call logout API
      await axios.post('/api/v1/auth/admin/logout')
    } catch (error) {
      console.error('Logout API error:', error)
    } finally {
      // Clear local data regardless of API response
      clearAuthData()
      loading.value = false
    }
  }

  const clearAuthData = () => {
    user.value = null
    token.value = null
    permissions.value = []
    localStorage.removeItem('admin_token')
    delete axios.defaults.headers.common['Authorization']
  }

  const restoreSession = async () => {
    const storedToken = localStorage.getItem('admin_token')
    if (!storedToken) return false

    loading.value = true
    try {
      // Set token for the request
      axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`
      
      // Verify token and get user data
      const response = await axios.get('/api/v1/auth/admin/me')
      
      if (response.data.success) {
        const { user: userData, permissions: userPermissions } = response.data.data
        
        user.value = userData
        token.value = storedToken
        permissions.value = userPermissions || []
        
        return true
      } else {
        clearAuthData()
        return false
      }
    } catch (error) {
      console.error('Session restore error:', error)
      clearAuthData()
      return false
    } finally {
      loading.value = false
    }
  }

  const updateProfile = async (profileData) => {
    loading.value = true
    try {
      const response = await axios.put('/api/v1/auth/admin/profile', profileData)
      
      if (response.data.success) {
        user.value = { ...user.value, ...response.data.data }
        return { success: true }
      } else {
        throw new Error(response.data.message || 'فشل في تحديث الملف الشخصي')
      }
    } catch (error) {
      console.error('Profile update error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'حدث خطأ أثناء تحديث الملف الشخصي'
      }
    } finally {
      loading.value = false
    }
  }

  const changePassword = async (passwordData) => {
    loading.value = true
    try {
      const response = await axios.put('/api/v1/auth/admin/change-password', passwordData)
      
      if (response.data.success) {
        return { success: true, message: 'تم تغيير كلمة المرور بنجاح' }
      } else {
        throw new Error(response.data.message || 'فشل في تغيير كلمة المرور')
      }
    } catch (error) {
      console.error('Password change error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'حدث خطأ أثناء تغيير كلمة المرور'
      }
    } finally {
      loading.value = false
    }
  }

  const hasPermission = (permission) => {
    if (!permissions.value || permissions.value.length === 0) return false
    
    // Super admin has all permissions
    if (userRole.value === 'super_admin') return true
    
    // Check if user has specific permission
    return permissions.value.includes(permission)
  }

  const hasAnyPermission = (permissionList) => {
    return permissionList.some(permission => hasPermission(permission))
  }

  const hasRole = (role) => {
    return userRole.value === role
  }

  // Initialize axios interceptors
  const initializeInterceptors = () => {
    // Request interceptor
    axios.interceptors.request.use(
      (config) => {
        if (token.value) {
          config.headers.Authorization = `Bearer ${token.value}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          clearAuthData()
          window.location.href = '/login'
        }
        return Promise.reject(error)
      }
    )
  }

  // Initialize on store creation
  initializeInterceptors()

  return {
    // State
    user,
    token,
    permissions,
    loading,
    
    // Getters
    isAuthenticated,
    userRole,
    userName,
    userAvatar,
    
    // Actions
    login,
    logout,
    clearAuthData,
    restoreSession,
    updateProfile,
    changePassword,
    hasPermission,
    hasAnyPermission,
    hasRole
  }
})
