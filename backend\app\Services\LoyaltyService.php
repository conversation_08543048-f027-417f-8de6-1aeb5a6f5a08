<?php

namespace App\Services;

use App\Models\User;
use App\Models\Booking;
use App\Models\LoyaltyPoint;
use App\Models\LoyaltyReward;
use App\Models\LoyaltyTier;
use App\Models\UserLoyalty;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class LoyaltyService
{
    /**
     * Award points for completed booking
     */
    public function awardPointsForBooking(Booking $booking)
    {
        $user = $booking->customer;
        $pointsEarned = $this->calculateBookingPoints($booking);
        
        if ($pointsEarned > 0) {
            $this->addPoints($user, $pointsEarned, 'booking_completed', [
                'booking_id' => $booking->id,
                'amount_spent' => $booking->total_amount
            ]);

            // Check for tier upgrade
            $this->checkTierUpgrade($user);

            // Send notification
            $this->sendPointsEarnedNotification($user, $pointsEarned, $booking);
        }

        return $pointsEarned;
    }

    /**
     * Add loyalty points to user account
     */
    public function addPoints(User $user, int $points, string $source, array $metadata = [])
    {
        DB::beginTransaction();
        
        try {
            // Create points record
            LoyaltyPoint::create([
                'user_id' => $user->id,
                'points' => $points,
                'source' => $source,
                'metadata' => $metadata,
                'expires_at' => now()->addYear(), // Points expire after 1 year
                'earned_at' => now()
            ]);

            // Update user's total points
            $userLoyalty = $this->getUserLoyalty($user);
            $userLoyalty->increment('total_points', $points);
            $userLoyalty->increment('available_points', $points);

            // Update lifetime stats
            $userLoyalty->increment('lifetime_points', $points);
            $userLoyalty->touch();

            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Redeem points for rewards
     */
    public function redeemPoints(User $user, LoyaltyReward $reward)
    {
        $userLoyalty = $this->getUserLoyalty($user);
        
        if ($userLoyalty->available_points < $reward->points_required) {
            return [
                'success' => false,
                'error' => 'نقاط غير كافية للاستبدال'
            ];
        }

        if (!$reward->is_active || ($reward->expires_at && $reward->expires_at < now())) {
            return [
                'success' => false,
                'error' => 'هذه المكافأة غير متاحة حالياً'
            ];
        }

        DB::beginTransaction();
        
        try {
            // Deduct points
            $this->deductPoints($user, $reward->points_required, 'reward_redemption', [
                'reward_id' => $reward->id,
                'reward_name' => $reward->name
            ]);

            // Create redemption record
            $redemption = $user->loyaltyRedemptions()->create([
                'reward_id' => $reward->id,
                'points_used' => $reward->points_required,
                'status' => 'pending',
                'redeemed_at' => now()
            ]);

            // Process reward based on type
            $result = $this->processRewardRedemption($user, $reward, $redemption);

            DB::commit();

            // Send confirmation notification
            $this->sendRedemptionNotification($user, $reward, $redemption);

            return [
                'success' => true,
                'redemption' => $redemption,
                'result' => $result
            ];

        } catch (\Exception $e) {
            DB::rollback();
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get user's loyalty information
     */
    public function getUserLoyalty(User $user)
    {
        return UserLoyalty::firstOrCreate(
            ['user_id' => $user->id],
            [
                'total_points' => 0,
                'available_points' => 0,
                'lifetime_points' => 0,
                'tier_id' => $this->getBaseTier()->id,
                'tier_achieved_at' => now()
            ]
        );
    }

    /**
     * Get user's loyalty tier
     */
    public function getUserTier(User $user)
    {
        $userLoyalty = $this->getUserLoyalty($user);
        return $userLoyalty->tier;
    }

    /**
     * Check and upgrade user tier if eligible
     */
    public function checkTierUpgrade(User $user)
    {
        $userLoyalty = $this->getUserLoyalty($user);
        $currentTier = $userLoyalty->tier;
        
        $eligibleTier = LoyaltyTier::where('points_required', '<=', $userLoyalty->lifetime_points)
            ->where('level', '>', $currentTier->level)
            ->orderBy('level', 'desc')
            ->first();

        if ($eligibleTier) {
            $userLoyalty->update([
                'tier_id' => $eligibleTier->id,
                'tier_achieved_at' => now()
            ]);

            // Award tier upgrade bonus
            $this->awardTierUpgradeBonus($user, $eligibleTier);

            // Send tier upgrade notification
            $this->sendTierUpgradeNotification($user, $eligibleTier);

            return $eligibleTier;
        }

        return null;
    }

    /**
     * Get available rewards for user
     */
    public function getAvailableRewards(User $user)
    {
        $userLoyalty = $this->getUserLoyalty($user);
        $userTier = $userLoyalty->tier;

        return LoyaltyReward::where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->where(function ($query) use ($userTier) {
                $query->whereNull('tier_required')
                      ->orWhere('tier_required', '<=', $userTier->level);
            })
            ->where('points_required', '<=', $userLoyalty->available_points + 1000) // Show rewards within reach
            ->orderBy('points_required')
            ->get()
            ->map(function ($reward) use ($userLoyalty) {
                $reward->can_redeem = $userLoyalty->available_points >= $reward->points_required;
                $reward->points_needed = max(0, $reward->points_required - $userLoyalty->available_points);
                return $reward;
            });
    }

    /**
     * Get loyalty statistics for user
     */
    public function getLoyaltyStats(User $user)
    {
        $userLoyalty = $this->getUserLoyalty($user);
        $currentTier = $userLoyalty->tier;
        $nextTier = LoyaltyTier::where('level', '>', $currentTier->level)
            ->orderBy('level')
            ->first();

        $pointsHistory = LoyaltyPoint::where('user_id', $user->id)
            ->where('earned_at', '>', now()->subDays(30))
            ->orderBy('earned_at', 'desc')
            ->get();

        $redemptionHistory = $user->loyaltyRedemptions()
            ->with('reward')
            ->orderBy('redeemed_at', 'desc')
            ->limit(10)
            ->get();

        return [
            'current_tier' => $currentTier,
            'next_tier' => $nextTier,
            'points' => [
                'available' => $userLoyalty->available_points,
                'lifetime' => $userLoyalty->lifetime_points,
                'expiring_soon' => $this->getExpiringPoints($user)
            ],
            'progress_to_next_tier' => $nextTier ? [
                'current' => $userLoyalty->lifetime_points,
                'required' => $nextTier->points_required,
                'percentage' => min(100, ($userLoyalty->lifetime_points / $nextTier->points_required) * 100)
            ] : null,
            'recent_activity' => $pointsHistory,
            'redemption_history' => $redemptionHistory,
            'tier_benefits' => $this->getTierBenefits($currentTier)
        ];
    }

    /**
     * Calculate points for booking
     */
    private function calculateBookingPoints(Booking $booking)
    {
        $basePoints = floor($booking->total_amount / 10); // 1 point per 10 EGP
        
        // Bonus points for different scenarios
        $bonusMultiplier = 1;
        
        // First booking bonus
        if ($this->isFirstBooking($booking->customer)) {
            $bonusMultiplier += 0.5; // 50% bonus
        }

        // Weekend bonus
        if ($booking->scheduled_date->isWeekend()) {
            $bonusMultiplier += 0.2; // 20% bonus
        }

        // High-value service bonus
        if ($booking->total_amount > 200) {
            $bonusMultiplier += 0.3; // 30% bonus
        }

        // Tier multiplier
        $userTier = $this->getUserTier($booking->customer);
        $bonusMultiplier += ($userTier->points_multiplier - 1);

        return floor($basePoints * $bonusMultiplier);
    }

    /**
     * Deduct points from user account
     */
    private function deductPoints(User $user, int $points, string $reason, array $metadata = [])
    {
        $userLoyalty = $this->getUserLoyalty($user);
        
        if ($userLoyalty->available_points < $points) {
            throw new \Exception('Insufficient points');
        }

        // Create negative points record
        LoyaltyPoint::create([
            'user_id' => $user->id,
            'points' => -$points,
            'source' => $reason,
            'metadata' => $metadata,
            'earned_at' => now()
        ]);

        // Update user's available points
        $userLoyalty->decrement('available_points', $points);
    }

    /**
     * Process reward redemption based on type
     */
    private function processRewardRedemption(User $user, LoyaltyReward $reward, $redemption)
    {
        switch ($reward->type) {
            case 'discount_coupon':
                return $this->createDiscountCoupon($user, $reward);
            
            case 'free_service':
                return $this->createFreeServiceVoucher($user, $reward);
            
            case 'wallet_credit':
                return $this->addWalletCredit($user, $reward);
            
            case 'vip_upgrade':
                return $this->processVIPUpgrade($user, $reward);
            
            default:
                $redemption->update(['status' => 'completed']);
                return ['message' => 'تم استبدال المكافأة بنجاح'];
        }
    }

    /**
     * Create discount coupon
     */
    private function createDiscountCoupon(User $user, LoyaltyReward $reward)
    {
        $couponService = app(CouponService::class);
        
        $coupon = $couponService->createPersonalCoupon($user, [
            'name' => $reward->name,
            'type' => 'percentage',
            'value' => $reward->value,
            'minimum_amount' => $reward->minimum_amount ?? 0,
            'expires_at' => now()->addDays(30),
            'usage_limit' => 1,
            'source' => 'loyalty_redemption'
        ]);

        return [
            'type' => 'coupon',
            'coupon_code' => $coupon->code,
            'message' => "تم إنشاء كوبون خصم {$reward->value}%"
        ];
    }

    /**
     * Create free service voucher
     */
    private function createFreeServiceVoucher(User $user, LoyaltyReward $reward)
    {
        // Implementation would create a voucher for free service
        return [
            'type' => 'voucher',
            'message' => 'تم إنشاء قسيمة خدمة مجانية'
        ];
    }

    /**
     * Add wallet credit
     */
    private function addWalletCredit(User $user, LoyaltyReward $reward)
    {
        $walletService = app(WalletService::class);
        $walletService->addCredit($user, $reward->value, 'Loyalty Reward');

        return [
            'type' => 'wallet_credit',
            'amount' => $reward->value,
            'message' => "تم إضافة {$reward->value} ج.م إلى محفظتك"
        ];
    }

    /**
     * Process VIP upgrade
     */
    private function processVIPUpgrade(User $user, LoyaltyReward $reward)
    {
        // Implementation would upgrade user to VIP
        return [
            'type' => 'vip_upgrade',
            'message' => 'تم ترقيتك إلى عضوية VIP'
        ];
    }

    /**
     * Award tier upgrade bonus
     */
    private function awardTierUpgradeBonus(User $user, LoyaltyTier $tier)
    {
        if ($tier->upgrade_bonus > 0) {
            $this->addPoints($user, $tier->upgrade_bonus, 'tier_upgrade', [
                'tier_id' => $tier->id,
                'tier_name' => $tier->name
            ]);
        }
    }

    /**
     * Get base tier
     */
    private function getBaseTier()
    {
        return LoyaltyTier::where('level', 1)->first();
    }

    /**
     * Check if this is user's first booking
     */
    private function isFirstBooking(User $user)
    {
        return Booking::where('customer_id', $user->id)
            ->where('status', 'completed')
            ->count() === 1;
    }

    /**
     * Get points expiring soon
     */
    private function getExpiringPoints(User $user)
    {
        return LoyaltyPoint::where('user_id', $user->id)
            ->where('points', '>', 0)
            ->where('expires_at', '>', now())
            ->where('expires_at', '<=', now()->addDays(30))
            ->sum('points');
    }

    /**
     * Get tier benefits
     */
    private function getTierBenefits(LoyaltyTier $tier)
    {
        return [
            'points_multiplier' => $tier->points_multiplier,
            'discount_percentage' => $tier->discount_percentage,
            'benefits' => $tier->benefits ?? []
        ];
    }

    /**
     * Send points earned notification
     */
    private function sendPointsEarnedNotification(User $user, int $points, Booking $booking)
    {
        $notificationService = app(NotificationService::class);
        
        $notificationService->sendPushNotification(
            $user->id,
            'نقاط ولاء جديدة!',
            "حصلت على {$points} نقطة من حجزك الأخير",
            [
                'type' => 'loyalty_points_earned',
                'points' => $points,
                'booking_id' => $booking->id
            ]
        );
    }

    /**
     * Send tier upgrade notification
     */
    private function sendTierUpgradeNotification(User $user, LoyaltyTier $tier)
    {
        $notificationService = app(NotificationService::class);
        
        $notificationService->sendPushNotification(
            $user->id,
            'ترقية مستوى الولاء!',
            "تهانينا! تم ترقيتك إلى مستوى {$tier->name}",
            [
                'type' => 'loyalty_tier_upgrade',
                'tier_id' => $tier->id,
                'tier_name' => $tier->name
            ]
        );
    }

    /**
     * Send redemption notification
     */
    private function sendRedemptionNotification(User $user, LoyaltyReward $reward, $redemption)
    {
        $notificationService = app(NotificationService::class);
        
        $notificationService->sendPushNotification(
            $user->id,
            'تم استبدال المكافأة',
            "تم استبدال {$reward->name} بنجاح",
            [
                'type' => 'loyalty_redemption',
                'reward_id' => $reward->id,
                'redemption_id' => $redemption->id
            ]
        );
    }

    /**
     * Get loyalty leaderboard
     */
    public function getLeaderboard(int $limit = 10)
    {
        return UserLoyalty::with('user')
            ->orderBy('lifetime_points', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($loyalty, $index) {
                return [
                    'rank' => $index + 1,
                    'user' => $loyalty->user->only(['id', 'name']),
                    'points' => $loyalty->lifetime_points,
                    'tier' => $loyalty->tier->name
                ];
            });
    }

    /**
     * Get monthly loyalty report
     */
    public function getMonthlyReport(Carbon $month)
    {
        $startDate = $month->copy()->startOfMonth();
        $endDate = $month->copy()->endOfMonth();

        $pointsEarned = LoyaltyPoint::whereBetween('earned_at', [$startDate, $endDate])
            ->where('points', '>', 0)
            ->sum('points');

        $pointsRedeemed = LoyaltyPoint::whereBetween('earned_at', [$startDate, $endDate])
            ->where('points', '<', 0)
            ->sum('points');

        $newMembers = UserLoyalty::whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $tierUpgrades = UserLoyalty::whereBetween('tier_achieved_at', [$startDate, $endDate])
            ->count();

        return [
            'period' => $month->format('Y-m'),
            'points_earned' => $pointsEarned,
            'points_redeemed' => abs($pointsRedeemed),
            'new_members' => $newMembers,
            'tier_upgrades' => $tierUpgrades,
            'engagement_rate' => $this->calculateEngagementRate($startDate, $endDate)
        ];
    }

    /**
     * Calculate engagement rate
     */
    private function calculateEngagementRate(Carbon $startDate, Carbon $endDate)
    {
        $totalUsers = User::count();
        $activeUsers = LoyaltyPoint::whereBetween('earned_at', [$startDate, $endDate])
            ->distinct('user_id')
            ->count();

        return $totalUsers > 0 ? ($activeUsers / $totalUsers) * 100 : 0;
    }
}
