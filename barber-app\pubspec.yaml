name: barber_professional
description: تطبيق الحلاقين المحترفين لمنصة حلاق على بابك - إدارة الطلبات والحجوزات

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  provider: ^6.1.1
  get: ^4.6.6
  bloc: ^8.1.2
  flutter_bloc: ^8.1.3

  # HTTP & API
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  pretty_dio_logger: ^1.3.1

  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.3.0

  # UI Components
  cupertino_icons: ^1.0.6
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  flutter_staggered_animations: ^1.1.1
  animate_do: ^3.1.2

  # Maps & Location
  google_maps_flutter: ^2.5.0
  location: ^5.0.3
  geocoding: ^2.1.1
  geolocator: ^10.1.0

  # Authentication & Security
  local_auth: ^2.1.7
  crypto: ^3.0.3

  # Notifications
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.9
  flutter_local_notifications: ^16.3.0

  # Media & Files
  image_picker: ^1.0.4
  file_picker: ^6.1.1
  permission_handler: ^11.1.0
  camera: ^0.10.5+5

  # Payment & Wallet
  flutter_stripe: ^9.5.0

  # Utils
  intl: ^0.18.1
  url_launcher: ^6.2.1
  share_plus: ^7.2.1
  package_info_plus: ^4.2.0
  device_info_plus: ^9.1.1
  connectivity_plus: ^5.0.2

  # Rating & Reviews
  flutter_rating_bar: ^4.0.1

  # Date & Time
  table_calendar: ^3.0.9

  # Animations & Loading
  loading_animation_widget: ^1.2.0+4
  flutter_spinkit: ^5.2.0

  # Form Validation
  form_field_validator: ^1.1.0

  # QR Code
  qr_flutter: ^4.1.0
  qr_code_scanner: ^1.0.1

  # Charts & Analytics
  fl_chart: ^0.64.0
  syncfusion_flutter_charts: ^23.1.44

  # Audio & Video
  just_audio: ^0.9.35
  video_player: ^2.7.2

  # Background Tasks
  workmanager: ^0.5.1

  # Real-time Communication
  socket_io_client: ^2.0.3+1

  # PDF Generation
  pdf: ^3.10.4
  printing: ^5.11.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  
  # Code Generation
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
    - assets/sounds/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300

flutter_intl:
  enabled: true
  arb_dir: lib/l10n
  output_dir: lib/generated
  use_deferred_loading: false
