<?php

namespace App\Services;

use App\Models\User;
use App\Models\Barber;
use App\Models\Booking;
use App\Models\Service;
use App\Models\Review;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class RecommendationService
{
    protected $cachePrefix = 'recommendations:';
    protected $cacheTtl = 3600; // 1 hour

    /**
     * Get personalized barber recommendations for a user
     */
    public function getBarberRecommendations(User $user, array $filters = []): Collection
    {
        $cacheKey = $this->cachePrefix . "barbers:user:{$user->id}:" . md5(serialize($filters));
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($user, $filters) {
            return $this->generateBarberRecommendations($user, $filters);
        });
    }

    /**
     * Get service recommendations based on user history
     */
    public function getServiceRecommendations(User $user, ?Barber $barber = null): Collection
    {
        $cacheKey = $this->cachePrefix . "services:user:{$user->id}:barber:" . ($barber?->id ?? 'any');
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($user, $barber) {
            return $this->generateServiceRecommendations($user, $barber);
        });
    }

    /**
     * Get trending services in user's area
     */
    public function getTrendingServices(?string $cityId = null): Collection
    {
        $cacheKey = $this->cachePrefix . "trending:services:city:" . ($cityId ?? 'all');
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($cityId) {
            return $this->generateTrendingServices($cityId);
        });
    }

    /**
     * Get similar users recommendations (collaborative filtering)
     */
    public function getSimilarUsersRecommendations(User $user): Collection
    {
        $cacheKey = $this->cachePrefix . "similar_users:{$user->id}";
        
        return Cache::remember($cacheKey, $this->cacheTtl * 2, function () use ($user) {
            return $this->generateSimilarUsersRecommendations($user);
        });
    }

    /**
     * Get personalized offers for user
     */
    public function getPersonalizedOffers(User $user): Collection
    {
        $cacheKey = $this->cachePrefix . "offers:user:{$user->id}";
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($user) {
            return $this->generatePersonalizedOffers($user);
        });
    }

    /**
     * Generate barber recommendations using multiple algorithms
     */
    protected function generateBarberRecommendations(User $user, array $filters): Collection
    {
        $recommendations = collect();

        // 1. Content-based filtering (based on user preferences)
        $contentBased = $this->getContentBasedBarberRecommendations($user, $filters);
        
        // 2. Collaborative filtering (based on similar users)
        $collaborative = $this->getCollaborativeBarberRecommendations($user, $filters);
        
        // 3. Location-based recommendations
        $locationBased = $this->getLocationBasedBarberRecommendations($user, $filters);
        
        // 4. Popularity-based recommendations
        $popularityBased = $this->getPopularityBasedBarberRecommendations($filters);

        // Combine and weight the recommendations
        $allRecommendations = collect();
        
        // Weight: Content-based (40%), Collaborative (30%), Location (20%), Popularity (10%)
        foreach ($contentBased as $barber) {
            $allRecommendations->push([
                'barber' => $barber,
                'score' => ($barber->recommendation_score ?? 0) * 0.4,
                'reason' => 'Based on your preferences'
            ]);
        }

        foreach ($collaborative as $barber) {
            $existing = $allRecommendations->firstWhere('barber.id', $barber->id);
            if ($existing) {
                $existing['score'] += ($barber->recommendation_score ?? 0) * 0.3;
                $existing['reason'] .= ', Similar users liked this barber';
            } else {
                $allRecommendations->push([
                    'barber' => $barber,
                    'score' => ($barber->recommendation_score ?? 0) * 0.3,
                    'reason' => 'Users like you chose this barber'
                ]);
            }
        }

        foreach ($locationBased as $barber) {
            $existing = $allRecommendations->firstWhere('barber.id', $barber->id);
            if ($existing) {
                $existing['score'] += ($barber->recommendation_score ?? 0) * 0.2;
                $existing['reason'] .= ', Close to your location';
            } else {
                $allRecommendations->push([
                    'barber' => $barber,
                    'score' => ($barber->recommendation_score ?? 0) * 0.2,
                    'reason' => 'Near your location'
                ]);
            }
        }

        foreach ($popularityBased as $barber) {
            $existing = $allRecommendations->firstWhere('barber.id', $barber->id);
            if ($existing) {
                $existing['score'] += ($barber->recommendation_score ?? 0) * 0.1;
                $existing['reason'] .= ', Highly rated';
            } else {
                $allRecommendations->push([
                    'barber' => $barber,
                    'score' => ($barber->recommendation_score ?? 0) * 0.1,
                    'reason' => 'Highly rated by customers'
                ]);
            }
        }

        // Sort by combined score and return top recommendations
        return $allRecommendations
            ->sortByDesc('score')
            ->take(20)
            ->map(function ($item) {
                $item['barber']->recommendation_score = round($item['score'], 2);
                $item['barber']->recommendation_reason = $item['reason'];
                return $item['barber'];
            });
    }

    /**
     * Content-based barber recommendations
     */
    protected function getContentBasedBarberRecommendations(User $user, array $filters): Collection
    {
        // Get user's booking history to understand preferences
        $userBookings = Booking::where('customer_id', $user->id)
            ->with(['barber', 'services', 'review'])
            ->get();

        // Extract user preferences
        $preferredServices = $userBookings->flatMap->services->pluck('id')->unique();
        $preferredBarberTraits = $this->extractBarberTraits($userBookings);
        $avgRatingGiven = $userBookings->whereNotNull('review')->avg('review.rating') ?? 4.0;

        // Build query for similar barbers
        $query = Barber::with(['user', 'services'])
            ->where('is_verified', true)
            ->where('is_available', true);

        // Apply filters
        if (!empty($filters['city_id'])) {
            $query->whereHas('user.profile', function ($q) use ($filters) {
                $q->where('city_id', $filters['city_id']);
            });
        }

        if (!empty($filters['service_ids'])) {
            $query->whereHas('services', function ($q) use ($filters) {
                $q->whereIn('services.id', $filters['service_ids']);
            });
        }

        $barbers = $query->get();

        // Score each barber based on user preferences
        return $barbers->map(function ($barber) use ($preferredServices, $preferredBarberTraits, $avgRatingGiven) {
            $score = 0;

            // Service similarity (40% weight)
            $barberServices = $barber->services->pluck('id');
            $serviceOverlap = $preferredServices->intersect($barberServices)->count();
            $serviceScore = $preferredServices->count() > 0 
                ? ($serviceOverlap / $preferredServices->count()) * 40 
                : 20;
            $score += $serviceScore;

            // Rating compatibility (30% weight)
            $ratingDiff = abs($barber->rating - $avgRatingGiven);
            $ratingScore = max(0, (5 - $ratingDiff) / 5) * 30;
            $score += $ratingScore;

            // Experience level (20% weight)
            $experienceScore = min($barber->experience_years / 10, 1) * 20;
            $score += $experienceScore;

            // Availability and responsiveness (10% weight)
            $availabilityScore = $barber->is_available ? 10 : 0;
            $score += $availabilityScore;

            $barber->recommendation_score = $score;
            return $barber;
        })->sortByDesc('recommendation_score');
    }

    /**
     * Collaborative filtering barber recommendations
     */
    protected function getCollaborativeBarberRecommendations(User $user, array $filters): Collection
    {
        // Find users with similar booking patterns
        $similarUsers = $this->findSimilarUsers($user);
        
        if ($similarUsers->isEmpty()) {
            return collect();
        }

        // Get barbers that similar users liked
        $recommendedBarbers = Booking::whereIn('customer_id', $similarUsers->pluck('id'))
            ->whereHas('review', function ($q) {
                $q->where('rating', '>=', 4);
            })
            ->with(['barber.user', 'barber.services'])
            ->get()
            ->groupBy('barber_id')
            ->map(function ($bookings, $barberId) {
                $barber = $bookings->first()->barber;
                $avgRating = $bookings->avg('review.rating');
                $bookingCount = $bookings->count();
                
                // Score based on rating and frequency
                $barber->recommendation_score = ($avgRating * 0.7) + (min($bookingCount / 5, 1) * 30);
                
                return $barber;
            })
            ->sortByDesc('recommendation_score');

        return $recommendedBarbers->take(10);
    }

    /**
     * Location-based barber recommendations
     */
    protected function getLocationBasedBarberRecommendations(User $user, array $filters): Collection
    {
        $userProfile = $user->profile;
        
        if (!$userProfile || !$userProfile->latitude || !$userProfile->longitude) {
            return collect();
        }

        // Find barbers within reasonable distance
        $query = Barber::select('barbers.*')
            ->selectRaw('
                (6371 * acos(cos(radians(?)) * cos(radians(user_profiles.latitude)) 
                * cos(radians(user_profiles.longitude) - radians(?)) 
                + sin(radians(?)) * sin(radians(user_profiles.latitude)))) AS distance
            ', [$userProfile->latitude, $userProfile->longitude, $userProfile->latitude])
            ->join('users', 'barbers.user_id', '=', 'users.id')
            ->join('user_profiles', 'users.id', '=', 'user_profiles.user_id')
            ->where('barbers.is_verified', true)
            ->where('barbers.is_available', true)
            ->having('distance', '<=', 20) // Within 20km
            ->orderBy('distance');

        $barbers = $query->get();

        // Score based on distance and rating
        return $barbers->map(function ($barber) {
            $distanceScore = max(0, (20 - $barber->distance) / 20) * 50;
            $ratingScore = ($barber->rating / 5) * 50;
            
            $barber->recommendation_score = $distanceScore + $ratingScore;
            return $barber;
        })->sortByDesc('recommendation_score');
    }

    /**
     * Popularity-based barber recommendations
     */
    protected function getPopularityBasedBarberRecommendations(array $filters): Collection
    {
        $query = Barber::with(['user', 'services'])
            ->where('is_verified', true)
            ->where('is_available', true)
            ->where('rating', '>=', 4.0)
            ->orderByDesc('rating')
            ->orderByDesc('total_bookings');

        // Apply filters
        if (!empty($filters['city_id'])) {
            $query->whereHas('user.profile', function ($q) use ($filters) {
                $q->where('city_id', $filters['city_id']);
            });
        }

        $barbers = $query->take(15)->get();

        return $barbers->map(function ($barber) {
            $ratingScore = ($barber->rating / 5) * 60;
            $popularityScore = min($barber->total_bookings / 100, 1) * 40;
            
            $barber->recommendation_score = $ratingScore + $popularityScore;
            return $barber;
        });
    }

    /**
     * Generate service recommendations
     */
    protected function generateServiceRecommendations(User $user, ?Barber $barber): Collection
    {
        // Get user's service history
        $userServices = Booking::where('customer_id', $user->id)
            ->with('services')
            ->get()
            ->flatMap->services;

        $serviceFrequency = $userServices->countBy('id');
        $recentServices = $userServices->where('pivot.created_at', '>', now()->subMonths(3));

        // Get trending services
        $trendingServices = $this->getTrendingServices($user->profile?->city_id);

        // Combine recommendations
        $recommendations = collect();

        // Frequently used services (weight: 40%)
        foreach ($serviceFrequency as $serviceId => $count) {
            $service = Service::find($serviceId);
            if ($service) {
                $score = min($count / 5, 1) * 40;
                $recommendations->push([
                    'service' => $service,
                    'score' => $score,
                    'reason' => 'You often book this service'
                ]);
            }
        }

        // Complementary services (weight: 30%)
        $complementaryServices = $this->getComplementaryServices($recentServices);
        foreach ($complementaryServices as $service) {
            $existing = $recommendations->firstWhere('service.id', $service->id);
            if ($existing) {
                $existing['score'] += 30;
                $existing['reason'] .= ', Goes well with your recent bookings';
            } else {
                $recommendations->push([
                    'service' => $service,
                    'score' => 30,
                    'reason' => 'Complements your recent services'
                ]);
            }
        }

        // Trending services (weight: 20%)
        foreach ($trendingServices->take(5) as $service) {
            $existing = $recommendations->firstWhere('service.id', $service->id);
            if ($existing) {
                $existing['score'] += 20;
                $existing['reason'] .= ', Trending in your area';
            } else {
                $recommendations->push([
                    'service' => $service,
                    'score' => 20,
                    'reason' => 'Trending in your area'
                ]);
            }
        }

        // Filter by barber if specified
        if ($barber) {
            $barberServiceIds = $barber->services->pluck('id');
            $recommendations = $recommendations->filter(function ($item) use ($barberServiceIds) {
                return $barberServiceIds->contains($item['service']->id);
            });
        }

        return $recommendations
            ->sortByDesc('score')
            ->take(10)
            ->map(function ($item) {
                $item['service']->recommendation_score = round($item['score'], 2);
                $item['service']->recommendation_reason = $item['reason'];
                return $item['service'];
            });
    }

    /**
     * Generate trending services
     */
    protected function generateTrendingServices(?string $cityId): Collection
    {
        $query = DB::table('booking_services')
            ->join('bookings', 'booking_services.booking_id', '=', 'bookings.id')
            ->join('services', 'booking_services.service_id', '=', 'services.id')
            ->where('bookings.created_at', '>', now()->subDays(30))
            ->where('bookings.status', 'completed');

        if ($cityId) {
            $query->join('barbers', 'bookings.barber_id', '=', 'barbers.id')
                  ->join('users', 'barbers.user_id', '=', 'users.id')
                  ->join('user_profiles', 'users.id', '=', 'user_profiles.user_id')
                  ->where('user_profiles.city_id', $cityId);
        }

        $trendingData = $query
            ->select('services.*', DB::raw('COUNT(*) as booking_count'))
            ->groupBy('services.id')
            ->orderByDesc('booking_count')
            ->get();

        return $trendingData->map(function ($item) {
            $service = Service::find($item->id);
            $service->trend_score = $item->booking_count;
            return $service;
        });
    }

    /**
     * Find users with similar preferences
     */
    protected function findSimilarUsers(User $user): Collection
    {
        $userServices = Booking::where('customer_id', $user->id)
            ->with('services')
            ->get()
            ->flatMap->services
            ->pluck('id')
            ->unique();

        if ($userServices->isEmpty()) {
            return collect();
        }

        // Find users who booked similar services
        $similarUsers = DB::table('bookings')
            ->join('booking_services', 'bookings.id', '=', 'booking_services.booking_id')
            ->whereIn('booking_services.service_id', $userServices)
            ->where('bookings.customer_id', '!=', $user->id)
            ->select('bookings.customer_id', DB::raw('COUNT(DISTINCT booking_services.service_id) as common_services'))
            ->groupBy('bookings.customer_id')
            ->having('common_services', '>=', max(1, $userServices->count() * 0.3))
            ->orderByDesc('common_services')
            ->take(20)
            ->get();

        return User::whereIn('id', $similarUsers->pluck('customer_id'))->get();
    }

    /**
     * Extract barber traits from user's booking history
     */
    protected function extractBarberTraits(Collection $bookings): array
    {
        $traits = [
            'avg_experience' => $bookings->avg('barber.experience_years') ?? 5,
            'preferred_rating_range' => [
                $bookings->min('barber.rating') ?? 4.0,
                $bookings->max('barber.rating') ?? 5.0
            ],
            'preferred_specialties' => $bookings->flatMap(function ($booking) {
                return $booking->barber->specialties ?? [];
            })->unique()->values()->toArray()
        ];

        return $traits;
    }

    /**
     * Get complementary services
     */
    protected function getComplementaryServices(Collection $recentServices): Collection
    {
        $serviceIds = $recentServices->pluck('id')->unique();
        
        // Define service combinations that work well together
        $complementaryMap = [
            1 => [2, 3], // Hair cut -> Beard trim, Hair wash
            2 => [1, 4], // Beard trim -> Hair cut, Face care
            3 => [1, 5], // Hair wash -> Hair cut, Hair treatment
            // Add more combinations based on your services
        ];

        $complementary = collect();
        
        foreach ($serviceIds as $serviceId) {
            if (isset($complementaryMap[$serviceId])) {
                $complementaryIds = $complementaryMap[$serviceId];
                $services = Service::whereIn('id', $complementaryIds)->get();
                $complementary = $complementary->merge($services);
            }
        }

        return $complementary->unique('id');
    }

    /**
     * Generate personalized offers
     */
    protected function generatePersonalizedOffers(User $user): Collection
    {
        $offers = collect();

        // Loyalty-based offers
        $loyaltyTier = $user->loyaltyTier;
        if ($loyaltyTier && $loyaltyTier->level >= 2) {
            $offers->push([
                'type' => 'loyalty_discount',
                'title' => 'خصم عضو ' . $loyaltyTier->name,
                'description' => 'احصل على خصم ' . $loyaltyTier->discount_percentage . '% على حجزك التالي',
                'discount_percentage' => $loyaltyTier->discount_percentage,
                'valid_until' => now()->addDays(7)
            ]);
        }

        // Frequency-based offers
        $bookingCount = Booking::where('customer_id', $user->id)
            ->where('created_at', '>', now()->subMonth())
            ->count();

        if ($bookingCount >= 3) {
            $offers->push([
                'type' => 'frequent_customer',
                'title' => 'عميل مميز',
                'description' => 'خصم 15% لعملائنا المميزين',
                'discount_percentage' => 15,
                'valid_until' => now()->addDays(14)
            ]);
        }

        // Comeback offers for inactive users
        $lastBooking = Booking::where('customer_id', $user->id)
            ->orderByDesc('created_at')
            ->first();

        if ($lastBooking && $lastBooking->created_at->lt(now()->subDays(30))) {
            $offers->push([
                'type' => 'comeback',
                'title' => 'نشتاق لك!',
                'description' => 'خصم 20% على عودتك إلينا',
                'discount_percentage' => 20,
                'valid_until' => now()->addDays(10)
            ]);
        }

        return $offers;
    }

    /**
     * Clear recommendation cache for a user
     */
    public function clearUserCache(User $user): void
    {
        $patterns = [
            $this->cachePrefix . "barbers:user:{$user->id}:*",
            $this->cachePrefix . "services:user:{$user->id}:*",
            $this->cachePrefix . "similar_users:{$user->id}",
            $this->cachePrefix . "offers:user:{$user->id}"
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Update recommendation models (for ML training)
     */
    public function updateRecommendationModels(): void
    {
        // This would integrate with ML services to retrain models
        // For now, we'll just clear all caches to force regeneration
        Cache::tags(['recommendations'])->flush();
    }
}
