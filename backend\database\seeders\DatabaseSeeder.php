<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\City;
use App\Models\Area;
use App\Models\ServiceCategory;
use App\Models\Service;
use App\Models\Barber;
use App\Models\PaymentMethod;
use App\Models\LoyaltyTier;
use App\Models\VipPackage;
use App\Models\SystemSetting;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create Cities
        $this->seedCities();
        
        // Create Service Categories and Services
        $this->seedServices();
        
        // Create Payment Methods
        $this->seedPaymentMethods();
        
        // Create Loyalty Tiers
        $this->seedLoyaltyTiers();
        
        // Create VIP Packages
        $this->seedVipPackages();
        
        // Create System Settings
        $this->seedSystemSettings();
        
        // Create Admin User
        $this->seedAdminUser();
        
        // Create Sample Data
        $this->seedSampleData();
    }

    private function seedCities()
    {
        $cities = [
            ['name' => 'القاهرة', 'name_en' => 'Cairo', 'latitude' => 30.0444, 'longitude' => 31.2357],
            ['name' => 'الجيزة', 'name_en' => 'Giza', 'latitude' => 30.0131, 'longitude' => 31.2089],
            ['name' => 'الإسكندرية', 'name_en' => 'Alexandria', 'latitude' => 31.2001, 'longitude' => 29.9187],
            ['name' => 'شبرا الخيمة', 'name_en' => 'Shubra El Kheima', 'latitude' => 30.1287, 'longitude' => 31.2441],
            ['name' => 'بورسعيد', 'name_en' => 'Port Said', 'latitude' => 31.2653, 'longitude' => 32.3019],
            ['name' => 'السويس', 'name_en' => 'Suez', 'latitude' => 29.9668, 'longitude' => 32.5498],
            ['name' => 'المنصورة', 'name_en' => 'Mansoura', 'latitude' => 31.0409, 'longitude' => 31.3785],
            ['name' => 'طنطا', 'name_en' => 'Tanta', 'latitude' => 30.7865, 'longitude' => 31.0004],
            ['name' => 'أسيوط', 'name_en' => 'Asyut', 'latitude' => 27.1809, 'longitude' => 31.1837],
            ['name' => 'الفيوم', 'name_en' => 'Faiyum', 'latitude' => 29.3084, 'longitude' => 30.8428],
        ];

        foreach ($cities as $cityData) {
            $city = City::create($cityData);
            
            // Create areas for each city
            $this->seedAreasForCity($city);
        }
    }

    private function seedAreasForCity(City $city)
    {
        $areas = [];
        
        switch ($city->name_en) {
            case 'Cairo':
                $areas = [
                    ['name' => 'مدينة نصر', 'name_en' => 'Nasr City'],
                    ['name' => 'مصر الجديدة', 'name_en' => 'Heliopolis'],
                    ['name' => 'المعادي', 'name_en' => 'Maadi'],
                    ['name' => 'الزمالك', 'name_en' => 'Zamalek'],
                    ['name' => 'وسط البلد', 'name_en' => 'Downtown'],
                    ['name' => 'مدينة الشروق', 'name_en' => 'Shorouk City'],
                    ['name' => 'التجمع الخامس', 'name_en' => 'New Cairo'],
                    ['name' => 'المقطم', 'name_en' => 'Mokattam'],
                ];
                break;
            case 'Giza':
                $areas = [
                    ['name' => 'الدقي', 'name_en' => 'Dokki'],
                    ['name' => 'المهندسين', 'name_en' => 'Mohandessin'],
                    ['name' => '6 أكتوبر', 'name_en' => '6th of October'],
                    ['name' => 'الشيخ زايد', 'name_en' => 'Sheikh Zayed'],
                    ['name' => 'فيصل', 'name_en' => 'Faisal'],
                    ['name' => 'الهرم', 'name_en' => 'Haram'],
                ];
                break;
            case 'Alexandria':
                $areas = [
                    ['name' => 'سيدي بشر', 'name_en' => 'Sidi Bishr'],
                    ['name' => 'ستانلي', 'name_en' => 'Stanley'],
                    ['name' => 'سموحة', 'name_en' => 'Smouha'],
                    ['name' => 'المنتزه', 'name_en' => 'Montaza'],
                    ['name' => 'العجمي', 'name_en' => 'Agami'],
                ];
                break;
            default:
                $areas = [
                    ['name' => 'وسط المدينة', 'name_en' => 'City Center'],
                    ['name' => 'الحي الأول', 'name_en' => 'First District'],
                    ['name' => 'الحي الثاني', 'name_en' => 'Second District'],
                ];
        }

        foreach ($areas as $areaData) {
            Area::create([
                'city_id' => $city->id,
                'name' => $areaData['name'],
                'name_en' => $areaData['name_en'],
                'latitude' => $city->latitude + (rand(-100, 100) / 1000),
                'longitude' => $city->longitude + (rand(-100, 100) / 1000),
            ]);
        }
    }

    private function seedServices()
    {
        $categories = [
            [
                'name' => 'قص الشعر',
                'description' => 'خدمات قص وتصفيف الشعر',
                'icon' => 'scissors',
                'sort_order' => 1,
                'services' => [
                    ['name' => 'قص شعر عادي', 'description' => 'قص شعر كلاسيكي', 'base_price' => 50.00, 'duration_minutes' => 30],
                    ['name' => 'قص شعر مدرج', 'description' => 'قص شعر مدرج حديث', 'base_price' => 70.00, 'duration_minutes' => 45],
                    ['name' => 'قص شعر أطفال', 'description' => 'قص شعر مخصص للأطفال', 'base_price' => 40.00, 'duration_minutes' => 25],
                    ['name' => 'تصفيف شعر', 'description' => 'تصفيف وتسريح الشعر', 'base_price' => 60.00, 'duration_minutes' => 40],
                ]
            ],
            [
                'name' => 'الحلاقة',
                'description' => 'حلاقة الذقن والشارب',
                'icon' => 'razor',
                'sort_order' => 2,
                'services' => [
                    ['name' => 'حلاقة ذقن', 'description' => 'حلاقة وتهذيب الذقن', 'base_price' => 30.00, 'duration_minutes' => 20],
                    ['name' => 'حلاقة شارب', 'description' => 'تهذيب الشارب', 'base_price' => 20.00, 'duration_minutes' => 15],
                    ['name' => 'حلاقة كاملة', 'description' => 'حلاقة الذقن والشارب', 'base_price' => 45.00, 'duration_minutes' => 30],
                    ['name' => 'تشكيل لحية', 'description' => 'تشكيل وتهذيب اللحية', 'base_price' => 35.00, 'duration_minutes' => 25],
                ]
            ],
            [
                'name' => 'العناية بالشعر',
                'description' => 'غسيل وترطيب الشعر',
                'icon' => 'hair-care',
                'sort_order' => 3,
                'services' => [
                    ['name' => 'غسيل شعر', 'description' => 'غسيل وتنظيف الشعر', 'base_price' => 25.00, 'duration_minutes' => 15],
                    ['name' => 'ماسك للشعر', 'description' => 'ماسك مغذي للشعر', 'base_price' => 40.00, 'duration_minutes' => 30],
                    ['name' => 'حمام كريم', 'description' => 'حمام كريم مرطب', 'base_price' => 35.00, 'duration_minutes' => 25],
                    ['name' => 'علاج الشعر', 'description' => 'علاج مشاكل الشعر', 'base_price' => 80.00, 'duration_minutes' => 60],
                ]
            ],
            [
                'name' => 'التجميل',
                'description' => 'خدمات التجميل والعناية',
                'icon' => 'beauty',
                'sort_order' => 4,
                'services' => [
                    ['name' => 'تنظيف بشرة', 'description' => 'تنظيف عميق للبشرة', 'base_price' => 100.00, 'duration_minutes' => 60],
                    ['name' => 'ماسك وجه', 'description' => 'ماسك مغذي للوجه', 'base_price' => 60.00, 'duration_minutes' => 45],
                    ['name' => 'تدليك وجه', 'description' => 'تدليك مريح للوجه', 'base_price' => 50.00, 'duration_minutes' => 30],
                    ['name' => 'إزالة الرؤوس السوداء', 'description' => 'تنظيف المسام', 'base_price' => 70.00, 'duration_minutes' => 40],
                ]
            ],
            [
                'name' => 'خدمات VIP',
                'description' => 'خدمات حصرية لأعضاء VIP',
                'icon' => 'vip',
                'sort_order' => 5,
                'services' => [
                    ['name' => 'باقة VIP كاملة', 'description' => 'خدمة شاملة مع مساج', 'base_price' => 200.00, 'duration_minutes' => 120, 'is_vip_exclusive' => true],
                    ['name' => 'حلاقة VIP', 'description' => 'حلاقة فاخرة مع عناية', 'base_price' => 150.00, 'duration_minutes' => 90, 'is_vip_exclusive' => true],
                    ['name' => 'عناية VIP', 'description' => 'عناية متكاملة للشعر والبشرة', 'base_price' => 180.00, 'duration_minutes' => 100, 'is_vip_exclusive' => true],
                ]
            ]
        ];

        foreach ($categories as $categoryData) {
            $services = $categoryData['services'];
            unset($categoryData['services']);
            
            $category = ServiceCategory::create($categoryData);
            
            foreach ($services as $serviceData) {
                $serviceData['category_id'] = $category->id;
                Service::create($serviceData);
            }
        }
    }

    private function seedPaymentMethods()
    {
        $paymentMethods = [
            [
                'name' => 'فيزا/ماستركارد',
                'type' => 'card',
                'provider' => 'stripe',
                'is_active' => true,
                'settings' => [
                    'supported_cards' => ['visa', 'mastercard'],
                    'currency' => 'EGP',
                    '3d_secure' => true
                ]
            ],
            [
                'name' => 'فوري',
                'type' => 'wallet',
                'provider' => 'paymob',
                'is_active' => true,
                'settings' => [
                    'wallet_type' => 'fawry',
                    'currency' => 'EGP'
                ]
            ],
            [
                'name' => 'كاش',
                'type' => 'cash',
                'provider' => null,
                'is_active' => true,
                'settings' => [
                    'requires_confirmation' => true
                ]
            ],
            [
                'name' => 'تحويل بنكي',
                'type' => 'bank_transfer',
                'provider' => null,
                'is_active' => true,
                'settings' => [
                    'requires_receipt' => true,
                    'processing_time' => '24 hours'
                ]
            ],
            [
                'name' => 'محفظة إلكترونية',
                'type' => 'wallet',
                'provider' => 'paymob',
                'is_active' => true,
                'settings' => [
                    'supported_wallets' => ['vodafone_cash', 'orange_cash', 'etisalat_cash'],
                    'currency' => 'EGP'
                ]
            ]
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::create($method);
        }
    }

    private function seedLoyaltyTiers()
    {
        $tiers = [
            [
                'name' => 'برونزي',
                'level' => 1,
                'points_required' => 0,
                'points_multiplier' => 1.00,
                'discount_percentage' => 0.00,
                'upgrade_bonus' => 0,
                'benefits' => [
                    'نقاط على كل حجز',
                    'إشعارات العروض الخاصة'
                ]
            ],
            [
                'name' => 'فضي',
                'level' => 2,
                'points_required' => 500,
                'points_multiplier' => 1.25,
                'discount_percentage' => 5.00,
                'upgrade_bonus' => 50,
                'benefits' => [
                    'خصم 5% على جميع الخدمات',
                    'نقاط إضافية 25%',
                    'أولوية في خدمة العملاء',
                    'عروض حصرية شهرية'
                ]
            ],
            [
                'name' => 'ذهبي',
                'level' => 3,
                'points_required' => 1500,
                'points_multiplier' => 1.50,
                'discount_percentage' => 10.00,
                'upgrade_bonus' => 100,
                'benefits' => [
                    'خصم 10% على جميع الخدمات',
                    'نقاط إضافية 50%',
                    'إلغاء مجاني حتى ساعة قبل الموعد',
                    'خدمة عملاء مميزة',
                    'دعوات لفعاليات خاصة'
                ]
            ],
            [
                'name' => 'بلاتيني',
                'level' => 4,
                'points_required' => 3000,
                'points_multiplier' => 2.00,
                'discount_percentage' => 15.00,
                'upgrade_bonus' => 200,
                'benefits' => [
                    'خصم 15% على جميع الخدمات',
                    'نقاط مضاعفة',
                    'إلغاء مجاني في أي وقت',
                    'أولوية في الحجز',
                    'خدمة عملاء VIP',
                    'هدايا حصرية',
                    'وصول مبكر للخدمات الجديدة'
                ]
            ]
        ];

        foreach ($tiers as $tier) {
            LoyaltyTier::create($tier);
        }
    }

    private function seedVipPackages()
    {
        $packages = [
            [
                'name' => 'VIP برونز',
                'description' => 'باقة VIP الأساسية مع مزايا أساسية',
                'price' => 99.00,
                'duration_days' => 30,
                'discount_percentage' => 10.00,
                'priority_booking' => true,
                'free_cancellation' => false,
                'features' => [
                    'خصم 10% على جميع الخدمات',
                    'أولوية في الحجز',
                    'خدمة عملاء مميزة',
                    'إشعارات العروض الحصرية'
                ],
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'VIP فضة',
                'description' => 'باقة VIP متوسطة مع مزايا إضافية',
                'price' => 199.00,
                'duration_days' => 30,
                'discount_percentage' => 15.00,
                'priority_booking' => true,
                'free_cancellation' => true,
                'features' => [
                    'خصم 15% على جميع الخدمات',
                    'أولوية في الحجز',
                    'إلغاء مجاني',
                    'خدمة عملاء مميزة',
                    'تقارير شخصية شهرية',
                    'دعوات للفعاليات الخاصة'
                ],
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'VIP ذهب',
                'description' => 'باقة VIP متقدمة مع مزايا شاملة',
                'price' => 299.00,
                'duration_days' => 30,
                'discount_percentage' => 20.00,
                'priority_booking' => true,
                'free_cancellation' => true,
                'features' => [
                    'خصم 20% على جميع الخدمات',
                    'أولوية في الحجز',
                    'إلغاء مجاني',
                    'حجز فوري',
                    'خدمة عملاء VIP',
                    'استشارات مجانية',
                    'هدايا شهرية',
                    'وصول مبكر للخدمات الجديدة'
                ],
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'VIP بلاتين',
                'description' => 'باقة VIP الفاخرة مع جميع المزايا',
                'price' => 499.00,
                'duration_days' => 30,
                'discount_percentage' => 25.00,
                'priority_booking' => true,
                'free_cancellation' => true,
                'features' => [
                    'خصم 25% على جميع الخدمات',
                    'أولوية في الحجز',
                    'إلغاء مجاني',
                    'حجز فوري',
                    'خدمة عملاء VIP 24/7',
                    'استشارات مجانية غير محدودة',
                    'هدايا شهرية فاخرة',
                    'وصول حصري للخدمات المميزة',
                    'تقارير تفصيلية أسبوعية',
                    'مدير حساب شخصي'
                ],
                'is_active' => true,
                'sort_order' => 4
            ]
        ];

        foreach ($packages as $package) {
            VipPackage::create($package);
        }
    }

    private function seedSystemSettings()
    {
        $settings = [
            ['key' => 'app_name', 'value' => 'حلاق على بابك', 'type' => 'string', 'description' => 'اسم التطبيق', 'is_public' => true],
            ['key' => 'app_version', 'value' => '1.0.0', 'type' => 'string', 'description' => 'إصدار التطبيق', 'is_public' => true],
            ['key' => 'app_logo', 'value' => '/images/logo.png', 'type' => 'string', 'description' => 'شعار التطبيق', 'is_public' => true],
            ['key' => 'commission_rate', 'value' => '15.00', 'type' => 'string', 'description' => 'نسبة العمولة الافتراضية (%)', 'is_public' => false],
            ['key' => 'booking_cancellation_hours', 'value' => '2', 'type' => 'integer', 'description' => 'عدد الساعات المسموحة للإلغاء المجاني', 'is_public' => true],
            ['key' => 'max_booking_days_ahead', 'value' => '30', 'type' => 'integer', 'description' => 'أقصى عدد أيام للحجز مقدماً', 'is_public' => true],
            ['key' => 'maintenance_mode', 'value' => 'false', 'type' => 'boolean', 'description' => 'وضع الصيانة', 'is_public' => true],
            ['key' => 'support_phone', 'value' => '+201234567890', 'type' => 'string', 'description' => 'رقم هاتف الدعم الفني', 'is_public' => true],
            ['key' => 'support_email', 'value' => '<EMAIL>', 'type' => 'string', 'description' => 'بريد الدعم الفني', 'is_public' => true],
            ['key' => 'terms_url', 'value' => 'https://barber-app.com/terms', 'type' => 'string', 'description' => 'رابط الشروط والأحكام', 'is_public' => true],
            ['key' => 'privacy_url', 'value' => 'https://barber-app.com/privacy', 'type' => 'string', 'description' => 'رابط سياسة الخصوصية', 'is_public' => true],
            ['key' => 'min_booking_amount', 'value' => '30.00', 'type' => 'string', 'description' => 'أقل مبلغ للحجز', 'is_public' => true],
            ['key' => 'max_booking_amount', 'value' => '1000.00', 'type' => 'string', 'description' => 'أقصى مبلغ للحجز', 'is_public' => true],
            ['key' => 'default_service_radius', 'value' => '10', 'type' => 'integer', 'description' => 'نطاق الخدمة الافتراضي (كم)', 'is_public' => true],
            ['key' => 'loyalty_points_rate', 'value' => '10', 'type' => 'integer', 'description' => 'نقاط الولاء لكل 10 جنيه', 'is_public' => false],
            ['key' => 'referral_bonus', 'value' => '50', 'type' => 'integer', 'description' => 'مكافأة الإحالة (نقاط)', 'is_public' => false],
            ['key' => 'social_facebook', 'value' => 'https://facebook.com/barberapp', 'type' => 'string', 'description' => 'رابط فيسبوك', 'is_public' => true],
            ['key' => 'social_instagram', 'value' => 'https://instagram.com/barberapp', 'type' => 'string', 'description' => 'رابط إنستغرام', 'is_public' => true],
            ['key' => 'social_twitter', 'value' => 'https://twitter.com/barberapp', 'type' => 'string', 'description' => 'رابط تويتر', 'is_public' => true],
            ['key' => 'push_notifications_enabled', 'value' => 'true', 'type' => 'boolean', 'description' => 'تفعيل الإشعارات', 'is_public' => false],
        ];

        foreach ($settings as $setting) {
            SystemSetting::create($setting);
        }
    }

    private function seedAdminUser()
    {
        $admin = User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'phone' => '+201000000000',
            'password' => Hash::make('admin123456'),
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'is_active' => true,
        ]);

        // Create admin profile
        $admin->profile()->create([
            'address' => 'مقر الشركة، القاهرة، مصر',
            'city_id' => 1, // Cairo
            'latitude' => 30.0444,
            'longitude' => 31.2357,
        ]);
    }

    private function seedSampleData()
    {
        // Create sample customers
        $this->seedSampleCustomers();
        
        // Create sample barbers
        $this->seedSampleBarbers();
        
        // Create sample bookings
        $this->seedSampleBookings();
    }

    private function seedSampleCustomers()
    {
        $customers = [
            ['name' => 'أحمد محمد', 'email' => '<EMAIL>', 'phone' => '+201111111111'],
            ['name' => 'محمد علي', 'email' => '<EMAIL>', 'phone' => '+201222222222'],
            ['name' => 'علي أحمد', 'email' => '<EMAIL>', 'phone' => '+201333333333'],
            ['name' => 'حسام محمود', 'email' => '<EMAIL>', 'phone' => '+201444444444'],
            ['name' => 'كريم سامي', 'email' => '<EMAIL>', 'phone' => '+201555555555'],
        ];

        foreach ($customers as $customerData) {
            $customer = User::create([
                'name' => $customerData['name'],
                'email' => $customerData['email'],
                'phone' => $customerData['phone'],
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'phone_verified_at' => now(),
            ]);

            $customer->profile()->create([
                'address' => 'عنوان تجريبي، القاهرة',
                'city_id' => rand(1, 3),
                'latitude' => 30.0444 + (rand(-100, 100) / 1000),
                'longitude' => 31.2357 + (rand(-100, 100) / 1000),
            ]);
        }
    }

    private function seedSampleBarbers()
    {
        $barbers = [
            ['name' => 'أستاذ حسن', 'email' => '<EMAIL>', 'phone' => '+201666666666', 'experience' => 10],
            ['name' => 'كابتن أحمد', 'email' => '<EMAIL>', 'phone' => '+201777777777', 'experience' => 8],
            ['name' => 'أستاذ محمد', 'email' => '<EMAIL>', 'phone' => '+201888888888', 'experience' => 12],
            ['name' => 'كابتن علي', 'email' => '<EMAIL>', 'phone' => '+201999999999', 'experience' => 6],
            ['name' => 'أستاذ سامي', 'email' => '<EMAIL>', 'phone' => '+201000000001', 'experience' => 15],
        ];

        foreach ($barbers as $barberData) {
            $user = User::create([
                'name' => $barberData['name'],
                'email' => $barberData['email'],
                'phone' => $barberData['phone'],
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'phone_verified_at' => now(),
            ]);

            $barber = Barber::create([
                'user_id' => $user->id,
                'experience_years' => $barberData['experience'],
                'bio' => 'حلاق محترف مع خبرة ' . $barberData['experience'] . ' سنوات في مجال الحلاقة والتجميل',
                'specialties' => ['قص شعر', 'حلاقة ذقن', 'تصفيف'],
                'rating' => rand(40, 50) / 10, // 4.0 to 5.0
                'total_reviews' => rand(50, 200),
                'total_bookings' => rand(100, 500),
                'is_verified' => true,
                'is_available' => true,
                'working_hours' => [
                    'saturday' => ['start' => '09:00', 'end' => '22:00'],
                    'sunday' => ['start' => '09:00', 'end' => '22:00'],
                    'monday' => ['start' => '09:00', 'end' => '22:00'],
                    'tuesday' => ['start' => '09:00', 'end' => '22:00'],
                    'wednesday' => ['start' => '09:00', 'end' => '22:00'],
                    'thursday' => ['start' => '09:00', 'end' => '22:00'],
                    'friday' => ['start' => '14:00', 'end' => '22:00'],
                ],
            ]);

            // Create barber profile
            $user->profile()->create([
                'address' => 'عنوان الحلاق، القاهرة',
                'city_id' => rand(1, 3),
                'latitude' => 30.0444 + (rand(-100, 100) / 1000),
                'longitude' => 31.2357 + (rand(-100, 100) / 1000),
            ]);

            // Add services to barber
            $services = Service::inRandomOrder()->limit(rand(5, 10))->get();
            foreach ($services as $service) {
                $barber->services()->attach($service->id, [
                    'custom_price' => $service->base_price + rand(-10, 20),
                ]);
            }

            // Create barber location
            $barber->locations()->create([
                'city_id' => $user->profile->city_id,
                'address' => $user->profile->address,
                'latitude' => $user->profile->latitude,
                'longitude' => $user->profile->longitude,
                'is_current' => true,
            ]);
        }
    }

    private function seedSampleBookings()
    {
        $customers = User::whereDoesntHave('barber')->get();
        $barbers = Barber::all();

        for ($i = 0; $i < 20; $i++) {
            $customer = $customers->random();
            $barber = $barbers->random();
            $services = $barber->services()->inRandomOrder()->limit(rand(1, 3))->get();
            
            $totalAmount = $services->sum('pivot.custom_price');
            $commissionRate = 15; // 15%
            $commissionAmount = ($totalAmount * $commissionRate) / 100;
            $barberAmount = $totalAmount - $commissionAmount;

            $booking = \App\Models\Booking::create([
                'booking_number' => 'BK' . date('Ymd') . str_pad($i + 1, 3, '0', STR_PAD_LEFT),
                'customer_id' => $customer->id,
                'barber_id' => $barber->id,
                'scheduled_date' => now()->addDays(rand(1, 30)),
                'scheduled_time' => sprintf('%02d:00', rand(9, 21)),
                'status' => ['pending', 'confirmed', 'completed'][rand(0, 2)],
                'total_amount' => $totalAmount,
                'commission_amount' => $commissionAmount,
                'barber_amount' => $barberAmount,
                'customer_address' => $customer->profile->address,
                'customer_latitude' => $customer->profile->latitude,
                'customer_longitude' => $customer->profile->longitude,
                'notes' => 'ملاحظات تجريبية للحجز',
            ]);

            // Add services to booking
            foreach ($services as $service) {
                $booking->services()->attach($service->id, [
                    'quantity' => 1,
                    'unit_price' => $service->pivot->custom_price,
                    'total_price' => $service->pivot->custom_price,
                ]);
            }
        }
    }
}
