import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // State
  const loading = ref(false)
  const notifications = ref([])
  const theme = ref(localStorage.getItem('admin_theme') || 'light')
  const sidebarCollapsed = ref(false)
  const breadcrumbs = ref([])

  // Getters
  const notificationCount = computed(() => 
    notifications.value.filter(n => !n.read).length
  )

  const isDarkTheme = computed(() => theme.value === 'dark')

  // Actions
  const setLoading = (value) => {
    loading.value = value
  }

  const addNotification = (notification) => {
    notifications.value.unshift({
      id: Date.now(),
      read: false,
      timestamp: new Date(),
      ...notification
    })
  }

  const markNotificationAsRead = (id) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  const markAllNotificationsAsRead = () => {
    notifications.value.forEach(n => n.read = true)
  }

  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    localStorage.setItem('admin_theme', theme.value)
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const setBreadcrumbs = (items) => {
    breadcrumbs.value = items
  }

  const initializeApp = async () => {
    // Initialize app-wide data
    try {
      // Load initial notifications
      // await loadNotifications()
      
      // Set up real-time connections
      // setupWebSocket()
      
    } catch (error) {
      console.error('App initialization error:', error)
    }
  }

  return {
    // State
    loading,
    notifications,
    theme,
    sidebarCollapsed,
    breadcrumbs,
    
    // Getters
    notificationCount,
    isDarkTheme,
    
    // Actions
    setLoading,
    addNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    removeNotification,
    toggleTheme,
    toggleSidebar,
    setBreadcrumbs,
    initializeApp
  }
})
