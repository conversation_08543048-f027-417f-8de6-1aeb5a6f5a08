# Service Mesh Configuration for Barber App Microservices
# Using Istio for advanced traffic management, security, and observability

apiVersion: v1
kind: Namespace
metadata:
  name: barber-app-mesh
  labels:
    istio-injection: enabled
    name: barber-app-mesh
---
# Istio Gateway for external traffic
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: barber-app-gateway
  namespace: barber-app-mesh
spec:
  selector:
    istio: ingressgateway
  servers:
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - api.barber-app.com
        - admin.barber-app.com
        - stream.barber-app.com
      tls:
        httpsRedirect: true
    - port:
        number: 443
        name: https
        protocol: HTTPS
      hosts:
        - api.barber-app.com
        - admin.barber-app.com
        - stream.barber-app.com
      tls:
        mode: SIMPLE
        credentialName: barber-app-tls

---
# Virtual Service for API routing
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: barber-app-api
  namespace: barber-app-mesh
spec:
  hosts:
    - api.barber-app.com
  gateways:
    - barber-app-gateway
  http:
    # Authentication Service
    - match:
        - uri:
            prefix: /api/auth
      route:
        - destination:
            host: auth-service
            port:
              number: 8080
          weight: 100
      fault:
        delay:
          percentage:
            value: 0.1
          fixedDelay: 5s
      retries:
        attempts: 3
        perTryTimeout: 2s

    # User Service
    - match:
        - uri:
            prefix: /api/users
      route:
        - destination:
            host: user-service
            port:
              number: 8080
          weight: 100
      timeout: 10s

    # Barber Service
    - match:
        - uri:
            prefix: /api/barbers
      route:
        - destination:
            host: barber-service
            port:
              number: 8080
          weight: 100

    # Booking Service
    - match:
        - uri:
            prefix: /api/bookings
      route:
        - destination:
            host: booking-service
            port:
              number: 8080
          weight: 100
      retries:
        attempts: 3
        perTryTimeout: 5s

    # Payment Service
    - match:
        - uri:
            prefix: /api/payments
      route:
        - destination:
            host: payment-service
            port:
              number: 8080
          weight: 100
      timeout: 30s

    # Notification Service
    - match:
        - uri:
            prefix: /api/notifications
      route:
        - destination:
            host: notification-service
            port:
              number: 8080
          weight: 100

    # Live Stream Service
    - match:
        - uri:
            prefix: /api/stream
      route:
        - destination:
            host: stream-service
            port:
              number: 8080
          weight: 100
      timeout: 60s

    # Analytics Service
    - match:
        - uri:
            prefix: /api/analytics
      route:
        - destination:
            host: analytics-service
            port:
              number: 8080
          weight: 100

    # Default route to main API
    - route:
        - destination:
            host: main-api-service
            port:
              number: 8080
          weight: 100

---
# Virtual Service for Admin Dashboard
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: barber-app-admin
  namespace: barber-app-mesh
spec:
  hosts:
    - admin.barber-app.com
  gateways:
    - barber-app-gateway
  http:
    - route:
        - destination:
            host: admin-dashboard-service
            port:
              number: 80
          weight: 100

---
# Virtual Service for Live Streaming
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: barber-app-stream
  namespace: barber-app-mesh
spec:
  hosts:
    - stream.barber-app.com
  gateways:
    - barber-app-gateway
  http:
    - route:
        - destination:
            host: stream-service
            port:
              number: 8080
          weight: 100
      timeout: 300s

---
# Destination Rules for Circuit Breaking and Load Balancing
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: auth-service-dr
  namespace: barber-app-mesh
spec:
  host: auth-service
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http1MaxPendingRequests: 50
        http2MaxRequests: 100
        maxRequestsPerConnection: 10
        maxRetries: 3
        consecutiveGatewayErrors: 5
        interval: 30s
        baseEjectionTime: 30s
        maxEjectionPercent: 50
    loadBalancer:
      simple: LEAST_CONN
    outlierDetection:
      consecutiveGatewayErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
  subsets:
    - name: v1
      labels:
        version: v1
    - name: v2
      labels:
        version: v2

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: booking-service-dr
  namespace: barber-app-mesh
spec:
  host: booking-service
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 200
      http:
        http1MaxPendingRequests: 100
        http2MaxRequests: 200
        maxRequestsPerConnection: 20
    loadBalancer:
      simple: ROUND_ROBIN
    outlierDetection:
      consecutiveGatewayErrors: 3
      interval: 30s
      baseEjectionTime: 30s

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: payment-service-dr
  namespace: barber-app-mesh
spec:
  host: payment-service
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 50
      http:
        http1MaxPendingRequests: 25
        http2MaxRequests: 50
        maxRequestsPerConnection: 5
    loadBalancer:
      simple: LEAST_CONN
    outlierDetection:
      consecutiveGatewayErrors: 2
      interval: 10s
      baseEjectionTime: 60s

---
# Service Entries for External Services
apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: stripe-api
  namespace: barber-app-mesh
spec:
  hosts:
    - api.stripe.com
  ports:
    - number: 443
      name: https
      protocol: HTTPS
  location: MESH_EXTERNAL
  resolution: DNS

---
apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: paymob-api
  namespace: barber-app-mesh
spec:
  hosts:
    - accept.paymob.com
  ports:
    - number: 443
      name: https
      protocol: HTTPS
  location: MESH_EXTERNAL
  resolution: DNS

---
apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: agora-api
  namespace: barber-app-mesh
spec:
  hosts:
    - api.agora.io
  ports:
    - number: 443
      name: https
      protocol: HTTPS
  location: MESH_EXTERNAL
  resolution: DNS

---
# Authorization Policies
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: auth-service-policy
  namespace: barber-app-mesh
spec:
  selector:
    matchLabels:
      app: auth-service
  rules:
    - from:
        - source:
            principals: ["cluster.local/ns/barber-app-mesh/sa/api-gateway"]
      to:
        - operation:
            methods: ["POST", "GET"]
            paths: ["/api/auth/*"]

---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: payment-service-policy
  namespace: barber-app-mesh
spec:
  selector:
    matchLabels:
      app: payment-service
  rules:
    - from:
        - source:
            principals: ["cluster.local/ns/barber-app-mesh/sa/booking-service"]
      to:
        - operation:
            methods: ["POST"]
            paths: ["/api/payments/process"]
    - from:
        - source:
            principals: ["cluster.local/ns/barber-app-mesh/sa/api-gateway"]
      to:
        - operation:
            methods: ["GET"]
            paths: ["/api/payments/status/*"]

---
# Peer Authentication for mTLS
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: barber-app-mesh
spec:
  mtls:
    mode: STRICT

---
# Request Authentication for JWT
apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  name: jwt-auth
  namespace: barber-app-mesh
spec:
  selector:
    matchLabels:
      app: api-gateway
  jwtRules:
    - issuer: "https://auth.barber-app.com"
      jwksUri: "https://auth.barber-app.com/.well-known/jwks.json"
      audiences:
        - "barber-app-api"

---
# Telemetry Configuration
apiVersion: telemetry.istio.io/v1alpha1
kind: Telemetry
metadata:
  name: default-metrics
  namespace: barber-app-mesh
spec:
  metrics:
    - providers:
        - name: prometheus
    - overrides:
        - match:
            metric: ALL_METRICS
          tagOverrides:
            request_id:
              operation: UPSERT
              value: "%{REQUEST_ID}"

---
# Envoy Filter for Rate Limiting
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: rate-limit-filter
  namespace: barber-app-mesh
spec:
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
      patch:
        operation: INSERT_BEFORE
        value:
          name: envoy.filters.http.local_ratelimit
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.local_ratelimit.v3.LocalRateLimit
            value:
              stat_prefix: rate_limiter
              token_bucket:
                max_tokens: 1000
                tokens_per_fill: 100
                fill_interval: 60s
              filter_enabled:
                runtime_key: rate_limit_enabled
                default_value:
                  numerator: 100
                  denominator: HUNDRED
              filter_enforced:
                runtime_key: rate_limit_enforced
                default_value:
                  numerator: 100
                  denominator: HUNDRED

---
# Wasm Plugin for Custom Logic
apiVersion: extensions.istio.io/v1alpha1
kind: WasmPlugin
metadata:
  name: barber-app-auth
  namespace: barber-app-mesh
spec:
  selector:
    matchLabels:
      app: api-gateway
  url: oci://registry.barber-app.com/wasm/auth-plugin:latest
  phase: AUTHN
  pluginConfig:
    jwt_secret: "your-jwt-secret"
    allowed_paths:
      - "/api/auth/login"
      - "/api/auth/register"
      - "/health"

---
# Traffic Split for Canary Deployment
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: booking-service-canary
  namespace: barber-app-mesh
spec:
  hosts:
    - booking-service
  http:
    - match:
        - headers:
            canary:
              exact: "true"
      route:
        - destination:
            host: booking-service
            subset: v2
          weight: 100
    - route:
        - destination:
            host: booking-service
            subset: v1
          weight: 90
        - destination:
            host: booking-service
            subset: v2
          weight: 10

---
# Fault Injection for Testing
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: fault-injection-test
  namespace: barber-app-mesh
spec:
  hosts:
    - booking-service
  http:
    - match:
        - headers:
            test-fault:
              exact: "delay"
      fault:
        delay:
          percentage:
            value: 100
          fixedDelay: 5s
      route:
        - destination:
            host: booking-service
    - match:
        - headers:
            test-fault:
              exact: "abort"
      fault:
        abort:
          percentage:
            value: 100
          httpStatus: 500
      route:
        - destination:
            host: booking-service

---
# Service Monitor for Prometheus
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: istio-mesh-metrics
  namespace: barber-app-mesh
spec:
  selector:
    matchLabels:
      app: istiod
  endpoints:
    - port: http-monitoring
      interval: 15s
      path: /stats/prometheus

---
# Network Policy for Additional Security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: mesh-network-policy
  namespace: barber-app-mesh
spec:
  podSelector: {}
  policyTypes:
    - Ingress
    - Egress
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: istio-system
    - from:
        - podSelector: {}
  egress:
    - to:
        - namespaceSelector:
            matchLabels:
              name: istio-system
    - to:
        - podSelector: {}
    - to: []
      ports:
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 53
        - protocol: UDP
          port: 53

---
# Horizontal Pod Autoscaler for Services
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: booking-service-hpa
  namespace: barber-app-mesh
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: booking-service
  minReplicas: 2
  maxReplicas: 20
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
    - type: Pods
      pods:
        metric:
          name: istio_request_total
        target:
          type: AverageValue
          averageValue: "100"

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: booking-service-pdb
  namespace: barber-app-mesh
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: booking-service
