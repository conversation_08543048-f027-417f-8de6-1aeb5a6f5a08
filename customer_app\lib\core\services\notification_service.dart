import 'package:flutter/foundation.dart';

class NotificationService {
  static Future<void> init() async {
    if (kDebugMode) {
      print('Notification service initialized');
    }
  }

  static Future<void> showNotification({
    required String title,
    required String body,
  }) async {
    if (kDebugMode) {
      print('Notification: $title - $body');
    }
  }

  static Future<String?> getToken() async {
    return 'mock_token';
  }

  static Future<void> subscribeToTopic(String topic) async {
    if (kDebugMode) {
      print('Subscribed to topic: $topic');
    }
  }

  static Future<void> unsubscribeFromTopic(String topic) async {
    if (kDebugMode) {
      print('Unsubscribed from topic: $topic');
    }
  }
}
