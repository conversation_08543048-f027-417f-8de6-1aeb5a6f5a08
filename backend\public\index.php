<?php

if (!defined('LARAVEL_START')) {
    define('LARAVEL_START', microtime(true));
}

/*
|--------------------------------------------------------------------------
| Barber App - Temporary Bootstrap
|--------------------------------------------------------------------------
|
| This is a temporary bootstrap file to get the application running
| while we set up the full Laravel environment.
|
*/

// Load the autoloader
$autoloaderResult = require __DIR__.'/../vendor/autoload.php';

// Simple health check
if (isset($_GET['health'])) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'OK',
        'message' => 'Barber App Backend is running',
        'timestamp' => date('Y-m-d H:i:s'),
        'autoloader' => $autoloaderResult
    ]);
    exit;
}

// API endpoint check
if (strpos($_SERVER['REQUEST_URI'], '/api/') !== false) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'Barber App API is ready',
        'version' => '1.0.0',
        'endpoints' => [
            'health' => '/health',
            'api' => '/api/v1',
            'admin' => '/admin'
        ]
    ]);
    exit;
}

// Default welcome page
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حلاق على بابك - Backend</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 600px;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .status {
            background: rgba(76, 175, 80, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        .info {
            background: rgba(33, 150, 243, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid rgba(33, 150, 243, 0.5);
        }
        .links {
            margin-top: 30px;
        }
        .links a {
            color: white;
            text-decoration: none;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
            margin: 0 10px;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 حلاق على بابك</h1>
        <h2>Backend Server</h2>

        <div class="status">
            ✅ الخادم يعمل بنجاح!
        </div>

        <div class="info">
            <strong>معلومات النظام:</strong><br>
            📅 التاريخ: <?php echo date('Y-m-d H:i:s'); ?><br>
            🔧 PHP Version: <?php echo PHP_VERSION; ?><br>
            📍 Server: <?php echo $_SERVER['SERVER_NAME'] ?? 'localhost'; ?><br>
            🌐 Port: <?php echo $_SERVER['SERVER_PORT'] ?? '80'; ?>
        </div>

        <div class="info">
            <strong>الخطوات التالية:</strong><br>
            1. تثبيت Composer وتشغيل: composer install<br>
            2. إعداد قاعدة البيانات<br>
            3. تشغيل: php artisan serve<br>
            4. تشغيل لوحة الإدارة
        </div>

        <div class="links">
            <a href="?health">Health Check</a>
            <a href="/api/v1">API Test</a>
            <a href="http://localhost:3000" target="_blank">لوحة الإدارة</a>
        </div>
    </div>
</body>
</html>
