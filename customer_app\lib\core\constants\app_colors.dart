import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2E7D32);
  static const Color primaryLight = Color(0xFF4CAF50);
  static const Color primaryDark = Color(0xFF1B5E20);
  
  // Secondary Colors
  static const Color secondary = Color(0xFFFF9800);
  static const Color secondaryLight = Color(0xFFFFB74D);
  static const Color secondaryDark = Color(0xFFE65100);
  
  // Accent Colors
  static const Color accent = Color(0xFF03DAC6);
  static const Color accentLight = Color(0xFF4AEDC4);
  static const Color accentDark = Color(0xFF00A896);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textWhite = Color(0xFFFFFFFF);
  
  // Background Colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderLight = Color(0xFFEEEEEE);
  static const Color borderDark = Color(0xFFBDBDBD);
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x33000000);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryLight, primary],
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondaryLight, secondary],
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentLight, accent],
  );
  
  // Service Type Colors
  static const Color haircut = Color(0xFF2196F3);
  static const Color styling = Color(0xFF9C27B0);
  static const Color fullService = Color(0xFFFF5722);
  static const Color vipService = Color(0xFFFFD700);
  
  // Rating Colors
  static const Color ratingGold = Color(0xFFFFD700);
  static const Color ratingEmpty = Color(0xFFE0E0E0);
  
  // Booking Status Colors
  static const Color pending = Color(0xFFFF9800);
  static const Color confirmed = Color(0xFF2196F3);
  static const Color inProgress = Color(0xFF9C27B0);
  static const Color completed = Color(0xFF4CAF50);
  static const Color cancelled = Color(0xFFF44336);
  
  // VIP Colors
  static const Color vipGold = Color(0xFFFFD700);
  static const Color vipSilver = Color(0xFFC0C0C0);
  static const Color vipBronze = Color(0xFFCD7F32);
  static const Color vipPlatinum = Color(0xFFE5E4E2);
  
  // Map Colors
  static const Color mapMarker = Color(0xFF2E7D32);
  static const Color mapRoute = Color(0xFF2196F3);
  static const Color mapUserLocation = Color(0xFFFF5722);
  
  // Chat Colors
  static const Color chatBubbleUser = Color(0xFF2E7D32);
  static const Color chatBubbleOther = Color(0xFFE0E0E0);
  static const Color chatOnline = Color(0xFF4CAF50);
  static const Color chatOffline = Color(0xFF9E9E9E);
  
  // Loyalty Colors
  static const Color loyaltyBronze = Color(0xFFCD7F32);
  static const Color loyaltySilver = Color(0xFFC0C0C0);
  static const Color loyaltyGold = Color(0xFFFFD700);
  static const Color loyaltyPlatinum = Color(0xFFE5E4E2);
  
  // Notification Colors
  static const Color notificationUnread = Color(0xFFFF5722);
  static const Color notificationRead = Color(0xFF9E9E9E);
  
  // Payment Colors
  static const Color paymentSuccess = Color(0xFF4CAF50);
  static const Color paymentPending = Color(0xFFFF9800);
  static const Color paymentFailed = Color(0xFFF44336);
  
  // Shimmer Colors
  static const Color shimmerBase = Color(0xFFE0E0E0);
  static const Color shimmerHighlight = Color(0xFFF5F5F5);
  
  // Dark Theme Colors
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFB3B3B3);
}
