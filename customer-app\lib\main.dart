import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/constants/app_constants.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/app_router.dart';
import 'core/utils/dependency_injection.dart';
import 'shared/services/auth_service.dart';
import 'shared/services/settings_service.dart';
import 'shared/services/notification_service.dart';
import 'shared/services/location_service.dart';
import 'features/auth/providers/auth_provider.dart';
import 'features/home/<USER>/home_provider.dart';
import 'features/booking/providers/booking_provider.dart';
import 'features/profile/providers/profile_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp();
  
  // Initialize Hive
  await Hive.initFlutter();
  
  // Initialize Dependencies
  await DependencyInjection.init();
  
  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );
  
  runApp(const BarberCustomerApp());
}

class BarberCustomerApp extends StatelessWidget {
  const BarberCustomerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => HomeProvider()),
        ChangeNotifierProvider(create: (_) => BookingProvider()),
        ChangeNotifierProvider(create: (_) => ProfileProvider()),
      ],
      child: GetMaterialApp(
        title: AppConstants.appName,
        debugShowCheckedModeBanner: false,
        
        // Localization
        locale: const Locale('ar', 'EG'),
        fallbackLocale: const Locale('en', 'US'),
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('ar', 'EG'),
          Locale('en', 'US'),
        ],
        
        // Theme
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.light,
        
        // Routing
        initialRoute: AppRouter.splash,
        getPages: AppRouter.routes,
        
        // Global settings
        defaultTransition: Transition.cupertino,
        transitionDuration: const Duration(milliseconds: 300),
        
        // Builder for global configurations
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(
              textScaleFactor: 1.0, // Prevent text scaling
            ),
            child: Directionality(
              textDirection: TextDirection.rtl,
              child: child!,
            ),
          );
        },
      ),
    );
  }
}

class AppInitializer extends StatefulWidget {
  final Widget child;
  
  const AppInitializer({
    super.key,
    required this.child,
  });

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  bool _isInitialized = false;
  String _initializationStatus = 'جاري تحميل التطبيق...';

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      setState(() {
        _initializationStatus = 'جاري تحميل الإعدادات...';
      });
      
      // Initialize Settings Service
      await Get.find<SettingsService>().initialize();
      
      setState(() {
        _initializationStatus = 'جاري تحميل بيانات المستخدم...';
      });
      
      // Initialize Auth Service
      await Get.find<AuthService>().initialize();
      
      setState(() {
        _initializationStatus = 'جاري إعداد الإشعارات...';
      });
      
      // Initialize Notification Service
      await Get.find<NotificationService>().initialize();
      
      setState(() {
        _initializationStatus = 'جاري تحديد الموقع...';
      });
      
      // Initialize Location Service
      await Get.find<LocationService>().initialize();
      
      setState(() {
        _initializationStatus = 'اكتمل التحميل';
        _isInitialized = true;
      });
      
    } catch (e) {
      setState(() {
        _initializationStatus = 'حدث خطأ في التحميل';
      });
      
      // Show error dialog
      Get.dialog(
        AlertDialog(
          title: const Text('خطأ في التحميل'),
          content: Text('حدث خطأ أثناء تحميل التطبيق: ${e.toString()}'),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
                _initializeApp(); // Retry
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: const LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Icon(
                  Icons.content_cut,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // App Name
              const Text(
                AppConstants.appName,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2c3e50),
                ),
              ),
              
              const SizedBox(height: 8),
              
              const Text(
                'خدمة الحلاقة المنزلية',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF7f8c8d),
                ),
              ),
              
              const SizedBox(height: 48),
              
              // Loading Indicator
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667eea)),
              ),
              
              const SizedBox(height: 16),
              
              // Status Text
              Text(
                _initializationStatus,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF7f8c8d),
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return widget.child;
  }
}
