name: barber_customer_app
description: "حلاق على بابك - تطبيق العملاء مع التقنيات المستقبلية المتقدمة"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Core dependencies
  cupertino_icons: ^1.0.6
  provider: ^6.1.1
  shared_preferences: ^2.2.2
  http: ^1.1.0
  dio: ^5.3.2
  
  # UI & Design
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  animations: ^2.0.8
  flutter_staggered_animations: ^1.1.1
  
  # Navigation & Routing
  go_router: ^12.1.1
  auto_route: ^7.8.4
  
  # State Management
  bloc: ^8.1.2
  flutter_bloc: ^8.1.3
  get_it: ^7.6.4
  injectable: ^2.3.2
  
  # Storage & Database
  sqflite: ^2.3.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.1
  
  # Network & API
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  connectivity_plus: ^5.0.1
  
  # Authentication & Security
  local_auth: ^2.1.6
  crypto: ^3.0.3
  encrypt: ^5.0.1
  flutter_secure_storage: ^9.0.0
  
  # Location & Maps
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  google_maps_flutter: ^2.5.0
  location: ^5.0.3
  
  # Camera & Media
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  video_player: ^2.7.2
  photo_view: ^0.14.0
  
  # Permissions
  permission_handler: ^11.0.1
  
  # Firebase
  firebase_core: ^2.17.0
  firebase_auth: ^4.12.0
  firebase_messaging: ^14.7.0
  firebase_analytics: ^10.6.0
  firebase_crashlytics: ^3.4.0
  firebase_storage: ^11.4.0
  cloud_firestore: ^4.12.0
  
  # Push Notifications
  flutter_local_notifications: ^16.1.0
  
  # Sensors & Hardware
  sensors_plus: ^4.0.2
  device_info_plus: ^9.1.0
  battery_plus: ^5.0.2
  
  # Advanced Features - AR/VR
  arcore_flutter_plugin: ^0.0.9
  ar_flutter_plugin: ^0.7.3
  
  # Advanced Features - AI/ML
  tflite_flutter: ^0.10.4
  camera_deep_ar: ^0.0.3
  
  # Advanced Features - Voice & Speech
  speech_to_text: ^6.6.0
  flutter_tts: ^3.8.5
  
  # Advanced Features - Biometrics
  flutter_face_api: ^2.0.0
  
  # Advanced Features - IoT
  flutter_bluetooth_serial: ^0.4.0
  wifi_iot: ^0.3.18+1
  
  # Advanced Features - Quantum & Space
  # Note: These are conceptual packages for the advanced features
  quantum_flutter: ^1.0.0  # Custom package for quantum features
  space_computing: ^1.0.0  # Custom package for space computing
  holographic_ui: ^1.0.0   # Custom package for holographic interface
  telepathic_sdk: ^1.0.0   # Custom package for telepathic interface
  
  # Utilities
  intl: ^0.18.1
  url_launcher: ^6.2.1
  share_plus: ^7.2.1
  package_info_plus: ^4.2.0
  flutter_dotenv: ^5.1.0
  uuid: ^4.1.0
  
  # Animation & Effects
  rive: ^0.12.4
  flare_flutter: ^3.0.2
  
  # Charts & Graphs
  fl_chart: ^0.64.0
  syncfusion_flutter_charts: ^23.1.44
  
  # QR Code
  qr_code_scanner: ^1.0.1
  qr_flutter: ^4.1.0
  
  # WebView
  webview_flutter: ^4.4.2
  
  # File handling
  file_picker: ^6.1.1
  open_file: ^3.3.2
  
  # Date & Time
  table_calendar: ^3.0.9
  
  # Math & Calculations
  vector_math: ^2.1.4
  
  # Development tools
  logger: ^2.0.2+1
  pretty_dio_logger: ^1.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  
  # Code generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  injectable_generator: ^2.4.1
  auto_route_generator: ^7.3.2
  hive_generator: ^2.0.1
  
  # Testing
  mockito: ^5.4.2
  bloc_test: ^9.1.4
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  generate: true

  assets:
    # Images
    - assets/images/
    - assets/images/logos/
    - assets/images/icons/
    - assets/images/backgrounds/
    - assets/images/avatars/
    - assets/images/services/
    - assets/images/onboarding/
    
    # Animations
    - assets/animations/
    - assets/lottie/
    - assets/rive/
    
    # 3D Models (for holographic features)
    - assets/models/
    - assets/models/barber_chair/
    - assets/models/barber_tools/
    - assets/models/hair_styles/
    - assets/models/avatars/
    
    # Textures (for holographic features)
    - assets/textures/
    - assets/textures/chair_texture.png
    - assets/textures/tools_texture.png
    - assets/textures/hair_texture.png
    - assets/textures/avatar_texture.png
    
    # Audio
    - assets/audio/
    - assets/audio/sounds/
    - assets/audio/voices/
    
    # Fonts
    - assets/fonts/
    
    # Configuration
    - .env
    - assets/config/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
    
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
    
    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal-Regular.ttf
        - asset: assets/fonts/Tajawal-Bold.ttf
          weight: 700
        - asset: assets/fonts/Tajawal-Light.ttf
          weight: 300

# Flutter configuration
flutter_intl:
  enabled: true
  class_name: AppLocalizations
  main_locale: ar
  arb_dir: lib/l10n
  output_dir: lib/generated

# Custom configuration for advanced features
barber_app_config:
  quantum_security:
    enabled: true
    encryption_level: "post_quantum"
  
  space_computing:
    enabled: true
    satellite_endpoints:
      - "https://barber-sat-1.space"
      - "https://barber-sat-2.space"
      - "https://barber-sat-3.space"
      - "https://barber-quantum-hub.space"
  
  holographic_interface:
    enabled: true
    supported_devices:
      - "HoloLens"
      - "Magic Leap"
      - "Meta Quest Pro"
  
  telepathic_interface:
    enabled: true
    bci_devices:
      - "Neuralink"
      - "Emotiv EPOC X"
      - "OpenBCI"
  
  neuro_ai:
    enabled: true
    models:
      - "customer_behavior_prediction"
      - "demand_forecasting"
      - "emotion_analysis"
      - "conversation_ai"
