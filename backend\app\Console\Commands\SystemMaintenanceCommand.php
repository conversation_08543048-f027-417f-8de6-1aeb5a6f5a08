<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SystemMaintenanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'system:maintenance 
                            {action : Action to perform (enable|disable|status|cleanup|optimize)}
                            {--reason= : Reason for maintenance}
                            {--duration= : Expected duration in minutes}
                            {--notify : Send notifications to users}';

    /**
     * The console command description.
     */
    protected $description = 'Manage system maintenance mode and perform maintenance tasks';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');
        
        switch ($action) {
            case 'enable':
                return $this->enableMaintenance();
            case 'disable':
                return $this->disableMaintenance();
            case 'status':
                return $this->showStatus();
            case 'cleanup':
                return $this->performCleanup();
            case 'optimize':
                return $this->optimizeSystem();
            default:
                $this->error("Invalid action: {$action}");
                return 1;
        }
    }

    protected function enableMaintenance()
    {
        $reason = $this->option('reason') ?? 'System maintenance';
        $duration = $this->option('duration') ?? 60;
        $notify = $this->option('notify');

        $this->info('Enabling maintenance mode...');

        // Enable Laravel maintenance mode
        $this->call('down', [
            '--message' => $reason,
            '--retry' => 60,
            '--secret' => config('app.maintenance_secret', 'barber-admin-secret')
        ]);

        // Store maintenance info
        Cache::put('maintenance_info', [
            'enabled_at' => now(),
            'reason' => $reason,
            'duration' => $duration,
            'enabled_by' => 'system'
        ], now()->addHours(24));

        // Update system settings
        DB::table('system_settings')
            ->where('key', 'maintenance_mode')
            ->update(['value' => 'true']);

        // Send notifications if requested
        if ($notify) {
            $this->sendMaintenanceNotifications($reason, $duration, 'started');
        }

        // Log maintenance start
        Log::info('System maintenance enabled', [
            'reason' => $reason,
            'duration' => $duration,
            'enabled_at' => now()
        ]);

        $this->info("✅ Maintenance mode enabled successfully!");
        $this->info("Reason: {$reason}");
        $this->info("Expected duration: {$duration} minutes");
        
        return 0;
    }

    protected function disableMaintenance()
    {
        $this->info('Disabling maintenance mode...');

        // Get maintenance info
        $maintenanceInfo = Cache::get('maintenance_info');

        // Disable Laravel maintenance mode
        $this->call('up');

        // Update system settings
        DB::table('system_settings')
            ->where('key', 'maintenance_mode')
            ->update(['value' => 'false']);

        // Clear maintenance cache
        Cache::forget('maintenance_info');

        // Send notifications
        if ($maintenanceInfo) {
            $this->sendMaintenanceNotifications(
                $maintenanceInfo['reason'] ?? 'System maintenance',
                0,
                'completed'
            );
        }

        // Log maintenance end
        Log::info('System maintenance disabled', [
            'disabled_at' => now(),
            'duration' => $maintenanceInfo ? now()->diffInMinutes($maintenanceInfo['enabled_at']) : null
        ]);

        $this->info("✅ Maintenance mode disabled successfully!");
        
        return 0;
    }

    protected function showStatus()
    {
        $isDown = app()->isDownForMaintenance();
        $maintenanceInfo = Cache::get('maintenance_info');

        $this->line('=== System Maintenance Status ===');
        
        if ($isDown) {
            $this->error('🔴 System is currently in maintenance mode');
            
            if ($maintenanceInfo) {
                $this->line("Reason: {$maintenanceInfo['reason']}");
                $this->line("Started: {$maintenanceInfo['enabled_at']}");
                $this->line("Duration: {$maintenanceInfo['duration']} minutes");
                
                $elapsed = now()->diffInMinutes($maintenanceInfo['enabled_at']);
                $remaining = max(0, $maintenanceInfo['duration'] - $elapsed);
                
                $this->line("Elapsed: {$elapsed} minutes");
                $this->line("Remaining: {$remaining} minutes");
            }
        } else {
            $this->info('🟢 System is operational');
        }

        // Show system health
        $this->showSystemHealth();
        
        return 0;
    }

    protected function performCleanup()
    {
        $this->info('🧹 Starting system cleanup...');

        $cleaned = 0;

        // Clean old logs
        $this->info('Cleaning old logs...');
        $logFiles = Storage::disk('local')->files('logs');
        $oldLogs = array_filter($logFiles, function ($file) {
            $fileTime = Storage::disk('local')->lastModified($file);
            return Carbon::createFromTimestamp($fileTime)->lt(now()->subDays(30));
        });
        
        foreach ($oldLogs as $log) {
            Storage::disk('local')->delete($log);
            $cleaned++;
        }
        $this->line("✅ Cleaned {$cleaned} old log files");

        // Clean old notifications
        $this->info('Cleaning old notifications...');
        $deletedNotifications = DB::table('notifications')
            ->where('created_at', '<', now()->subDays(90))
            ->delete();
        $this->line("✅ Cleaned {$deletedNotifications} old notifications");

        // Clean expired tokens
        $this->info('Cleaning expired tokens...');
        $deletedTokens = DB::table('personal_access_tokens')
            ->where('expires_at', '<', now())
            ->delete();
        $this->line("✅ Cleaned {$deletedTokens} expired tokens");

        // Clean old analytics data
        $this->info('Cleaning old analytics data...');
        $deletedAnalytics = DB::table('analytics')
            ->where('created_at', '<', now()->subDays(365))
            ->delete();
        $this->line("✅ Cleaned {$deletedAnalytics} old analytics records");

        // Clean temporary files
        $this->info('Cleaning temporary files...');
        $tempFiles = Storage::disk('local')->files('temp');
        foreach ($tempFiles as $file) {
            Storage::disk('local')->delete($file);
        }
        $this->line("✅ Cleaned " . count($tempFiles) . " temporary files");

        // Clean cache
        $this->info('Clearing application cache...');
        Cache::flush();
        $this->line("✅ Application cache cleared");

        // Clean failed jobs
        $this->info('Cleaning failed jobs...');
        $deletedJobs = DB::table('failed_jobs')
            ->where('failed_at', '<', now()->subDays(7))
            ->delete();
        $this->line("✅ Cleaned {$deletedJobs} old failed jobs");

        $this->info('🎉 System cleanup completed successfully!');
        
        return 0;
    }

    protected function optimizeSystem()
    {
        $this->info('🚀 Starting system optimization...');

        // Clear and cache config
        $this->info('Optimizing configuration...');
        $this->call('config:clear');
        $this->call('config:cache');
        $this->line('✅ Configuration optimized');

        // Clear and cache routes
        $this->info('Optimizing routes...');
        $this->call('route:clear');
        $this->call('route:cache');
        $this->line('✅ Routes optimized');

        // Clear and cache views
        $this->info('Optimizing views...');
        $this->call('view:clear');
        $this->call('view:cache');
        $this->line('✅ Views optimized');

        // Optimize autoloader
        $this->info('Optimizing autoloader...');
        $this->call('optimize');
        $this->line('✅ Autoloader optimized');

        // Update search indexes
        $this->info('Updating search indexes...');
        $this->call('search:update-indexes');
        $this->line('✅ Search indexes updated');

        // Optimize database
        $this->info('Optimizing database...');
        $this->optimizeDatabase();
        $this->line('✅ Database optimized');

        // Clear opcache if available
        if (function_exists('opcache_reset')) {
            opcache_reset();
            $this->line('✅ OPcache cleared');
        }

        $this->info('🎉 System optimization completed successfully!');
        
        return 0;
    }

    protected function optimizeDatabase()
    {
        $tables = DB::select('SHOW TABLES');
        $tableColumn = 'Tables_in_' . config('database.connections.mysql.database');

        foreach ($tables as $table) {
            $tableName = $table->$tableColumn;
            DB::statement("OPTIMIZE TABLE `{$tableName}`");
        }
    }

    protected function sendMaintenanceNotifications($reason, $duration, $status)
    {
        $message = match($status) {
            'started' => "نعتذر، النظام تحت الصيانة حالياً. السبب: {$reason}. المدة المتوقعة: {$duration} دقيقة.",
            'completed' => "تم الانتهاء من صيانة النظام. النظام متاح الآن للاستخدام.",
            default => "تحديث حالة الصيانة"
        };

        // Send to all active users
        $users = DB::table('users')
            ->where('is_active', true)
            ->whereNotNull('fcm_token')
            ->select('id', 'fcm_token')
            ->get();

        foreach ($users as $user) {
            DB::table('notifications')->insert([
                'user_id' => $user->id,
                'title' => 'إشعار صيانة النظام',
                'body' => $message,
                'type' => 'system_maintenance',
                'data' => json_encode([
                    'status' => $status,
                    'reason' => $reason,
                    'duration' => $duration
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        $this->line("📱 Sent notifications to " . count($users) . " users");
    }

    protected function showSystemHealth()
    {
        $this->line("\n=== System Health Check ===");

        // Database connection
        try {
            DB::connection()->getPdo();
            $this->info('🟢 Database: Connected');
        } catch (\Exception $e) {
            $this->error('🔴 Database: Disconnected');
        }

        // Redis connection
        try {
            Cache::store('redis')->get('test');
            $this->info('🟢 Redis: Connected');
        } catch (\Exception $e) {
            $this->error('🔴 Redis: Disconnected');
        }

        // Storage
        $diskSpace = disk_free_space(storage_path());
        $diskSpaceGB = round($diskSpace / 1024 / 1024 / 1024, 2);
        
        if ($diskSpaceGB > 10) {
            $this->info("🟢 Storage: {$diskSpaceGB} GB free");
        } elseif ($diskSpaceGB > 5) {
            $this->comment("🟡 Storage: {$diskSpaceGB} GB free (Warning)");
        } else {
            $this->error("🔴 Storage: {$diskSpaceGB} GB free (Critical)");
        }

        // Memory usage
        $memoryUsage = memory_get_usage(true);
        $memoryUsageMB = round($memoryUsage / 1024 / 1024, 2);
        $this->info("📊 Memory Usage: {$memoryUsageMB} MB");

        // Queue status
        $failedJobs = DB::table('failed_jobs')->count();
        if ($failedJobs == 0) {
            $this->info('🟢 Queue: No failed jobs');
        } else {
            $this->error("🔴 Queue: {$failedJobs} failed jobs");
        }

        // Recent errors
        $recentErrors = DB::table('logs')
            ->where('level', 'error')
            ->where('created_at', '>', now()->subHour())
            ->count();
            
        if ($recentErrors == 0) {
            $this->info('🟢 Errors: No recent errors');
        } else {
            $this->error("🔴 Errors: {$recentErrors} errors in last hour");
        }
    }
}
