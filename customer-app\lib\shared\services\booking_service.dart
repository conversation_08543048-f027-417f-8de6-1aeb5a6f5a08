import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../../core/constants/app_constants.dart';
import '../../core/network/api_client.dart';
import '../../core/network/api_endpoints.dart';
import '../models/booking_model.dart';
import '../models/barber_model.dart';
import '../models/service_model.dart';
import 'cache_service.dart';

abstract class BookingService {
  Future<List<BarberModel>> getAvailableBarbers({
    required String serviceId,
    required DateTime date,
    required String timeSlot,
    String? cityId,
    String? areaId,
    double? latitude,
    double? longitude,
  });
  
  Future<List<String>> getAvailableTimeSlots({
    required String barberId,
    required String serviceId,
    required DateTime date,
  });
  
  Future<BookingResult> createBooking({
    required String barberId,
    required String serviceId,
    required DateTime date,
    required String timeSlot,
    required String addressId,
    String? notes,
    bool isVip = false,
  });
  
  Future<List<BookingModel>> getMyBookings({
    String? status,
    int page = 1,
    int limit = 20,
  });
  
  Future<BookingModel?> getBookingDetails(String bookingId);
  
  Future<bool> cancelBooking(String bookingId, {String? reason});
  
  Future<bool> rescheduleBooking({
    required String bookingId,
    required DateTime newDate,
    required String newTimeSlot,
  });
  
  Future<bool> rateBooking({
    required String bookingId,
    required double rating,
    String? review,
  });
  
  Future<double> calculateBookingCost({
    required String serviceId,
    required String barberId,
    required DateTime date,
    required String timeSlot,
    bool isVip = false,
    bool isEmergency = false,
  });
}

class BookingServiceImpl implements BookingService {
  final ApiClient _apiClient;
  final CacheService _cacheService;

  BookingServiceImpl({
    required ApiClient apiClient,
    required CacheService cacheService,
  }) : _apiClient = apiClient,
       _cacheService = cacheService;

  @override
  Future<List<BarberModel>> getAvailableBarbers({
    required String serviceId,
    required DateTime date,
    required String timeSlot,
    String? cityId,
    String? areaId,
    double? latitude,
    double? longitude,
  }) async {
    try {
      final response = await _apiClient.get(
        ApiEndpoints.availableBarbers,
        queryParameters: {
          'service_id': serviceId,
          'date': date.toIso8601String().split('T')[0],
          'time_slot': timeSlot,
          if (cityId != null) 'city_id': cityId,
          if (areaId != null) 'area_id': areaId,
          if (latitude != null) 'latitude': latitude,
          if (longitude != null) 'longitude': longitude,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> barbersJson = response.data['data'];
        return barbersJson.map((json) => BarberModel.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      throw BookingException('فشل في جلب الحلاقين المتاحين: ${_handleError(e)}');
    }
  }

  @override
  Future<List<String>> getAvailableTimeSlots({
    required String barberId,
    required String serviceId,
    required DateTime date,
  }) async {
    try {
      final cacheKey = 'time_slots_${barberId}_${serviceId}_${date.toIso8601String().split('T')[0]}';
      
      // Check cache first
      final cachedSlots = await _cacheService.get(cacheKey);
      if (cachedSlots != null) {
        return List<String>.from(cachedSlots);
      }

      final response = await _apiClient.get(
        ApiEndpoints.availableTimeSlots,
        queryParameters: {
          'barber_id': barberId,
          'service_id': serviceId,
          'date': date.toIso8601String().split('T')[0],
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> slotsJson = response.data['data'];
        final slots = slotsJson.map((slot) => slot.toString()).toList();
        
        // Cache for 30 minutes
        await _cacheService.set(cacheKey, slots, 
            duration: const Duration(minutes: 30));
        
        return slots;
      }

      return [];
    } catch (e) {
      throw BookingException('فشل في جلب المواعيد المتاحة: ${_handleError(e)}');
    }
  }

  @override
  Future<BookingResult> createBooking({
    required String barberId,
    required String serviceId,
    required DateTime date,
    required String timeSlot,
    required String addressId,
    String? notes,
    bool isVip = false,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.createBooking,
        data: {
          'barber_id': barberId,
          'service_id': serviceId,
          'date': date.toIso8601String().split('T')[0],
          'time_slot': timeSlot,
          'address_id': addressId,
          'notes': notes,
          'is_vip': isVip,
        },
      );

      if (response.statusCode == 201) {
        final bookingData = response.data['data'];
        final booking = BookingModel.fromJson(bookingData);
        
        // Clear related caches
        await _clearBookingCaches(barberId, serviceId, date);
        
        return BookingResult(
          success: true,
          message: 'تم إنشاء الحجز بنجاح',
          booking: booking,
        );
      }

      return BookingResult(
        success: false,
        message: response.data['message'] ?? 'فشل في إنشاء الحجز',
      );
    } catch (e) {
      return BookingResult(
        success: false,
        message: _handleError(e),
      );
    }
  }

  @override
  Future<List<BookingModel>> getMyBookings({
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        ApiEndpoints.myBookings,
        queryParameters: {
          if (status != null) 'status': status,
          'page': page,
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> bookingsJson = response.data['data'];
        return bookingsJson.map((json) => BookingModel.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      throw BookingException('فشل في جلب الحجوزات: ${_handleError(e)}');
    }
  }

  @override
  Future<BookingModel?> getBookingDetails(String bookingId) async {
    try {
      final response = await _apiClient.get('${ApiEndpoints.bookingDetails}/$bookingId');

      if (response.statusCode == 200) {
        return BookingModel.fromJson(response.data['data']);
      }

      return null;
    } catch (e) {
      throw BookingException('فشل في جلب تفاصيل الحجز: ${_handleError(e)}');
    }
  }

  @override
  Future<bool> cancelBooking(String bookingId, {String? reason}) async {
    try {
      final response = await _apiClient.post(
        '${ApiEndpoints.cancelBooking}/$bookingId',
        data: {
          if (reason != null) 'reason': reason,
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      throw BookingException('فشل في إلغاء الحجز: ${_handleError(e)}');
    }
  }

  @override
  Future<bool> rescheduleBooking({
    required String bookingId,
    required DateTime newDate,
    required String newTimeSlot,
  }) async {
    try {
      final response = await _apiClient.post(
        '${ApiEndpoints.rescheduleBooking}/$bookingId',
        data: {
          'date': newDate.toIso8601String().split('T')[0],
          'time_slot': newTimeSlot,
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      throw BookingException('فشل في إعادة جدولة الحجز: ${_handleError(e)}');
    }
  }

  @override
  Future<bool> rateBooking({
    required String bookingId,
    required double rating,
    String? review,
  }) async {
    try {
      final response = await _apiClient.post(
        '${ApiEndpoints.rateBooking}/$bookingId',
        data: {
          'rating': rating,
          if (review != null) 'review': review,
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      throw BookingException('فشل في تقييم الحجز: ${_handleError(e)}');
    }
  }

  @override
  Future<double> calculateBookingCost({
    required String serviceId,
    required String barberId,
    required DateTime date,
    required String timeSlot,
    bool isVip = false,
    bool isEmergency = false,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.calculateBookingCost,
        data: {
          'service_id': serviceId,
          'barber_id': barberId,
          'date': date.toIso8601String().split('T')[0],
          'time_slot': timeSlot,
          'is_vip': isVip,
          'is_emergency': isEmergency,
        },
      );

      if (response.statusCode == 200) {
        return (response.data['data']['total_cost'] as num).toDouble();
      }

      return 0.0;
    } catch (e) {
      throw BookingException('فشل في حساب تكلفة الحجز: ${_handleError(e)}');
    }
  }

  Future<void> _clearBookingCaches(String barberId, String serviceId, DateTime date) async {
    final dateStr = date.toIso8601String().split('T')[0];
    final cacheKey = 'time_slots_${barberId}_${serviceId}_$dateStr';
    await _cacheService.remove(cacheKey);
  }

  String _handleError(dynamic error) {
    if (error is DioException) {
      if (error.response?.statusCode == 422) {
        final errors = error.response?.data['errors'];
        if (errors != null && errors is Map) {
          final firstError = errors.values.first;
          if (firstError is List && firstError.isNotEmpty) {
            return firstError.first.toString();
          }
        }
      }
      
      return error.response?.data['message'] ?? AppConstants.networkError;
    }
    
    return AppConstants.unknownError;
  }
}

class BookingResult {
  final bool success;
  final String message;
  final BookingModel? booking;

  BookingResult({
    required this.success,
    required this.message,
    this.booking,
  });
}

class BookingException implements Exception {
  final String message;
  
  BookingException(this.message);
  
  @override
  String toString() => message;
}
