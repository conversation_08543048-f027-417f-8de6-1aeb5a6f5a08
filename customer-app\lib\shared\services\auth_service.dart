import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';

import '../../core/constants/app_constants.dart';
import '../../core/network/api_client.dart';
import '../../core/network/api_endpoints.dart';
import '../models/user_model.dart';
import 'storage_service.dart';
import 'settings_service.dart';

abstract class AuthService {
  Future<bool> isLoggedIn();
  Future<UserModel?> getCurrentUser();
  Future<AuthResult> login({required String phone, required String password, bool rememberMe = false});
  Future<AuthResult> register({required Map<String, dynamic> userData});
  Future<AuthResult> verifyPhone({required String phone, required String otp});
  Future<AuthResult> resendOtp({required String phone});
  Future<AuthResult> forgotPassword({required String phone});
  Future<AuthResult> resetPassword({required String phone, required String otp, required String newPassword});
  Future<bool> refreshToken();
  Future<bool> verifyToken();
  Future<void> logout();
  Future<void> initialize();
}

class AuthServiceImpl implements AuthService {
  final ApiClient _apiClient;
  final StorageService _storageService;
  final SettingsService _settingsService;
  
  UserModel? _currentUser;
  String? _currentToken;

  AuthServiceImpl({
    required ApiClient apiClient,
    required StorageService storageService,
    required SettingsService settingsService,
  }) : _apiClient = apiClient,
       _storageService = storageService,
       _settingsService = settingsService;

  @override
  Future<void> initialize() async {
    _currentToken = await _storageService.getString(AppConstants.tokenKey);
    
    if (_currentToken != null) {
      final userJson = await _storageService.getString(AppConstants.userKey);
      if (userJson != null) {
        try {
          _currentUser = UserModel.fromJson(userJson);
        } catch (e) {
          await logout();
        }
      }
    }
  }

  @override
  Future<bool> isLoggedIn() async {
    return _currentToken != null && _currentUser != null;
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    return _currentUser;
  }

  @override
  Future<AuthResult> login({
    required String phone, 
    required String password, 
    bool rememberMe = false
  }) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.login,
        data: {
          'phone': phone,
          'password': password,
          'remember_me': rememberMe,
          'device_name': await _getDeviceName(),
        },
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];
        
        _currentToken = data['token'];
        _currentUser = UserModel.fromJson(data['user']);
        
        await _storageService.setString(AppConstants.tokenKey, _currentToken!);
        await _storageService.setString(AppConstants.userKey, _currentUser!.toJson());
        
        return AuthResult(success: true, message: 'تم تسجيل الدخول بنجاح');
      } else {
        return AuthResult(
          success: false, 
          message: response.data['message'] ?? 'فشل في تسجيل الدخول'
        );
      }
    } catch (e) {
      return AuthResult(
        success: false, 
        message: _handleError(e)
      );
    }
  }

  @override
  Future<AuthResult> register({required Map<String, dynamic> userData}) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.register,
        data: {
          ...userData,
          'device_name': await _getDeviceName(),
        },
      );

      if (response.statusCode == 201) {
        return AuthResult(
          success: true, 
          message: 'تم إنشاء الحساب بنجاح، يرجى تأكيد رقم الهاتف'
        );
      } else {
        return AuthResult(
          success: false, 
          message: response.data['message'] ?? 'فشل في إنشاء الحساب'
        );
      }
    } catch (e) {
      return AuthResult(
        success: false, 
        message: _handleError(e)
      );
    }
  }

  @override
  Future<AuthResult> verifyPhone({required String phone, required String otp}) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.verifyPhone,
        data: {
          'phone': phone,
          'otp': otp,
          'device_name': await _getDeviceName(),
        },
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];
        
        _currentToken = data['token'];
        _currentUser = UserModel.fromJson(data['user']);
        
        await _storageService.setString(AppConstants.tokenKey, _currentToken!);
        await _storageService.setString(AppConstants.userKey, _currentUser!.toJson());
        
        return AuthResult(success: true, message: 'تم تأكيد رقم الهاتف بنجاح');
      } else {
        return AuthResult(
          success: false, 
          message: response.data['message'] ?? 'رمز التأكيد غير صحيح'
        );
      }
    } catch (e) {
      return AuthResult(
        success: false, 
        message: _handleError(e)
      );
    }
  }

  @override
  Future<AuthResult> resendOtp({required String phone}) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.resendOtp,
        data: {'phone': phone},
      );

      if (response.statusCode == 200) {
        return AuthResult(success: true, message: 'تم إرسال رمز التأكيد مرة أخرى');
      } else {
        return AuthResult(
          success: false, 
          message: response.data['message'] ?? 'فشل في إرسال رمز التأكيد'
        );
      }
    } catch (e) {
      return AuthResult(
        success: false, 
        message: _handleError(e)
      );
    }
  }

  @override
  Future<AuthResult> forgotPassword({required String phone}) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.forgotPassword,
        data: {'phone': phone},
      );

      if (response.statusCode == 200) {
        return AuthResult(success: true, message: 'تم إرسال رمز إعادة تعيين كلمة المرور');
      } else {
        return AuthResult(
          success: false, 
          message: response.data['message'] ?? 'فشل في إرسال رمز إعادة التعيين'
        );
      }
    } catch (e) {
      return AuthResult(
        success: false, 
        message: _handleError(e)
      );
    }
  }

  @override
  Future<AuthResult> resetPassword({
    required String phone, 
    required String otp, 
    required String newPassword
  }) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.resetPassword,
        data: {
          'phone': phone,
          'otp': otp,
          'password': newPassword,
          'password_confirmation': newPassword,
        },
      );

      if (response.statusCode == 200) {
        return AuthResult(success: true, message: 'تم تغيير كلمة المرور بنجاح');
      } else {
        return AuthResult(
          success: false, 
          message: response.data['message'] ?? 'فشل في تغيير كلمة المرور'
        );
      }
    } catch (e) {
      return AuthResult(
        success: false, 
        message: _handleError(e)
      );
    }
  }

  @override
  Future<bool> refreshToken() async {
    try {
      if (_currentToken == null) return false;
      
      final response = await _apiClient.post(ApiEndpoints.refreshToken);
      
      if (response.statusCode == 200) {
        final data = response.data['data'];
        _currentToken = data['token'];
        
        await _storageService.setString(AppConstants.tokenKey, _currentToken!);
        return true;
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> verifyToken() async {
    try {
      if (_currentToken == null) return false;
      
      final response = await _apiClient.get('/user/profile');
      
      if (response.statusCode == 200) {
        final userData = response.data['data'];
        _currentUser = UserModel.fromJson(userData);
        await _storageService.setString(AppConstants.userKey, _currentUser!.toJson());
        return true;
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> logout() async {
    try {
      if (_currentToken != null) {
        await _apiClient.post(ApiEndpoints.logout);
      }
    } catch (e) {
      // Ignore logout errors
    } finally {
      _currentToken = null;
      _currentUser = null;
      
      await _storageService.remove(AppConstants.tokenKey);
      await _storageService.remove(AppConstants.userKey);
      
      // Clear Hive boxes
      final userBox = await Hive.openBox(AppConstants.userBox);
      await userBox.clear();
    }
  }

  Future<String> _getDeviceName() async {
    // Get device name for token identification
    return 'Flutter App';
  }

  String _handleError(dynamic error) {
    if (error is DioException) {
      if (error.response?.statusCode == 422) {
        final errors = error.response?.data['errors'];
        if (errors != null && errors is Map) {
          final firstError = errors.values.first;
          if (firstError is List && firstError.isNotEmpty) {
            return firstError.first.toString();
          }
        }
      }
      
      return error.response?.data['message'] ?? AppConstants.networkError;
    }
    
    return AppConstants.unknownError;
  }
}

class AuthResult {
  final bool success;
  final String message;
  final Map<String, dynamic>? data;

  AuthResult({
    required this.success,
    required this.message,
    this.data,
  });
}
