<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, LogsActivity, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'user_type',
        'avatar',
        'date_of_birth',
        'gender',
        'location',
        'address',
        'city_id',
        'area_id',
        'preferences',
        'status',
        'email_verified_at',
        'phone_verified_at',
        'last_login_at',
        'last_login_ip',
        'last_login_user_agent',
        'registration_ip',
        'registration_user_agent',
        'two_factor_enabled',
        'two_factor_secret',
        'quantum_key_id',
        'security_level',
        'biometric_data',
        'neural_pattern_id',
        'telepathic_network_id',
        'holographic_profile_id',
        'space_computing_access',
        'neuro_ai_profile',
        'carbon_footprint_score',
        'sustainability_level',
        'loyalty_points',
        'vip_level',
        'referral_code',
        'referred_by',
        'language',
        'timezone',
        'notification_preferences',
        'privacy_settings',
        'marketing_consent',
        'data_processing_consent',
        'terms_accepted_at',
        'privacy_policy_accepted_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'quantum_key_id',
        'biometric_data',
        'neural_pattern_id'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'date_of_birth' => 'date',
        'location' => 'array',
        'preferences' => 'array',
        'notification_preferences' => 'array',
        'privacy_settings' => 'array',
        'neuro_ai_profile' => 'array',
        'two_factor_enabled' => 'boolean',
        'space_computing_access' => 'boolean',
        'marketing_consent' => 'boolean',
        'data_processing_consent' => 'boolean',
        'terms_accepted_at' => 'datetime',
        'privacy_policy_accepted_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * User types
     */
    const TYPE_CUSTOMER = 'customer';
    const TYPE_BARBER = 'barber';
    const TYPE_ADMIN = 'admin';

    /**
     * User statuses
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_SUSPENDED = 'suspended';
    const STATUS_PENDING_VERIFICATION = 'pending_verification';

    /**
     * Security levels
     */
    const SECURITY_BASIC = 'basic';
    const SECURITY_ENHANCED = 'enhanced';
    const SECURITY_QUANTUM = 'quantum_enhanced';
    const SECURITY_COSMIC = 'cosmic_level';

    /**
     * VIP levels
     */
    const VIP_BRONZE = 'bronze';
    const VIP_SILVER = 'silver';
    const VIP_GOLD = 'gold';
    const VIP_PLATINUM = 'platinum';
    const VIP_DIAMOND = 'diamond';
    const VIP_COSMIC = 'cosmic';

    /**
     * Activity log options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'name', 'email', 'phone', 'user_type', 'status',
                'last_login_at', 'security_level', 'vip_level'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the user's bookings
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class, 'customer_id');
    }

    /**
     * Get the user's barber profile (if barber)
     */
    public function barberProfile(): HasOne
    {
        return $this->hasOne(BarberProfile::class);
    }

    /**
     * Get the user's reviews as customer
     */
    public function customerReviews(): HasMany
    {
        return $this->hasMany(Review::class, 'customer_id');
    }

    /**
     * Get the user's reviews as barber
     */
    public function barberReviews(): HasMany
    {
        return $this->hasMany(Review::class, 'barber_id');
    }

    /**
     * Get the user's payments
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the user's notifications
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Get the user's favorite barbers
     */
    public function favoriteBarbers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'favorite_barbers', 'customer_id', 'barber_id')
                    ->withTimestamps();
    }

    /**
     * Get the user's loyalty transactions
     */
    public function loyaltyTransactions(): HasMany
    {
        return $this->hasMany(LoyaltyTransaction::class);
    }

    /**
     * Get the user's referrals
     */
    public function referrals(): HasMany
    {
        return $this->hasMany(User::class, 'referred_by');
    }

    /**
     * Get the user who referred this user
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referred_by');
    }

    /**
     * Get the user's city
     */
    public function city()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Get the user's area
     */
    public function area()
    {
        return $this->belongsTo(Area::class);
    }

    /**
     * Get the user's quantum security profile
     */
    public function quantumProfile(): HasOne
    {
        return $this->hasOne(QuantumSecurityProfile::class);
    }

    /**
     * Get the user's neural AI profile
     */
    public function neuralAIProfile(): HasOne
    {
        return $this->hasOne(NeuroAIProfile::class);
    }

    /**
     * Get the user's holographic profile
     */
    public function holographicProfile(): HasOne
    {
        return $this->hasOne(HolographicProfile::class);
    }

    /**
     * Get the user's telepathic profile
     */
    public function telepathicProfile(): HasOne
    {
        return $this->hasOne(TelepathicProfile::class);
    }

    /**
     * Get the user's space computing sessions
     */
    public function spaceComputingSessions(): HasMany
    {
        return $this->hasMany(SpaceComputingSession::class);
    }

    /**
     * Get the user's carbon footprint records
     */
    public function carbonFootprintRecords(): HasMany
    {
        return $this->hasMany(CarbonFootprintRecord::class);
    }

    /**
     * Check if user is a customer
     */
    public function isCustomer(): bool
    {
        return $this->user_type === self::TYPE_CUSTOMER;
    }

    /**
     * Check if user is a barber
     */
    public function isBarber(): bool
    {
        return $this->user_type === self::TYPE_BARBER;
    }

    /**
     * Check if user is an admin
     */
    public function isAdmin(): bool
    {
        return $this->user_type === self::TYPE_ADMIN;
    }

    /**
     * Check if user is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if user has quantum security enabled
     */
    public function hasQuantumSecurity(): bool
    {
        return in_array($this->security_level, [
            self::SECURITY_QUANTUM,
            self::SECURITY_COSMIC
        ]);
    }

    /**
     * Check if user has space computing access
     */
    public function hasSpaceComputingAccess(): bool
    {
        return $this->space_computing_access === true;
    }

    /**
     * Check if user is VIP
     */
    public function isVIP(): bool
    {
        return !is_null($this->vip_level) && $this->vip_level !== self::VIP_BRONZE;
    }

    /**
     * Get user's full name with title
     */
    public function getFullNameAttribute(): string
    {
        $title = $this->isBarber() ? 'الأستاذ' : '';
        return trim($title . ' ' . $this->name);
    }

    /**
     * Get user's avatar URL
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/avatars/' . $this->avatar);
        }
        
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&color=7F9CF5&background=EBF4FF';
    }

    /**
     * Get user's age
     */
    public function getAgeAttribute(): ?int
    {
        return $this->date_of_birth ? $this->date_of_birth->age : null;
    }

    /**
     * Get user's loyalty level
     */
    public function getLoyaltyLevelAttribute(): string
    {
        $points = $this->loyalty_points ?? 0;
        
        if ($points >= 10000) return 'diamond';
        if ($points >= 5000) return 'platinum';
        if ($points >= 2000) return 'gold';
        if ($points >= 500) return 'silver';
        return 'bronze';
    }

    /**
     * Get user's sustainability score
     */
    public function getSustainabilityScoreAttribute(): float
    {
        return $this->carbon_footprint_score ? 
            max(0, 100 - $this->carbon_footprint_score) : 50.0;
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope for customers
     */
    public function scopeCustomers($query)
    {
        return $query->where('user_type', self::TYPE_CUSTOMER);
    }

    /**
     * Scope for barbers
     */
    public function scopeBarbers($query)
    {
        return $query->where('user_type', self::TYPE_BARBER);
    }

    /**
     * Scope for VIP users
     */
    public function scopeVIP($query)
    {
        return $query->whereNotNull('vip_level');
    }

    /**
     * Scope for quantum security users
     */
    public function scopeQuantumSecured($query)
    {
        return $query->whereIn('security_level', [
            self::SECURITY_QUANTUM,
            self::SECURITY_COSMIC
        ]);
    }

    /**
     * Scope for space computing users
     */
    public function scopeSpaceComputingEnabled($query)
    {
        return $query->where('space_computing_access', true);
    }

    /**
     * Generate referral code
     */
    public function generateReferralCode(): string
    {
        $code = 'BARBER' . strtoupper(substr($this->name, 0, 3)) . rand(1000, 9999);
        
        // Ensure uniqueness
        while (self::where('referral_code', $code)->exists()) {
            $code = 'BARBER' . strtoupper(substr($this->name, 0, 3)) . rand(1000, 9999);
        }
        
        $this->update(['referral_code' => $code]);
        return $code;
    }

    /**
     * Add loyalty points
     */
    public function addLoyaltyPoints(int $points, string $reason = 'General'): void
    {
        $this->increment('loyalty_points', $points);
        
        // Log loyalty transaction
        $this->loyaltyTransactions()->create([
            'points' => $points,
            'type' => 'earned',
            'reason' => $reason,
            'balance_after' => $this->fresh()->loyalty_points
        ]);
    }

    /**
     * Deduct loyalty points
     */
    public function deductLoyaltyPoints(int $points, string $reason = 'Redemption'): bool
    {
        if ($this->loyalty_points < $points) {
            return false;
        }
        
        $this->decrement('loyalty_points', $points);
        
        // Log loyalty transaction
        $this->loyaltyTransactions()->create([
            'points' => -$points,
            'type' => 'redeemed',
            'reason' => $reason,
            'balance_after' => $this->fresh()->loyalty_points
        ]);
        
        return true;
    }

    /**
     * Update carbon footprint
     */
    public function updateCarbonFootprint(float $emission, string $activity): void
    {
        $this->increment('carbon_footprint_score', $emission);
        
        // Log carbon footprint record
        $this->carbonFootprintRecords()->create([
            'emission_amount' => $emission,
            'activity_type' => $activity,
            'total_footprint' => $this->fresh()->carbon_footprint_score
        ]);
    }
}
