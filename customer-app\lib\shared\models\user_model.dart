import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  final String id;
  final String name;
  final String phone;
  final String? email;
  final String? avatar;
  final DateTime? emailVerifiedAt;
  final DateTime? phoneVerifiedAt;
  final bool isActive;
  final bool isVip;
  final DateTime? vipExpiresAt;
  final double walletBalance;
  final int loyaltyPoints;
  final double? rating;
  final int totalRatings;
  final String? cityId;
  final String? cityName;
  final String? areaId;
  final String? areaName;
  final List<AddressModel> addresses;
  final UserPreferences preferences;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserModel({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    this.avatar,
    this.emailVerifiedAt,
    this.phoneVerifiedAt,
    this.isActive = true,
    this.isVip = false,
    this.vipExpiresAt,
    this.walletBalance = 0.0,
    this.loyaltyPoints = 0,
    this.rating,
    this.totalRatings = 0,
    this.cityId,
    this.cityName,
    this.areaId,
    this.areaName,
    this.addresses = const [],
    required this.preferences,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  UserModel copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? avatar,
    DateTime? emailVerifiedAt,
    DateTime? phoneVerifiedAt,
    bool? isActive,
    bool? isVip,
    DateTime? vipExpiresAt,
    double? walletBalance,
    int? loyaltyPoints,
    double? rating,
    int? totalRatings,
    String? cityId,
    String? cityName,
    String? areaId,
    String? areaName,
    List<AddressModel>? addresses,
    UserPreferences? preferences,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      phoneVerifiedAt: phoneVerifiedAt ?? this.phoneVerifiedAt,
      isActive: isActive ?? this.isActive,
      isVip: isVip ?? this.isVip,
      vipExpiresAt: vipExpiresAt ?? this.vipExpiresAt,
      walletBalance: walletBalance ?? this.walletBalance,
      loyaltyPoints: loyaltyPoints ?? this.loyaltyPoints,
      rating: rating ?? this.rating,
      totalRatings: totalRatings ?? this.totalRatings,
      cityId: cityId ?? this.cityId,
      cityName: cityName ?? this.cityName,
      areaId: areaId ?? this.areaId,
      areaName: areaName ?? this.areaName,
      addresses: addresses ?? this.addresses,
      preferences: preferences ?? this.preferences,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isEmailVerified => emailVerifiedAt != null;
  bool get isPhoneVerified => phoneVerifiedAt != null;
  bool get isVerified => isPhoneVerified;
  bool get hasAvatar => avatar != null && avatar!.isNotEmpty;
  bool get hasEmail => email != null && email!.isNotEmpty;
  bool get hasDefaultAddress => addresses.any((address) => address.isDefault);
  
  AddressModel? get defaultAddress {
    try {
      return addresses.firstWhere((address) => address.isDefault);
    } catch (e) {
      return addresses.isNotEmpty ? addresses.first : null;
    }
  }

  bool get isVipActive {
    if (!isVip) return false;
    if (vipExpiresAt == null) return true;
    return vipExpiresAt!.isAfter(DateTime.now());
  }

  String get displayName => name;
  String get displayPhone => phone;
  String get displayEmail => email ?? 'غير محدد';
  String get displayLocation => cityName ?? 'غير محدد';

  double get averageRating => rating ?? 0.0;
  String get ratingText => totalRatings > 0 
      ? '${averageRating.toStringAsFixed(1)} (${totalRatings} تقييم)'
      : 'لا توجد تقييمات';
}

@JsonSerializable()
class AddressModel {
  final String id;
  final String title;
  final String address;
  final String? buildingNumber;
  final String? floor;
  final String? apartment;
  final String? landmark;
  final double latitude;
  final double longitude;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;

  AddressModel({
    required this.id,
    required this.title,
    required this.address,
    this.buildingNumber,
    this.floor,
    this.apartment,
    this.landmark,
    required this.latitude,
    required this.longitude,
    this.isDefault = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) =>
      _$AddressModelFromJson(json);

  Map<String, dynamic> toJson() => _$AddressModelToJson(this);

  String get fullAddress {
    List<String> parts = [address];
    
    if (buildingNumber != null) parts.add('مبنى $buildingNumber');
    if (floor != null) parts.add('الطابق $floor');
    if (apartment != null) parts.add('شقة $apartment');
    if (landmark != null) parts.add('بالقرب من $landmark');
    
    return parts.join(', ');
  }
}

@JsonSerializable()
class UserPreferences {
  final String language;
  final String theme;
  final bool notificationsEnabled;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool pushNotifications;
  final bool locationSharing;
  final String preferredPaymentMethod;
  final bool autoBookingConfirmation;
  final int reminderMinutes;

  UserPreferences({
    this.language = 'ar',
    this.theme = 'light',
    this.notificationsEnabled = true,
    this.emailNotifications = true,
    this.smsNotifications = true,
    this.pushNotifications = true,
    this.locationSharing = true,
    this.preferredPaymentMethod = 'cash',
    this.autoBookingConfirmation = false,
    this.reminderMinutes = 30,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) =>
      _$UserPreferencesFromJson(json);

  Map<String, dynamic> toJson() => _$UserPreferencesToJson(this);

  UserPreferences copyWith({
    String? language,
    String? theme,
    bool? notificationsEnabled,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? pushNotifications,
    bool? locationSharing,
    String? preferredPaymentMethod,
    bool? autoBookingConfirmation,
    int? reminderMinutes,
  }) {
    return UserPreferences(
      language: language ?? this.language,
      theme: theme ?? this.theme,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      locationSharing: locationSharing ?? this.locationSharing,
      preferredPaymentMethod: preferredPaymentMethod ?? this.preferredPaymentMethod,
      autoBookingConfirmation: autoBookingConfirmation ?? this.autoBookingConfirmation,
      reminderMinutes: reminderMinutes ?? this.reminderMinutes,
    );
  }
}
