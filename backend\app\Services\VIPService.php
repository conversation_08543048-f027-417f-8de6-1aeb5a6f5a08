<?php

namespace App\Services;

use App\Models\User;
use App\Models\VipPackage;
use App\Models\UserSubscription;
use App\Models\Booking;
use App\Models\VipBenefit;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class VIPService
{
    /**
     * Subscribe user to VIP package
     */
    public function subscribeToVIP(User $user, VipPackage $package, array $paymentData = [])
    {
        DB::beginTransaction();
        
        try {
            // Check if user already has active subscription
            $activeSubscription = $this->getActiveSubscription($user);
            
            if ($activeSubscription) {
                // Upgrade/downgrade existing subscription
                $this->handleSubscriptionChange($activeSubscription, $package);
            } else {
                // Create new subscription
                $this->createNewSubscription($user, $package);
            }

            // Process payment
            $paymentResult = $this->processVIPPayment($user, $package, $paymentData);
            
            if (!$paymentResult['success']) {
                throw new \Exception('Payment failed: ' . $paymentResult['error']);
            }

            // Apply immediate benefits
            $this->applyImmediateBenefits($user, $package);

            // Send welcome notification
            $this->sendVIPWelcomeNotification($user, $package);

            DB::commit();

            return [
                'success' => true,
                'subscription' => $user->activeSubscription,
                'benefits' => $this->getPackageBenefits($package),
                'payment' => $paymentResult
            ];

        } catch (\Exception $e) {
            DB::rollback();
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get active subscription for user
     */
    public function getActiveSubscription(User $user)
    {
        return UserSubscription::where('user_id', $user->id)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->with('vipPackage')
            ->first();
    }

    /**
     * Check if user has VIP access
     */
    public function hasVIPAccess(User $user)
    {
        $subscription = $this->getActiveSubscription($user);
        return $subscription !== null;
    }

    /**
     * Get VIP benefits for user
     */
    public function getUserVIPBenefits(User $user)
    {
        $subscription = $this->getActiveSubscription($user);
        
        if (!$subscription) {
            return [];
        }

        return $this->getPackageBenefits($subscription->vipPackage);
    }

    /**
     * Apply VIP discount to booking
     */
    public function applyVIPDiscount(Booking $booking)
    {
        $user = $booking->customer;
        $subscription = $this->getActiveSubscription($user);
        
        if (!$subscription) {
            return $booking;
        }

        $package = $subscription->vipPackage;
        $discountPercentage = $package->discount_percentage;
        
        if ($discountPercentage > 0) {
            $discountAmount = ($booking->total_amount * $discountPercentage) / 100;
            
            $booking->update([
                'discount_amount' => $discountAmount,
                'total_amount' => $booking->total_amount - $discountAmount,
                'vip_discount_applied' => true
            ]);

            // Log VIP benefit usage
            $this->logBenefitUsage($user, 'discount', [
                'booking_id' => $booking->id,
                'discount_percentage' => $discountPercentage,
                'discount_amount' => $discountAmount
            ]);
        }

        return $booking;
    }

    /**
     * Check priority booking eligibility
     */
    public function hasPriorityBooking(User $user)
    {
        $subscription = $this->getActiveSubscription($user);
        
        if (!$subscription) {
            return false;
        }

        return $subscription->vipPackage->priority_booking;
    }

    /**
     * Get priority booking slots
     */
    public function getPriorityBookingSlots(User $user, $barberId, $date)
    {
        if (!$this->hasPriorityBooking($user)) {
            return [];
        }

        // VIP users get access to slots 24 hours before regular users
        $prioritySlots = DB::table('barber_schedules')
            ->where('barber_id', $barberId)
            ->where('day_of_week', Carbon::parse($date)->dayOfWeek)
            ->where('is_available', true)
            ->get();

        // Filter out already booked slots
        $bookedSlots = Booking::where('barber_id', $barberId)
            ->where('scheduled_date', $date)
            ->pluck('scheduled_time')
            ->toArray();

        return $prioritySlots->filter(function ($slot) use ($bookedSlots) {
            return !in_array($slot->start_time, $bookedSlots);
        });
    }

    /**
     * Check free cancellation eligibility
     */
    public function hasFreeCancellation(User $user)
    {
        $subscription = $this->getActiveSubscription($user);
        
        if (!$subscription) {
            return false;
        }

        return $subscription->vipPackage->free_cancellation;
    }

    /**
     * Cancel booking with VIP benefits
     */
    public function cancelBookingWithVIP(Booking $booking, string $reason = '')
    {
        $user = $booking->customer;
        
        if ($this->hasFreeCancellation($user)) {
            // VIP users can cancel without penalty
            $booking->update([
                'status' => 'cancelled',
                'cancellation_reason' => $reason,
                'cancelled_at' => now(),
                'cancellation_fee' => 0
            ]);

            // Refund full amount
            $this->processVIPRefund($booking);

            // Log VIP benefit usage
            $this->logBenefitUsage($user, 'free_cancellation', [
                'booking_id' => $booking->id,
                'refund_amount' => $booking->total_amount
            ]);

            return [
                'success' => true,
                'refund_amount' => $booking->total_amount,
                'message' => 'تم إلغاء الحجز مجاناً كعضو VIP'
            ];
        }

        return [
            'success' => false,
            'message' => 'لا يمكن الإلغاء المجاني'
        ];
    }

    /**
     * Get VIP customer support priority
     */
    public function getCustomerSupportPriority(User $user)
    {
        $subscription = $this->getActiveSubscription($user);
        
        if (!$subscription) {
            return 'normal';
        }

        $package = $subscription->vipPackage;
        
        // Determine priority based on package features
        $features = $package->features;
        
        if (in_array('خدمة عملاء VIP', $features)) {
            return 'vip';
        } elseif (in_array('خدمة عملاء مميزة', $features)) {
            return 'premium';
        } else {
            return 'high';
        }
    }

    /**
     * Get exclusive services for VIP users
     */
    public function getExclusiveServices(User $user)
    {
        if (!$this->hasVIPAccess($user)) {
            return collect();
        }

        return DB::table('services')
            ->where('is_vip_exclusive', true)
            ->where('is_active', true)
            ->get();
    }

    /**
     * Check instant booking eligibility
     */
    public function hasInstantBooking(User $user)
    {
        $subscription = $this->getActiveSubscription($user);
        
        if (!$subscription) {
            return false;
        }

        $features = $subscription->vipPackage->features;
        return in_array('حجز فوري', $features);
    }

    /**
     * Process instant booking for VIP users
     */
    public function processInstantBooking(User $user, array $bookingData)
    {
        if (!$this->hasInstantBooking($user)) {
            return [
                'success' => false,
                'error' => 'خدمة الحجز الفوري غير متاحة'
            ];
        }

        // Skip normal booking validation and confirmation
        $booking = Booking::create(array_merge($bookingData, [
            'status' => 'confirmed',
            'is_instant_booking' => true,
            'confirmed_at' => now()
        ]));

        // Notify barber immediately
        $notificationService = app(NotificationService::class);
        $notificationService->sendPushNotification(
            $booking->barber->user_id,
            'حجز VIP فوري',
            'لديك حجز VIP فوري جديد - يرجى التأكيد',
            ['booking_id' => $booking->id, 'priority' => 'high']
        );

        // Log VIP benefit usage
        $this->logBenefitUsage($user, 'instant_booking', [
            'booking_id' => $booking->id
        ]);

        return [
            'success' => true,
            'booking' => $booking,
            'message' => 'تم تأكيد حجزك فورياً كعضو VIP'
        ];
    }

    /**
     * Get VIP analytics and usage statistics
     */
    public function getVIPAnalytics(User $user)
    {
        $subscription = $this->getActiveSubscription($user);
        
        if (!$subscription) {
            return null;
        }

        $benefitUsage = VipBenefit::where('user_id', $user->id)
            ->where('created_at', '>=', $subscription->starts_at)
            ->get();

        $totalSavings = $benefitUsage->where('benefit_type', 'discount')
            ->sum('benefit_data.discount_amount');

        $bookingsCount = Booking::where('customer_id', $user->id)
            ->where('created_at', '>=', $subscription->starts_at)
            ->count();

        return [
            'subscription' => $subscription,
            'total_savings' => $totalSavings,
            'bookings_count' => $bookingsCount,
            'benefits_used' => $benefitUsage->groupBy('benefit_type')->map->count(),
            'membership_duration' => $subscription->starts_at->diffInDays(now()),
            'renewal_date' => $subscription->expires_at,
            'auto_renew' => $subscription->auto_renew
        ];
    }

    /**
     * Renew VIP subscription
     */
    public function renewSubscription(UserSubscription $subscription, array $paymentData = [])
    {
        DB::beginTransaction();
        
        try {
            $package = $subscription->vipPackage;
            
            // Process payment
            $paymentResult = $this->processVIPPayment($subscription->user, $package, $paymentData);
            
            if (!$paymentResult['success']) {
                throw new \Exception('Payment failed: ' . $paymentResult['error']);
            }

            // Extend subscription
            $newExpiryDate = $subscription->expires_at->addDays($package->duration_days);
            
            $subscription->update([
                'expires_at' => $newExpiryDate,
                'renewed_at' => now(),
                'auto_renew' => $paymentData['auto_renew'] ?? $subscription->auto_renew
            ]);

            // Send renewal confirmation
            $this->sendRenewalNotification($subscription->user, $subscription);

            DB::commit();

            return [
                'success' => true,
                'subscription' => $subscription,
                'new_expiry' => $newExpiryDate
            ];

        } catch (\Exception $e) {
            DB::rollback();
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Cancel VIP subscription
     */
    public function cancelSubscription(UserSubscription $subscription, string $reason = '')
    {
        $subscription->update([
            'is_active' => false,
            'cancelled_at' => now(),
            'cancellation_reason' => $reason
        ]);

        // Send cancellation confirmation
        $this->sendCancellationNotification($subscription->user, $subscription);

        return [
            'success' => true,
            'message' => 'تم إلغاء اشتراك VIP بنجاح'
        ];
    }

    /**
     * Get available VIP packages
     */
    public function getAvailablePackages()
    {
        return VipPackage::where('is_active', true)
            ->orderBy('price')
            ->get();
    }

    /**
     * Compare VIP packages
     */
    public function comparePackages(array $packageIds)
    {
        $packages = VipPackage::whereIn('id', $packageIds)
            ->where('is_active', true)
            ->get();

        $comparison = [];
        
        foreach ($packages as $package) {
            $comparison[] = [
                'package' => $package,
                'benefits' => $this->getPackageBenefits($package),
                'value_score' => $this->calculateValueScore($package)
            ];
        }

        return $comparison;
    }

    /**
     * Create new subscription
     */
    private function createNewSubscription(User $user, VipPackage $package)
    {
        return UserSubscription::create([
            'user_id' => $user->id,
            'vip_package_id' => $package->id,
            'starts_at' => now(),
            'expires_at' => now()->addDays($package->duration_days),
            'is_active' => true,
            'auto_renew' => false
        ]);
    }

    /**
     * Handle subscription change (upgrade/downgrade)
     */
    private function handleSubscriptionChange(UserSubscription $subscription, VipPackage $newPackage)
    {
        $oldPackage = $subscription->vipPackage;
        
        // Calculate prorated amount
        $remainingDays = $subscription->expires_at->diffInDays(now());
        $proratedAmount = $this->calculateProratedAmount($oldPackage, $newPackage, $remainingDays);

        $subscription->update([
            'vip_package_id' => $newPackage->id,
            'expires_at' => now()->addDays($newPackage->duration_days),
            'upgraded_at' => now(),
            'prorated_amount' => $proratedAmount
        ]);

        return $subscription;
    }

    /**
     * Apply immediate benefits
     */
    private function applyImmediateBenefits(User $user, VipPackage $package)
    {
        // Give welcome bonus or credits if applicable
        $features = $package->features;
        
        if (in_array('رصيد ترحيبي', $features)) {
            $this->addWelcomeCredit($user, $package);
        }
    }

    /**
     * Get package benefits
     */
    private function getPackageBenefits(VipPackage $package)
    {
        return [
            'discount_percentage' => $package->discount_percentage,
            'priority_booking' => $package->priority_booking,
            'free_cancellation' => $package->free_cancellation,
            'features' => $package->features,
            'customer_support_priority' => $this->getPackageSupportPriority($package)
        ];
    }

    /**
     * Process VIP payment
     */
    private function processVIPPayment(User $user, VipPackage $package, array $paymentData)
    {
        $paymentService = app(PaymentService::class);
        
        return $paymentService->processPayment([
            'amount' => $package->price,
            'currency' => 'EGP',
            'description' => 'VIP Package: ' . $package->name,
            'customer_id' => $user->id,
            'payment_method' => $paymentData['payment_method'] ?? 'card',
            'metadata' => [
                'type' => 'vip_subscription',
                'package_id' => $package->id
            ]
        ]);
    }

    /**
     * Process VIP refund
     */
    private function processVIPRefund(Booking $booking)
    {
        $paymentService = app(PaymentService::class);
        
        return $paymentService->refundPayment($booking->payment->transaction_id, $booking->total_amount);
    }

    /**
     * Log VIP benefit usage
     */
    private function logBenefitUsage(User $user, string $benefitType, array $data)
    {
        VipBenefit::create([
            'user_id' => $user->id,
            'benefit_type' => $benefitType,
            'benefit_data' => $data,
            'used_at' => now()
        ]);
    }

    /**
     * Send VIP welcome notification
     */
    private function sendVIPWelcomeNotification(User $user, VipPackage $package)
    {
        $notificationService = app(NotificationService::class);
        
        $notificationService->sendPushNotification(
            $user->id,
            'مرحباً بك في VIP!',
            "تم تفعيل باقة {$package->name} بنجاح. استمتع بالمزايا الحصرية!",
            ['type' => 'vip_welcome', 'package_id' => $package->id]
        );
    }

    /**
     * Send renewal notification
     */
    private function sendRenewalNotification(User $user, UserSubscription $subscription)
    {
        $notificationService = app(NotificationService::class);
        
        $notificationService->sendPushNotification(
            $user->id,
            'تم تجديد اشتراك VIP',
            "تم تجديد اشتراكك حتى {$subscription->expires_at->format('Y-m-d')}",
            ['type' => 'vip_renewal', 'subscription_id' => $subscription->id]
        );
    }

    /**
     * Send cancellation notification
     */
    private function sendCancellationNotification(User $user, UserSubscription $subscription)
    {
        $notificationService = app(NotificationService::class);
        
        $notificationService->sendPushNotification(
            $user->id,
            'تم إلغاء اشتراك VIP',
            'تم إلغاء اشتراكك في VIP. يمكنك الاشتراك مرة أخرى في أي وقت.',
            ['type' => 'vip_cancellation']
        );
    }

    /**
     * Calculate value score for package comparison
     */
    private function calculateValueScore(VipPackage $package)
    {
        $score = 0;
        
        // Base score from discount
        $score += $package->discount_percentage * 2;
        
        // Additional features score
        $features = $package->features;
        $score += count($features) * 5;
        
        if ($package->priority_booking) $score += 10;
        if ($package->free_cancellation) $score += 10;
        
        // Price efficiency (lower price = higher score)
        $score += max(0, 100 - $package->price / 10);
        
        return round($score, 2);
    }

    /**
     * Calculate prorated amount for subscription changes
     */
    private function calculateProratedAmount(VipPackage $oldPackage, VipPackage $newPackage, int $remainingDays)
    {
        $oldDailyRate = $oldPackage->price / $oldPackage->duration_days;
        $newDailyRate = $newPackage->price / $newPackage->duration_days;
        
        $refund = $oldDailyRate * $remainingDays;
        $newCharge = $newDailyRate * $newPackage->duration_days;
        
        return $newCharge - $refund;
    }

    /**
     * Add welcome credit to user
     */
    private function addWelcomeCredit(User $user, VipPackage $package)
    {
        $creditAmount = $package->price * 0.1; // 10% welcome credit
        
        $walletService = app(WalletService::class);
        $walletService->addCredit($user, $creditAmount, 'VIP Welcome Bonus');
    }

    /**
     * Get package support priority
     */
    private function getPackageSupportPriority(VipPackage $package)
    {
        $features = $package->features;
        
        if (in_array('خدمة عملاء VIP', $features)) {
            return 'vip';
        } elseif (in_array('خدمة عملاء مميزة', $features)) {
            return 'premium';
        } else {
            return 'high';
        }
    }
}
