<template>
  <div>
    <!-- Page Header -->
    <v-row class="mb-6">
      <v-col cols="12">
        <div class="d-flex justify-space-between align-center">
          <div>
            <h1 class="text-h4 font-weight-bold mb-2">الإعدادات الديناميكية</h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              تحكم في جميع مزايا وإعدادات النظام بشكل ديناميكي
            </p>
          </div>
          <div class="d-flex gap-2">
            <v-btn
              color="primary"
              variant="outlined"
              prepend-icon="mdi-cached"
              @click="clearCache"
              :loading="settingsStore.loading"
            >
              مسح الذاكرة المؤقتة
            </v-btn>
            <v-btn
              color="success"
              variant="outlined"
              prepend-icon="mdi-download"
              @click="exportSettings"
              :loading="settingsStore.loading"
            >
              تصدير الإعدادات
            </v-btn>
            <v-btn
              color="primary"
              prepend-icon="mdi-content-save"
              @click="saveSettings"
              :loading="settingsStore.loading"
              :disabled="!hasChanges"
            >
              حفظ التغييرات
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- Quick Feature Toggles -->
    <v-row class="mb-6">
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon class="me-2">mdi-toggle-switch</v-icon>
            التحكم السريع في المزايا
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col
                v-for="feature in quickFeatures"
                :key="feature.key"
                cols="12"
                sm="6"
                md="4"
                lg="3"
              >
                <v-card
                  variant="outlined"
                  class="pa-3"
                  :class="{ 'border-success': isFeatureEnabled(feature.key) }"
                >
                  <div class="d-flex align-center justify-space-between">
                    <div class="d-flex align-center">
                      <v-icon :color="isFeatureEnabled(feature.key) ? 'success' : 'grey'" class="me-2">
                        {{ feature.icon }}
                      </v-icon>
                      <div>
                        <div class="font-weight-medium">{{ feature.name }}</div>
                        <div class="text-caption text-medium-emphasis">{{ feature.description }}</div>
                      </div>
                    </div>
                    <v-switch
                      :model-value="isFeatureEnabled(feature.key)"
                      @update:model-value="toggleFeature(feature.key, $event)"
                      color="success"
                      hide-details
                      density="compact"
                    ></v-switch>
                  </div>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Settings Categories -->
    <v-row>
      <v-col cols="12" md="3">
        <!-- Category Navigation -->
        <v-card>
          <v-card-title>فئات الإعدادات</v-card-title>
          <v-list density="compact" nav>
            <v-list-item
              v-for="category in settingsStore.categories"
              :key="category.key"
              :value="category.key"
              :active="selectedCategory === category.key"
              @click="selectedCategory = category.key"
              :prepend-icon="category.icon"
              :title="category.name"
            >
              <template v-slot:append>
                <v-chip
                  size="small"
                  variant="outlined"
                  :text="getCategorySettingsCount(category.key)"
                ></v-chip>
              </template>
            </v-list-item>
          </v-list>
        </v-card>
      </v-col>

      <v-col cols="12" md="9">
        <!-- Settings Form -->
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon class="me-2">{{ getCurrentCategoryIcon() }}</v-icon>
            {{ getCurrentCategoryName() }}
          </v-card-title>
          
          <v-card-text>
            <v-form ref="settingsForm">
              <v-row>
                <v-col
                  v-for="(setting, key) in getCurrentCategorySettings()"
                  :key="key"
                  cols="12"
                  :md="setting.type === 'boolean' ? 6 : 12"
                >
                  <!-- Boolean Settings -->
                  <v-switch
                    v-if="setting.type === 'boolean'"
                    v-model="localSettings[key]"
                    :label="setting.description || key"
                    color="success"
                    hide-details="auto"
                    @update:model-value="markAsChanged(key)"
                  ></v-switch>

                  <!-- String Settings -->
                  <v-text-field
                    v-else-if="setting.type === 'string'"
                    v-model="localSettings[key]"
                    :label="setting.description || key"
                    variant="outlined"
                    hide-details="auto"
                    @update:model-value="markAsChanged(key)"
                  ></v-text-field>

                  <!-- Number Settings -->
                  <v-text-field
                    v-else-if="setting.type === 'integer' || setting.type === 'float'"
                    v-model.number="localSettings[key]"
                    :label="setting.description || key"
                    type="number"
                    :step="setting.type === 'float' ? '0.01' : '1'"
                    variant="outlined"
                    hide-details="auto"
                    @update:model-value="markAsChanged(key)"
                  ></v-text-field>

                  <!-- JSON Settings -->
                  <v-textarea
                    v-else-if="setting.type === 'json'"
                    v-model="localSettings[key]"
                    :label="setting.description || key"
                    variant="outlined"
                    rows="4"
                    hide-details="auto"
                    @update:model-value="markAsChanged(key)"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Loading Overlay -->
    <v-overlay
      v-model="settingsStore.loading"
      class="align-center justify-center"
      contained
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      ></v-progress-circular>
    </v-overlay>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useSettingsStore } from '@/stores/settings'
import { useSnackbar } from '@/composables/useSnackbar'

export default {
  name: 'DynamicSettings',
  setup() {
    const settingsStore = useSettingsStore()
    const { showSnackbar } = useSnackbar()

    const selectedCategory = ref('general')
    const localSettings = ref({})
    const changedSettings = ref(new Set())

    const quickFeatures = ref([
      {
        key: 'registration',
        name: 'التسجيل الجديد',
        description: 'السماح بتسجيل مستخدمين جدد',
        icon: 'mdi-account-plus'
      },
      {
        key: 'booking',
        name: 'الحجز العادي',
        description: 'تفعيل خدمة الحجز العادية',
        icon: 'mdi-calendar-check'
      },
      {
        key: 'vip',
        name: 'خدمة VIP',
        description: 'تفعيل الخدمات المميزة',
        icon: 'mdi-crown'
      },
      {
        key: 'online_payment',
        name: 'الدفع الإلكتروني',
        description: 'تفعيل بوابات الدفع الإلكتروني',
        icon: 'mdi-credit-card'
      },
      {
        key: 'store',
        name: 'المتجر',
        description: 'تفعيل متجر المنتجات',
        icon: 'mdi-store'
      },
      {
        key: 'loyalty_points',
        name: 'نظام النقاط',
        description: 'تفعيل نظام الولاء والنقاط',
        icon: 'mdi-star'
      }
    ])

    const hasChanges = computed(() => changedSettings.value.size > 0)

    const getCurrentCategorySettings = () => {
      return settingsStore.getSettingsByCategory(selectedCategory.value)
    }

    const getCurrentCategoryName = () => {
      const category = settingsStore.getCategoryInfo(selectedCategory.value)
      return category?.name || 'إعدادات'
    }

    const getCurrentCategoryIcon = () => {
      const category = settingsStore.getCategoryInfo(selectedCategory.value)
      return category?.icon || 'mdi-cog'
    }

    const getCategorySettingsCount = (categoryKey) => {
      const settings = settingsStore.getSettingsByCategory(categoryKey)
      return Object.keys(settings).length
    }

    const isFeatureEnabled = (feature) => {
      return settingsStore.isFeatureEnabled(feature)
    }

    const markAsChanged = (key) => {
      changedSettings.value.add(key)
    }

    const toggleFeature = async (feature, enabled) => {
      const result = await settingsStore.toggleFeature(feature, enabled)
      if (result.success) {
        showSnackbar(result.message, 'success')
      } else {
        showSnackbar(result.message, 'error')
      }
    }

    const saveSettings = async () => {
      if (!hasChanges.value) return

      const settingsToUpdate = {}
      changedSettings.value.forEach(key => {
        settingsToUpdate[key] = localSettings.value[key]
      })

      const result = await settingsStore.updateSettings(settingsToUpdate)
      if (result.success) {
        showSnackbar(result.message, 'success')
        changedSettings.value.clear()
      } else {
        showSnackbar(result.message, 'error')
      }
    }

    const clearCache = async () => {
      const result = await settingsStore.clearCache()
      if (result.success) {
        showSnackbar(result.message, 'success')
      } else {
        showSnackbar(result.message, 'error')
      }
    }

    const exportSettings = async () => {
      const result = await settingsStore.exportSettings()
      if (result.success) {
        showSnackbar(result.message, 'success')
      } else {
        showSnackbar(result.message, 'error')
      }
    }

    const initializeLocalSettings = () => {
      Object.entries(settingsStore.settings).forEach(([key, setting]) => {
        localSettings.value[key] = setting.value
      })
    }

    // Watch for settings changes
    watch(() => settingsStore.settings, () => {
      initializeLocalSettings()
    }, { deep: true })

    onMounted(async () => {
      await settingsStore.fetchAllSettings()
      initializeLocalSettings()
    })

    return {
      settingsStore,
      selectedCategory,
      localSettings,
      changedSettings,
      quickFeatures,
      hasChanges,
      getCurrentCategorySettings,
      getCurrentCategoryName,
      getCurrentCategoryIcon,
      getCategorySettingsCount,
      isFeatureEnabled,
      markAsChanged,
      toggleFeature,
      saveSettings,
      clearCache,
      exportSettings
    }
  }
}
</script>

<style scoped>
.border-success {
  border-color: rgb(var(--v-theme-success)) !important;
  border-width: 2px !important;
}
</style>
