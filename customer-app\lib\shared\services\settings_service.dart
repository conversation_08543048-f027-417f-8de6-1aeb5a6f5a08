import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../../core/constants/app_constants.dart';
import '../../core/network/api_client.dart';
import '../../core/network/api_endpoints.dart';
import 'storage_service.dart';
import 'cache_service.dart';

abstract class SettingsService {
  Future<void> initialize();
  Future<Map<String, dynamic>> getPublicSettings();
  Future<bool> isMaintenanceMode();
  Future<bool> isFeatureEnabled(String feature);
  Future<T?> getSetting<T>(String key, {T? defaultValue});
  Future<void> refreshSettings();
  Future<Map<String, dynamic>> getAppConfig();
  Future<Map<String, dynamic>> getBookingConfig();
  Future<Map<String, dynamic>> getVipConfig();
  Future<Map<String, dynamic>> getPaymentConfig();
}

class SettingsServiceImpl implements SettingsService {
  final ApiClient _apiClient;
  final StorageService _storageService;
  final CacheService _cacheService;
  
  Map<String, dynamic> _settings = {};
  bool _isInitialized = false;

  SettingsServiceImpl({
    required ApiClient apiClient,
    required StorageService storageService,
    required CacheService cacheService,
  }) : _apiClient = apiClient,
       _storageService = storageService,
       _cacheService = cacheService;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    // Try to load from cache first
    final cachedSettings = await _cacheService.get('app_settings');
    if (cachedSettings != null) {
      _settings = Map<String, dynamic>.from(cachedSettings);
    }
    
    // Load from storage
    final storedSettings = await _storageService.getString(AppConstants.settingsKey);
    if (storedSettings != null) {
      try {
        _settings = Map<String, dynamic>.from(storedSettings);
      } catch (e) {
        // Ignore parsing errors
      }
    }
    
    // Refresh from server
    await refreshSettings();
    
    _isInitialized = true;
  }

  @override
  Future<Map<String, dynamic>> getPublicSettings() async {
    try {
      final response = await _apiClient.get(ApiEndpoints.publicSettings);
      
      if (response.statusCode == 200) {
        final settings = response.data['data'] as Map<String, dynamic>;
        
        _settings.addAll(settings);
        
        // Cache the settings
        await _cacheService.set('app_settings', _settings, 
            duration: AppConstants.cacheExpiration);
        await _storageService.setString(AppConstants.settingsKey, _settings);
        
        return settings;
      }
      
      return _settings;
    } catch (e) {
      return _settings;
    }
  }

  @override
  Future<bool> isMaintenanceMode() async {
    try {
      final response = await _apiClient.get(ApiEndpoints.maintenanceCheck);
      
      if (response.statusCode == 200) {
        return response.data['data']['maintenance_mode'] ?? false;
      }
      
      return getSetting<bool>('maintenance_mode', defaultValue: false) ?? false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> isFeatureEnabled(String feature) async {
    final featureKey = 'feature_${feature}_enabled';
    return getSetting<bool>(featureKey, defaultValue: true) ?? true;
  }

  @override
  Future<T?> getSetting<T>(String key, {T? defaultValue}) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    final value = _settings[key];
    
    if (value == null) return defaultValue;
    
    if (value is T) return value;
    
    // Try to convert the value
    try {
      if (T == bool) {
        if (value is String) {
          return (value.toLowerCase() == 'true' || value == '1') as T;
        }
        if (value is int) {
          return (value == 1) as T;
        }
      }
      
      if (T == int) {
        if (value is String) {
          return int.parse(value) as T;
        }
        if (value is double) {
          return value.toInt() as T;
        }
      }
      
      if (T == double) {
        if (value is String) {
          return double.parse(value) as T;
        }
        if (value is int) {
          return value.toDouble() as T;
        }
      }
      
      if (T == String) {
        return value.toString() as T;
      }
      
      return value as T;
    } catch (e) {
      return defaultValue;
    }
  }

  @override
  Future<void> refreshSettings() async {
    try {
      await getPublicSettings();
    } catch (e) {
      // Ignore refresh errors
    }
  }

  @override
  Future<Map<String, dynamic>> getAppConfig() async {
    try {
      final response = await _apiClient.get(ApiEndpoints.appConfig);
      
      if (response.statusCode == 200) {
        final config = response.data['data'] as Map<String, dynamic>;
        
        // Cache the config
        await _cacheService.set('app_config', config, 
            duration: AppConstants.shortCacheExpiration);
        
        return config;
      }
      
      return await _cacheService.get('app_config') ?? {};
    } catch (e) {
      return await _cacheService.get('app_config') ?? {};
    }
  }

  @override
  Future<Map<String, dynamic>> getBookingConfig() async {
    try {
      final response = await _apiClient.get(ApiEndpoints.bookingConfig);
      
      if (response.statusCode == 200) {
        final config = response.data['data'] as Map<String, dynamic>;
        
        // Cache the config
        await _cacheService.set('booking_config', config, 
            duration: AppConstants.cacheExpiration);
        
        return config;
      }
      
      return await _cacheService.get('booking_config') ?? {};
    } catch (e) {
      return await _cacheService.get('booking_config') ?? {};
    }
  }

  @override
  Future<Map<String, dynamic>> getVipConfig() async {
    try {
      final response = await _apiClient.get(ApiEndpoints.vipConfig);
      
      if (response.statusCode == 200) {
        final config = response.data['data'] as Map<String, dynamic>;
        
        // Cache the config
        await _cacheService.set('vip_config', config, 
            duration: AppConstants.cacheExpiration);
        
        return config;
      }
      
      return await _cacheService.get('vip_config') ?? {};
    } catch (e) {
      return await _cacheService.get('vip_config') ?? {};
    }
  }

  @override
  Future<Map<String, dynamic>> getPaymentConfig() async {
    try {
      final response = await _apiClient.get(ApiEndpoints.paymentConfig);
      
      if (response.statusCode == 200) {
        final config = response.data['data'] as Map<String, dynamic>;
        
        // Cache the config
        await _cacheService.set('payment_config', config, 
            duration: AppConstants.cacheExpiration);
        
        return config;
      }
      
      return await _cacheService.get('payment_config') ?? {};
    } catch (e) {
      return await _cacheService.get('payment_config') ?? {};
    }
  }

  // Helper methods for common settings
  Future<bool> get registrationEnabled => 
      getSetting<bool>('registration_enabled', defaultValue: true);
  
  Future<bool> get bookingEnabled => 
      getSetting<bool>('booking_enabled', defaultValue: true);
  
  Future<bool> get vipEnabled => 
      getSetting<bool>('vip_enabled', defaultValue: true);
  
  Future<bool> get onlinePaymentEnabled => 
      getSetting<bool>('online_payment_enabled', defaultValue: true);
  
  Future<bool> get storeEnabled => 
      getSetting<bool>('store_enabled', defaultValue: true);
  
  Future<bool> get loyaltyPointsEnabled => 
      getSetting<bool>('loyalty_points_enabled', defaultValue: true);
  
  Future<bool> get mobileBarberEnabled => 
      getSetting<bool>('mobile_barber_enabled', defaultValue: true);
  
  Future<bool> get liveStreamEnabled => 
      getSetting<bool>('live_stream_enabled', defaultValue: false);
  
  Future<bool> get aiRecommendationEnabled => 
      getSetting<bool>('ai_recommendation_enabled', defaultValue: false);
  
  Future<double> get deliveryFee => 
      getSetting<double>('delivery_fee', defaultValue: 25.0);
  
  Future<double> get freeDeliveryThreshold => 
      getSetting<double>('free_delivery_threshold', defaultValue: 200.0);
  
  Future<double> get vipMonthlyPrice => 
      getSetting<double>('vip_monthly_price', defaultValue: 299.0);
  
  Future<double> get vipYearlyPrice => 
      getSetting<double>('vip_yearly_price', defaultValue: 2999.0);
  
  Future<double> get vipDiscountPercentage => 
      getSetting<double>('vip_discount_percentage', defaultValue: 20.0);
  
  Future<int> get maxAdvanceBookingDays => 
      getSetting<int>('max_advance_booking_days', defaultValue: 30);
  
  Future<int> get minAdvanceBookingHours => 
      getSetting<int>('min_advance_booking_hours', defaultValue: 2);
  
  Future<String> get supportPhone => 
      getSetting<String>('support_phone', defaultValue: '+201234567890');
  
  Future<String> get supportEmail => 
      getSetting<String>('support_email', defaultValue: '<EMAIL>');
}
