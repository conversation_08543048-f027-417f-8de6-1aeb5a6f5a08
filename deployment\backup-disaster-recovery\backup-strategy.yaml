# Comprehensive Backup and Disaster Recovery Strategy
# for Barber App Production Environment

apiVersion: v1
kind: Namespace
metadata:
  name: backup-system
  labels:
    name: backup-system
---
# Velero Backup Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: velero-config
  namespace: backup-system
data:
  backup-schedule.yaml: |
    # Daily full backup
    apiVersion: velero.io/v1
    kind: Schedule
    metadata:
      name: daily-full-backup
      namespace: backup-system
    spec:
      schedule: "0 2 * * *"  # 2 AM daily
      template:
        includedNamespaces:
          - barber-app
          - monitoring
          - ingress-nginx
        excludedResources:
          - events
          - events.events.k8s.io
        storageLocation: default
        volumeSnapshotLocations:
          - default
        ttl: 720h  # 30 days retention

    ---
    # Hourly incremental backup
    apiVersion: velero.io/v1
    kind: Schedule
    metadata:
      name: hourly-incremental-backup
      namespace: backup-system
    spec:
      schedule: "0 * * * *"  # Every hour
      template:
        includedNamespaces:
          - barber-app
        includedResources:
          - persistentvolumes
          - persistentvolumeclaims
        storageLocation: default
        ttl: 168h  # 7 days retention

    ---
    # Weekly archive backup
    apiVersion: velero.io/v1
    kind: Schedule
    metadata:
      name: weekly-archive-backup
      namespace: backup-system
    spec:
      schedule: "0 1 * * 0"  # 1 AM every Sunday
      template:
        includedNamespaces:
          - barber-app
          - monitoring
          - ingress-nginx
          - backup-system
        storageLocation: archive
        ttl: 8760h  # 1 year retention

---
# Database Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: mysql-backup
  namespace: backup-system
spec:
  schedule: "0 3 * * *"  # 3 AM daily
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
            - name: mysql-backup
              image: mysql:8.0
              env:
                - name: MYSQL_HOST
                  value: "mysql.barber-app.svc.cluster.local"
                - name: MYSQL_USER
                  value: "backup_user"
                - name: MYSQL_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: mysql-backup-secret
                      key: password
                - name: BACKUP_RETENTION_DAYS
                  value: "30"
                - name: S3_BUCKET
                  value: "barber-app-backups"
                - name: AWS_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: aws-backup-credentials
                      key: access-key-id
                - name: AWS_SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: aws-backup-credentials
                      key: secret-access-key
              command:
                - /bin/bash
                - -c
                - |
                  set -e
                  
                  # Create backup directory
                  BACKUP_DIR="/tmp/mysql-backup"
                  mkdir -p $BACKUP_DIR
                  
                  # Generate backup filename with timestamp
                  TIMESTAMP=$(date +%Y%m%d_%H%M%S)
                  BACKUP_FILE="barber_app_backup_${TIMESTAMP}.sql"
                  
                  echo "Starting MySQL backup at $(date)"
                  
                  # Create database backup
                  mysqldump -h $MYSQL_HOST -u $MYSQL_USER -p$MYSQL_PASSWORD \
                    --single-transaction \
                    --routines \
                    --triggers \
                    --events \
                    --all-databases \
                    --add-drop-database \
                    --add-drop-table \
                    --add-locks \
                    --create-options \
                    --disable-keys \
                    --extended-insert \
                    --lock-tables=false \
                    --quick \
                    --set-charset \
                    > $BACKUP_DIR/$BACKUP_FILE
                  
                  # Compress backup
                  gzip $BACKUP_DIR/$BACKUP_FILE
                  COMPRESSED_FILE="${BACKUP_FILE}.gz"
                  
                  # Install AWS CLI
                  apt-get update && apt-get install -y awscli
                  
                  # Upload to S3
                  aws s3 cp $BACKUP_DIR/$COMPRESSED_FILE s3://$S3_BUCKET/mysql/daily/$COMPRESSED_FILE
                  
                  # Create checksum
                  md5sum $BACKUP_DIR/$COMPRESSED_FILE > $BACKUP_DIR/${COMPRESSED_FILE}.md5
                  aws s3 cp $BACKUP_DIR/${COMPRESSED_FILE}.md5 s3://$S3_BUCKET/mysql/daily/${COMPRESSED_FILE}.md5
                  
                  # Cleanup old backups
                  CUTOFF_DATE=$(date -d "$BACKUP_RETENTION_DAYS days ago" +%Y%m%d)
                  aws s3 ls s3://$S3_BUCKET/mysql/daily/ | while read -r line; do
                    FILE_DATE=$(echo $line | awk '{print $4}' | grep -o '[0-9]\{8\}' | head -1)
                    if [[ $FILE_DATE < $CUTOFF_DATE ]]; then
                      FILE_NAME=$(echo $line | awk '{print $4}')
                      echo "Deleting old backup: $FILE_NAME"
                      aws s3 rm s3://$S3_BUCKET/mysql/daily/$FILE_NAME
                    fi
                  done
                  
                  echo "MySQL backup completed successfully at $(date)"
              resources:
                requests:
                  cpu: 100m
                  memory: 256Mi
                limits:
                  cpu: 500m
                  memory: 1Gi
              volumeMounts:
                - name: backup-storage
                  mountPath: /tmp/mysql-backup
          volumes:
            - name: backup-storage
              emptyDir:
                sizeLimit: 10Gi

---
# Redis Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: redis-backup
  namespace: backup-system
spec:
  schedule: "0 4 * * *"  # 4 AM daily
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
            - name: redis-backup
              image: redis:7.0
              env:
                - name: REDIS_HOST
                  value: "redis.barber-app.svc.cluster.local"
                - name: REDIS_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: redis-backup-secret
                      key: password
                - name: S3_BUCKET
                  value: "barber-app-backups"
                - name: AWS_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: aws-backup-credentials
                      key: access-key-id
                - name: AWS_SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: aws-backup-credentials
                      key: secret-access-key
              command:
                - /bin/bash
                - -c
                - |
                  set -e
                  
                  # Create backup directory
                  BACKUP_DIR="/tmp/redis-backup"
                  mkdir -p $BACKUP_DIR
                  
                  # Generate backup filename with timestamp
                  TIMESTAMP=$(date +%Y%m%d_%H%M%S)
                  BACKUP_FILE="redis_backup_${TIMESTAMP}.rdb"
                  
                  echo "Starting Redis backup at $(date)"
                  
                  # Create Redis backup using BGSAVE
                  redis-cli -h $REDIS_HOST -a $REDIS_PASSWORD BGSAVE
                  
                  # Wait for backup to complete
                  while [ $(redis-cli -h $REDIS_HOST -a $REDIS_PASSWORD LASTSAVE) -eq $(redis-cli -h $REDIS_HOST -a $REDIS_PASSWORD LASTSAVE) ]; do
                    sleep 1
                  done
                  
                  # Copy RDB file
                  redis-cli -h $REDIS_HOST -a $REDIS_PASSWORD --rdb $BACKUP_DIR/$BACKUP_FILE
                  
                  # Compress backup
                  gzip $BACKUP_DIR/$BACKUP_FILE
                  COMPRESSED_FILE="${BACKUP_FILE}.gz"
                  
                  # Install AWS CLI
                  apt-get update && apt-get install -y awscli
                  
                  # Upload to S3
                  aws s3 cp $BACKUP_DIR/$COMPRESSED_FILE s3://$S3_BUCKET/redis/daily/$COMPRESSED_FILE
                  
                  echo "Redis backup completed successfully at $(date)"
              resources:
                requests:
                  cpu: 100m
                  memory: 256Mi
                limits:
                  cpu: 500m
                  memory: 1Gi

---
# File Storage Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: file-storage-backup
  namespace: backup-system
spec:
  schedule: "0 5 * * *"  # 5 AM daily
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
            - name: file-backup
              image: amazon/aws-cli:latest
              env:
                - name: SOURCE_BUCKET
                  value: "barber-app-storage"
                - name: BACKUP_BUCKET
                  value: "barber-app-backups"
                - name: AWS_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: aws-backup-credentials
                      key: access-key-id
                - name: AWS_SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: aws-backup-credentials
                      key: secret-access-key
              command:
                - /bin/bash
                - -c
                - |
                  set -e
                  
                  TIMESTAMP=$(date +%Y%m%d_%H%M%S)
                  BACKUP_PREFIX="files/daily/$TIMESTAMP"
                  
                  echo "Starting file storage backup at $(date)"
                  
                  # Sync files to backup bucket
                  aws s3 sync s3://$SOURCE_BUCKET s3://$BACKUP_BUCKET/$BACKUP_PREFIX \
                    --delete \
                    --storage-class STANDARD_IA
                  
                  # Create manifest file
                  aws s3 ls s3://$BACKUP_BUCKET/$BACKUP_PREFIX --recursive > /tmp/manifest.txt
                  aws s3 cp /tmp/manifest.txt s3://$BACKUP_BUCKET/$BACKUP_PREFIX/manifest.txt
                  
                  echo "File storage backup completed successfully at $(date)"
              resources:
                requests:
                  cpu: 100m
                  memory: 256Mi
                limits:
                  cpu: 500m
                  memory: 1Gi

---
# Backup Monitoring and Alerting
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-monitoring-config
  namespace: backup-system
data:
  backup-alerts.yml: |
    groups:
      - name: backup-alerts
        rules:
          - alert: BackupJobFailed
            expr: kube_job_status_failed{namespace="backup-system"} > 0
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "Backup job failed"
              description: "Backup job {{ $labels.job_name }} in namespace {{ $labels.namespace }} has failed"

          - alert: BackupJobTooLong
            expr: time() - kube_job_status_start_time{namespace="backup-system"} > 3600
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "Backup job running too long"
              description: "Backup job {{ $labels.job_name }} has been running for more than 1 hour"

          - alert: NoRecentBackup
            expr: time() - backup_last_success_timestamp > 86400
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "No recent backup found"
              description: "No successful backup found in the last 24 hours for {{ $labels.backup_type }}"

---
# Disaster Recovery Plan ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: disaster-recovery-plan
  namespace: backup-system
data:
  recovery-procedures.md: |
    # Disaster Recovery Procedures for Barber App

    ## Recovery Time Objectives (RTO) and Recovery Point Objectives (RPO)
    - **RTO**: 4 hours maximum downtime
    - **RPO**: 1 hour maximum data loss
    - **Critical Services**: API, Database, Payment Processing
    - **Non-Critical Services**: Analytics, Reporting

    ## Recovery Scenarios

    ### 1. Database Recovery
    ```bash
    # Download latest backup
    aws s3 cp s3://barber-app-backups/mysql/daily/latest.sql.gz /tmp/

    # Restore database
    gunzip /tmp/latest.sql.gz
    mysql -h $MYSQL_HOST -u root -p < /tmp/latest.sql
    ```

    ### 2. Complete Cluster Recovery
    ```bash
    # Restore from Velero backup
    velero restore create --from-backup daily-full-backup-20240117

    # Verify restoration
    kubectl get pods -n barber-app
    kubectl get pvc -n barber-app
    ```

    ### 3. Application Recovery
    ```bash
    # Redeploy application
    kubectl apply -f deployment/kubernetes/production/

    # Restore database
    kubectl exec -it mysql-pod -- mysql -u root -p < backup.sql

    # Verify services
    kubectl get svc -n barber-app
    curl https://api.barber-app.com/health
    ```

    ## Emergency Contacts
    - **Primary**: <EMAIL>
    - **Secondary**: <EMAIL>
    - **Cloud Provider**: AWS Support (Enterprise)

  recovery-scripts.sh: |
    #!/bin/bash
    # Automated Recovery Scripts

    # Function to restore MySQL database
    restore_mysql() {
        local backup_file=$1
        echo "Restoring MySQL from $backup_file"
        
        # Download backup
        aws s3 cp s3://barber-app-backups/mysql/daily/$backup_file /tmp/
        
        # Decompress
        gunzip /tmp/$backup_file
        
        # Restore
        mysql -h $MYSQL_HOST -u root -p$MYSQL_ROOT_PASSWORD < /tmp/${backup_file%.gz}
        
        echo "MySQL restoration completed"
    }

    # Function to restore Redis
    restore_redis() {
        local backup_file=$1
        echo "Restoring Redis from $backup_file"
        
        # Download backup
        aws s3 cp s3://barber-app-backups/redis/daily/$backup_file /tmp/
        
        # Decompress
        gunzip /tmp/$backup_file
        
        # Stop Redis
        kubectl scale deployment redis --replicas=0 -n barber-app
        
        # Copy RDB file
        kubectl cp /tmp/${backup_file%.gz} redis-pod:/data/dump.rdb
        
        # Start Redis
        kubectl scale deployment redis --replicas=1 -n barber-app
        
        echo "Redis restoration completed"
    }

    # Function to verify system health
    verify_system_health() {
        echo "Verifying system health..."
        
        # Check pods
        kubectl get pods -n barber-app
        
        # Check services
        kubectl get svc -n barber-app
        
        # Health check endpoints
        curl -f https://api.barber-app.com/health
        curl -f https://admin.barber-app.com/health
        
        echo "System health verification completed"
    }

---
# Backup Secrets
apiVersion: v1
kind: Secret
metadata:
  name: mysql-backup-secret
  namespace: backup-system
type: Opaque
data:
  password: YmFja3VwLXBhc3N3b3JkLTIwMjQ= # backup-password-2024

---
apiVersion: v1
kind: Secret
metadata:
  name: redis-backup-secret
  namespace: backup-system
type: Opaque
data:
  password: cmVkaXMtYmFja3VwLXBhc3N3b3JkLTIwMjQ= # redis-backup-password-2024

---
apiVersion: v1
kind: Secret
metadata:
  name: aws-backup-credentials
  namespace: backup-system
type: Opaque
data:
  access-key-id: QUtJQUlPU0ZPRE5ON0VYQU1QTEU= # AKIAIOSFODNN7EXAMPLE
  secret-access-key: d0phbHJYVXRuRkVNSS9LN01ERU5HL2JQeFJmaUNZRVhBTVBMRUtFWQ== # wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY

---
# Backup Storage Class
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: backup-storage
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp3
  iops: "3000"
  throughput: "125"
  encrypted: "true"
allowVolumeExpansion: true
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer

---
# Backup Verification Job
apiVersion: batch/v1
kind: CronJob
metadata:
  name: backup-verification
  namespace: backup-system
spec:
  schedule: "0 6 * * *"  # 6 AM daily
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
            - name: backup-verifier
              image: amazon/aws-cli:latest
              env:
                - name: S3_BUCKET
                  value: "barber-app-backups"
                - name: AWS_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: aws-backup-credentials
                      key: access-key-id
                - name: AWS_SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: aws-backup-credentials
                      key: secret-access-key
              command:
                - /bin/bash
                - -c
                - |
                  set -e
                  
                  echo "Starting backup verification at $(date)"
                  
                  # Check if daily backups exist
                  YESTERDAY=$(date -d "yesterday" +%Y%m%d)
                  
                  # Verify MySQL backup
                  MYSQL_BACKUP=$(aws s3 ls s3://$S3_BUCKET/mysql/daily/ | grep $YESTERDAY | head -1)
                  if [ -z "$MYSQL_BACKUP" ]; then
                    echo "ERROR: No MySQL backup found for $YESTERDAY"
                    exit 1
                  fi
                  
                  # Verify Redis backup
                  REDIS_BACKUP=$(aws s3 ls s3://$S3_BUCKET/redis/daily/ | grep $YESTERDAY | head -1)
                  if [ -z "$REDIS_BACKUP" ]; then
                    echo "ERROR: No Redis backup found for $YESTERDAY"
                    exit 1
                  fi
                  
                  # Verify file backup
                  FILE_BACKUP=$(aws s3 ls s3://$S3_BUCKET/files/daily/ | grep $YESTERDAY | head -1)
                  if [ -z "$FILE_BACKUP" ]; then
                    echo "ERROR: No file backup found for $YESTERDAY"
                    exit 1
                  fi
                  
                  # Test backup integrity (sample check)
                  MYSQL_FILE=$(echo $MYSQL_BACKUP | awk '{print $4}')
                  aws s3 cp s3://$S3_BUCKET/mysql/daily/$MYSQL_FILE /tmp/test_backup.sql.gz
                  
                  # Verify checksum if available
                  if aws s3 ls s3://$S3_BUCKET/mysql/daily/${MYSQL_FILE}.md5; then
                    aws s3 cp s3://$S3_BUCKET/mysql/daily/${MYSQL_FILE}.md5 /tmp/
                    cd /tmp && md5sum -c ${MYSQL_FILE}.md5
                  fi
                  
                  echo "Backup verification completed successfully at $(date)"
              resources:
                requests:
                  cpu: 100m
                  memory: 256Mi
                limits:
                  cpu: 500m
                  memory: 1Gi
