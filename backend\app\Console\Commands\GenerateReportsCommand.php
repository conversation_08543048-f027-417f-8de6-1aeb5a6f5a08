<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AnalyticsService;
use App\Models\User;
use App\Models\Booking;
use App\Models\Payment;
use App\Models\Barber;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class GenerateReportsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:generate 
                            {type=daily : Type of report (daily, weekly, monthly, yearly)}
                            {--date= : Specific date for the report (Y-m-d format)}
                            {--email= : Email address to send the report to}
                            {--save : Save report to storage}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate various types of reports for the barber app';

    protected AnalyticsService $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        parent::__construct();
        $this->analyticsService = $analyticsService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');
        $date = $this->option('date') ? Carbon::parse($this->option('date')) : Carbon::now();
        $email = $this->option('email');
        $save = $this->option('save');

        $this->info("Generating {$type} report for {$date->format('Y-m-d')}...");

        try {
            $report = $this->generateReport($type, $date);
            
            if ($save) {
                $this->saveReport($report, $type, $date);
            }
            
            if ($email) {
                $this->emailReport($report, $type, $date, $email);
            }
            
            $this->displayReport($report, $type);
            
            $this->info('Report generated successfully!');
            
        } catch (\Exception $e) {
            $this->error("Failed to generate report: {$e->getMessage()}");
            return 1;
        }

        return 0;
    }

    protected function generateReport(string $type, Carbon $date): array
    {
        switch ($type) {
            case 'daily':
                return $this->generateDailyReport($date);
            case 'weekly':
                return $this->generateWeeklyReport($date);
            case 'monthly':
                return $this->generateMonthlyReport($date);
            case 'yearly':
                return $this->generateYearlyReport($date);
            default:
                throw new \InvalidArgumentException("Invalid report type: {$type}");
        }
    }

    protected function generateDailyReport(Carbon $date): array
    {
        $startDate = $date->copy()->startOfDay();
        $endDate = $date->copy()->endOfDay();

        return [
            'type' => 'daily',
            'date' => $date->format('Y-m-d'),
            'period' => $date->format('l, F j, Y'),
            'bookings' => $this->getBookingStats($startDate, $endDate),
            'revenue' => $this->getRevenueStats($startDate, $endDate),
            'users' => $this->getUserStats($startDate, $endDate),
            'barbers' => $this->getBarberStats($startDate, $endDate),
            'performance' => $this->getPerformanceStats($startDate, $endDate),
            'top_services' => $this->getTopServices($startDate, $endDate),
            'top_barbers' => $this->getTopBarbers($startDate, $endDate),
        ];
    }

    protected function generateWeeklyReport(Carbon $date): array
    {
        $startDate = $date->copy()->startOfWeek();
        $endDate = $date->copy()->endOfWeek();

        return [
            'type' => 'weekly',
            'date' => $date->format('Y-m-d'),
            'period' => "Week of {$startDate->format('M j')} - {$endDate->format('M j, Y')}",
            'bookings' => $this->getBookingStats($startDate, $endDate),
            'revenue' => $this->getRevenueStats($startDate, $endDate),
            'users' => $this->getUserStats($startDate, $endDate),
            'barbers' => $this->getBarberStats($startDate, $endDate),
            'performance' => $this->getPerformanceStats($startDate, $endDate),
            'top_services' => $this->getTopServices($startDate, $endDate),
            'top_barbers' => $this->getTopBarbers($startDate, $endDate),
            'daily_breakdown' => $this->getDailyBreakdown($startDate, $endDate),
        ];
    }

    protected function generateMonthlyReport(Carbon $date): array
    {
        $startDate = $date->copy()->startOfMonth();
        $endDate = $date->copy()->endOfMonth();

        return [
            'type' => 'monthly',
            'date' => $date->format('Y-m-d'),
            'period' => $date->format('F Y'),
            'bookings' => $this->getBookingStats($startDate, $endDate),
            'revenue' => $this->getRevenueStats($startDate, $endDate),
            'users' => $this->getUserStats($startDate, $endDate),
            'barbers' => $this->getBarberStats($startDate, $endDate),
            'performance' => $this->getPerformanceStats($startDate, $endDate),
            'top_services' => $this->getTopServices($startDate, $endDate),
            'top_barbers' => $this->getTopBarbers($startDate, $endDate),
            'weekly_breakdown' => $this->getWeeklyBreakdown($startDate, $endDate),
            'growth_metrics' => $this->getGrowthMetrics($startDate, $endDate),
        ];
    }

    protected function generateYearlyReport(Carbon $date): array
    {
        $startDate = $date->copy()->startOfYear();
        $endDate = $date->copy()->endOfYear();

        return [
            'type' => 'yearly',
            'date' => $date->format('Y-m-d'),
            'period' => $date->format('Y'),
            'bookings' => $this->getBookingStats($startDate, $endDate),
            'revenue' => $this->getRevenueStats($startDate, $endDate),
            'users' => $this->getUserStats($startDate, $endDate),
            'barbers' => $this->getBarberStats($startDate, $endDate),
            'performance' => $this->getPerformanceStats($startDate, $endDate),
            'top_services' => $this->getTopServices($startDate, $endDate),
            'top_barbers' => $this->getTopBarbers($startDate, $endDate),
            'monthly_breakdown' => $this->getMonthlyBreakdown($startDate, $endDate),
            'growth_metrics' => $this->getGrowthMetrics($startDate, $endDate),
            'yearly_trends' => $this->getYearlyTrends($startDate, $endDate),
        ];
    }

    protected function getBookingStats(Carbon $startDate, Carbon $endDate): array
    {
        $bookings = Booking::whereBetween('created_at', [$startDate, $endDate]);

        return [
            'total' => $bookings->count(),
            'completed' => $bookings->where('status', 'completed')->count(),
            'cancelled' => $bookings->where('status', 'cancelled')->count(),
            'pending' => $bookings->where('status', 'pending')->count(),
            'confirmed' => $bookings->where('status', 'confirmed')->count(),
            'in_progress' => $bookings->where('status', 'in_progress')->count(),
            'completion_rate' => $this->calculateCompletionRate($startDate, $endDate),
            'cancellation_rate' => $this->calculateCancellationRate($startDate, $endDate),
        ];
    }

    protected function getRevenueStats(Carbon $startDate, Carbon $endDate): array
    {
        $payments = Payment::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate]);

        $totalRevenue = $payments->sum('amount');
        $bookings = Booking::whereBetween('created_at', [$startDate, $endDate]);
        $totalCommission = $bookings->sum('commission_amount');
        $totalBarberEarnings = $bookings->sum('barber_amount');

        return [
            'total_revenue' => $totalRevenue,
            'total_commission' => $totalCommission,
            'total_barber_earnings' => $totalBarberEarnings,
            'average_booking_value' => $bookings->avg('total_amount') ?? 0,
            'payment_methods' => $this->getPaymentMethodBreakdown($startDate, $endDate),
        ];
    }

    protected function getUserStats(Carbon $startDate, Carbon $endDate): array
    {
        $newUsers = User::whereBetween('created_at', [$startDate, $endDate])->count();
        $activeUsers = User::whereHas('bookings', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->count();

        return [
            'new_users' => $newUsers,
            'active_users' => $activeUsers,
            'total_users' => User::count(),
            'user_retention' => $this->calculateUserRetention($startDate, $endDate),
        ];
    }

    protected function getBarberStats(Carbon $startDate, Carbon $endDate): array
    {
        $newBarbers = Barber::whereBetween('created_at', [$startDate, $endDate])->count();
        $activeBarbers = Barber::whereHas('bookings', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->count();

        return [
            'new_barbers' => $newBarbers,
            'active_barbers' => $activeBarbers,
            'total_barbers' => Barber::count(),
            'verified_barbers' => Barber::where('is_verified', true)->count(),
            'average_rating' => Barber::avg('rating') ?? 0,
        ];
    }

    protected function getPerformanceStats(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'response_time' => $this->analyticsService->getAverageResponseTime($startDate, $endDate),
            'booking_conversion_rate' => $this->calculateBookingConversionRate($startDate, $endDate),
            'customer_satisfaction' => $this->calculateCustomerSatisfaction($startDate, $endDate),
        ];
    }

    protected function getTopServices(Carbon $startDate, Carbon $endDate, int $limit = 10): array
    {
        return Booking::join('booking_services', 'bookings.id', '=', 'booking_services.booking_id')
            ->join('services', 'booking_services.service_id', '=', 'services.id')
            ->whereBetween('bookings.created_at', [$startDate, $endDate])
            ->selectRaw('services.name, COUNT(*) as bookings_count, SUM(booking_services.total_price) as total_revenue')
            ->groupBy('services.id', 'services.name')
            ->orderByDesc('bookings_count')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    protected function getTopBarbers(Carbon $startDate, Carbon $endDate, int $limit = 10): array
    {
        return Barber::join('users', 'barbers.user_id', '=', 'users.id')
            ->join('bookings', 'barbers.id', '=', 'bookings.barber_id')
            ->whereBetween('bookings.created_at', [$startDate, $endDate])
            ->selectRaw('users.name, COUNT(bookings.id) as bookings_count, SUM(bookings.barber_amount) as total_earnings, AVG(barbers.rating) as rating')
            ->groupBy('barbers.id', 'users.name')
            ->orderByDesc('bookings_count')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    protected function getDailyBreakdown(Carbon $startDate, Carbon $endDate): array
    {
        $breakdown = [];
        $current = $startDate->copy();

        while ($current <= $endDate) {
            $dayStart = $current->copy()->startOfDay();
            $dayEnd = $current->copy()->endOfDay();

            $breakdown[] = [
                'date' => $current->format('Y-m-d'),
                'day' => $current->format('l'),
                'bookings' => Booking::whereBetween('created_at', [$dayStart, $dayEnd])->count(),
                'revenue' => Payment::where('status', 'completed')
                    ->whereBetween('created_at', [$dayStart, $dayEnd])
                    ->sum('amount'),
            ];

            $current->addDay();
        }

        return $breakdown;
    }

    protected function getWeeklyBreakdown(Carbon $startDate, Carbon $endDate): array
    {
        $breakdown = [];
        $current = $startDate->copy()->startOfWeek();

        while ($current <= $endDate) {
            $weekStart = $current->copy();
            $weekEnd = $current->copy()->endOfWeek();

            $breakdown[] = [
                'week_start' => $weekStart->format('Y-m-d'),
                'week_end' => $weekEnd->format('Y-m-d'),
                'bookings' => Booking::whereBetween('created_at', [$weekStart, $weekEnd])->count(),
                'revenue' => Payment::where('status', 'completed')
                    ->whereBetween('created_at', [$weekStart, $weekEnd])
                    ->sum('amount'),
            ];

            $current->addWeek();
        }

        return $breakdown;
    }

    protected function getMonthlyBreakdown(Carbon $startDate, Carbon $endDate): array
    {
        $breakdown = [];
        $current = $startDate->copy()->startOfMonth();

        while ($current <= $endDate) {
            $monthStart = $current->copy()->startOfMonth();
            $monthEnd = $current->copy()->endOfMonth();

            $breakdown[] = [
                'month' => $current->format('Y-m'),
                'month_name' => $current->format('F Y'),
                'bookings' => Booking::whereBetween('created_at', [$monthStart, $monthEnd])->count(),
                'revenue' => Payment::where('status', 'completed')
                    ->whereBetween('created_at', [$monthStart, $monthEnd])
                    ->sum('amount'),
            ];

            $current->addMonth();
        }

        return $breakdown;
    }

    protected function getGrowthMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $previousPeriod = $this->getPreviousPeriod($startDate, $endDate);
        
        $currentBookings = Booking::whereBetween('created_at', [$startDate, $endDate])->count();
        $previousBookings = Booking::whereBetween('created_at', $previousPeriod)->count();
        
        $currentRevenue = Payment::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');
        $previousRevenue = Payment::where('status', 'completed')
            ->whereBetween('created_at', $previousPeriod)
            ->sum('amount');

        return [
            'booking_growth' => $this->calculateGrowthRate($currentBookings, $previousBookings),
            'revenue_growth' => $this->calculateGrowthRate($currentRevenue, $previousRevenue),
        ];
    }

    protected function getYearlyTrends(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for yearly trends analysis
        return [
            'seasonal_patterns' => $this->getSeasonalPatterns($startDate, $endDate),
            'peak_months' => $this->getPeakMonths($startDate, $endDate),
            'growth_trajectory' => $this->getGrowthTrajectory($startDate, $endDate),
        ];
    }

    protected function calculateCompletionRate(Carbon $startDate, Carbon $endDate): float
    {
        $totalBookings = Booking::whereBetween('created_at', [$startDate, $endDate])->count();
        $completedBookings = Booking::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')->count();

        return $totalBookings > 0 ? ($completedBookings / $totalBookings) * 100 : 0;
    }

    protected function calculateCancellationRate(Carbon $startDate, Carbon $endDate): float
    {
        $totalBookings = Booking::whereBetween('created_at', [$startDate, $endDate])->count();
        $cancelledBookings = Booking::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'cancelled')->count();

        return $totalBookings > 0 ? ($cancelledBookings / $totalBookings) * 100 : 0;
    }

    protected function getPaymentMethodBreakdown(Carbon $startDate, Carbon $endDate): array
    {
        return Payment::join('payment_methods', 'payments.payment_method_id', '=', 'payment_methods.id')
            ->where('payments.status', 'completed')
            ->whereBetween('payments.created_at', [$startDate, $endDate])
            ->selectRaw('payment_methods.name, COUNT(*) as count, SUM(payments.amount) as total')
            ->groupBy('payment_methods.id', 'payment_methods.name')
            ->get()
            ->toArray();
    }

    protected function calculateUserRetention(Carbon $startDate, Carbon $endDate): float
    {
        // Simplified retention calculation
        $newUsers = User::whereBetween('created_at', [$startDate, $endDate])->count();
        $returningUsers = User::whereHas('bookings', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->where('created_at', '<', $startDate)->count();

        $totalActiveUsers = $newUsers + $returningUsers;
        return $totalActiveUsers > 0 ? ($returningUsers / $totalActiveUsers) * 100 : 0;
    }

    protected function calculateBookingConversionRate(Carbon $startDate, Carbon $endDate): float
    {
        // This would require tracking user sessions/visits
        // For now, return a placeholder
        return 0;
    }

    protected function calculateCustomerSatisfaction(Carbon $startDate, Carbon $endDate): float
    {
        $averageRating = \App\Models\Review::whereBetween('created_at', [$startDate, $endDate])
            ->avg('rating');

        return $averageRating ? ($averageRating / 5) * 100 : 0;
    }

    protected function getPreviousPeriod(Carbon $startDate, Carbon $endDate): array
    {
        $duration = $startDate->diffInDays($endDate) + 1;
        $previousStart = $startDate->copy()->subDays($duration);
        $previousEnd = $startDate->copy()->subDay();

        return [$previousStart, $previousEnd];
    }

    protected function calculateGrowthRate(float $current, float $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return (($current - $previous) / $previous) * 100;
    }

    protected function getSeasonalPatterns(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for seasonal pattern analysis
        return [];
    }

    protected function getPeakMonths(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for peak months analysis
        return [];
    }

    protected function getGrowthTrajectory(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for growth trajectory analysis
        return [];
    }

    protected function displayReport(array $report, string $type): void
    {
        $this->line('');
        $this->line("=== {$report['period']} Report ===");
        $this->line('');

        // Bookings
        $this->info('📊 Bookings:');
        $this->line("  Total: {$report['bookings']['total']}");
        $this->line("  Completed: {$report['bookings']['completed']}");
        $this->line("  Cancelled: {$report['bookings']['cancelled']}");
        $this->line("  Completion Rate: " . number_format($report['bookings']['completion_rate'], 2) . '%');
        $this->line('');

        // Revenue
        $this->info('💰 Revenue:');
        $this->line("  Total Revenue: " . number_format($report['revenue']['total_revenue'], 2) . ' EGP');
        $this->line("  Commission: " . number_format($report['revenue']['total_commission'], 2) . ' EGP');
        $this->line("  Barber Earnings: " . number_format($report['revenue']['total_barber_earnings'], 2) . ' EGP');
        $this->line("  Avg Booking Value: " . number_format($report['revenue']['average_booking_value'], 2) . ' EGP');
        $this->line('');

        // Users
        $this->info('👥 Users:');
        $this->line("  New Users: {$report['users']['new_users']}");
        $this->line("  Active Users: {$report['users']['active_users']}");
        $this->line("  Total Users: {$report['users']['total_users']}");
        $this->line('');

        // Top Services
        if (!empty($report['top_services'])) {
            $this->info('🔝 Top Services:');
            foreach (array_slice($report['top_services'], 0, 5) as $service) {
                $this->line("  {$service['name']}: {$service['bookings_count']} bookings");
            }
        }
    }

    protected function saveReport(array $report, string $type, Carbon $date): void
    {
        $filename = "reports/{$type}/" . $date->format('Y-m-d') . "_report.json";
        Storage::disk('local')->put($filename, json_encode($report, JSON_PRETTY_PRINT));
        $this->info("Report saved to: {$filename}");
    }

    protected function emailReport(array $report, string $type, Carbon $date, string $email): void
    {
        // Implementation for emailing reports
        $this->info("Report would be emailed to: {$email}");
    }
}
