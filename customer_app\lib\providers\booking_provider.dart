import 'package:flutter/foundation.dart';

class BookingProvider extends ChangeNotifier {
  List<Booking> _bookings = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Booking> get bookings => _bookings;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Get upcoming bookings
  List<Booking> get upcomingBookings {
    return _bookings.where((booking) =>
      booking.status == BookingStatus.confirmed ||
      booking.status == BookingStatus.pending
    ).toList();
  }

  // Get past bookings
  List<Booking> get pastBookings {
    return _bookings.where((booking) =>
      booking.status == BookingStatus.completed ||
      booking.status == BookingStatus.cancelled
    ).toList();
  }

  // Load bookings
  Future<void> loadBookings() async {
    try {
      _setLoading(true);
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      _bookings = [
        Booking(
          id: '1',
          barberId: '1',
          barberName: 'أحمد محمد',
          service: 'قص شعر',
          date: DateTime.now().add(const Duration(days: 1)),
          price: 25,
          status: BookingStatus.confirmed,
        ),
        Booking(
          id: '2',
          barberId: '2',
          barberName: 'محمد علي',
          service: 'حلاقة كاملة',
          date: DateTime.now().subtract(const Duration(days: 2)),
          price: 50,
          status: BookingStatus.completed,
        ),
      ];
      
      _clearError();
    } catch (e) {
      _setError('خطأ في تحميل الحجوزات');
    } finally {
      _setLoading(false);
    }
  }

  // Create booking
  Future<bool> createBooking({
    required String barberId,
    required String barberName,
    required String service,
    required DateTime date,
    required double price,
  }) async {
    try {
      _setLoading(true);
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      final booking = Booking(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        barberId: barberId,
        barberName: barberName,
        service: service,
        date: date,
        price: price,
        status: BookingStatus.pending,
      );
      
      _bookings.insert(0, booking);
      _clearError();
      return true;
    } catch (e) {
      _setError('خطأ في إنشاء الحجز');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Cancel booking
  Future<bool> cancelBooking(String bookingId) async {
    try {
      _setLoading(true);
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      final index = _bookings.indexWhere((b) => b.id == bookingId);
      if (index != -1) {
        _bookings[index] = _bookings[index].copyWith(
          status: BookingStatus.cancelled,
        );
      }
      
      _clearError();
      return true;
    } catch (e) {
      _setError('خطأ في إلغاء الحجز');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Reschedule booking
  Future<bool> rescheduleBooking(String bookingId, DateTime newDate) async {
    try {
      _setLoading(true);
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      final index = _bookings.indexWhere((b) => b.id == bookingId);
      if (index != -1) {
        _bookings[index] = _bookings[index].copyWith(date: newDate);
      }
      
      _clearError();
      return true;
    } catch (e) {
      _setError('خطأ في إعادة جدولة الحجز');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}

class Booking {
  final String id;
  final String barberId;
  final String barberName;
  final String service;
  final DateTime date;
  final double price;
  final BookingStatus status;

  Booking({
    required this.id,
    required this.barberId,
    required this.barberName,
    required this.service,
    required this.date,
    required this.price,
    required this.status,
  });

  Booking copyWith({
    String? id,
    String? barberId,
    String? barberName,
    String? service,
    DateTime? date,
    double? price,
    BookingStatus? status,
  }) {
    return Booking(
      id: id ?? this.id,
      barberId: barberId ?? this.barberId,
      barberName: barberName ?? this.barberName,
      service: service ?? this.service,
      date: date ?? this.date,
      price: price ?? this.price,
      status: status ?? this.status,
    );
  }

  String get statusText {
    switch (status) {
      case BookingStatus.pending:
        return 'في الانتظار';
      case BookingStatus.confirmed:
        return 'مؤكد';
      case BookingStatus.inProgress:
        return 'جاري التنفيذ';
      case BookingStatus.completed:
        return 'مكتمل';
      case BookingStatus.cancelled:
        return 'ملغي';
    }
  }
}

enum BookingStatus {
  pending,
  confirmed,
  inProgress,
  completed,
  cancelled,
}
