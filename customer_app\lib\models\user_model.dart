import 'dart:convert';

class User {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String avatar;
  final String loyaltyLevel;
  final int loyaltyPoints;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? city;
  final String? country;
  final bool isVip;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.avatar,
    required this.loyaltyLevel,
    required this.loyaltyPoints,
    this.dateOfBirth,
    this.gender,
    this.city,
    this.country,
    this.isVip = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  // Copy with method
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? avatar,
    String? loyaltyLevel,
    int? loyaltyPoints,
    DateTime? dateOfBirth,
    String? gender,
    String? city,
    String? country,
    bool? isVip,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      loyaltyLevel: loyaltyLevel ?? this.loyaltyLevel,
      loyaltyPoints: loyaltyPoints ?? this.loyaltyPoints,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      city: city ?? this.city,
      country: country ?? this.country,
      isVip: isVip ?? this.isVip,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  // To JSON
  String toJson() {
    return jsonEncode({
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'loyaltyLevel': loyaltyLevel,
      'loyaltyPoints': loyaltyPoints,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'gender': gender,
      'city': city,
      'country': country,
      'isVip': isVip,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    });
  }

  // From JSON
  factory User.fromJson(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return User(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      avatar: json['avatar'] ?? '',
      loyaltyLevel: json['loyaltyLevel'] ?? 'bronze',
      loyaltyPoints: json['loyaltyPoints'] ?? 0,
      dateOfBirth: json['dateOfBirth'] != null
          ? DateTime.parse(json['dateOfBirth'])
          : null,
      gender: json['gender'],
      city: json['city'],
      country: json['country'],
      isVip: json['isVip'] ?? false,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
    );
  }

  // From Map
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      avatar: map['avatar'] ?? '',
      loyaltyLevel: map['loyaltyLevel'] ?? 'bronze',
      loyaltyPoints: map['loyaltyPoints'] ?? 0,
      dateOfBirth: map['dateOfBirth'] != null
          ? DateTime.parse(map['dateOfBirth'])
          : null,
      gender: map['gender'],
      city: map['city'],
      country: map['country'],
      isVip: map['isVip'] ?? false,
      createdAt: map['createdAt'] != null
          ? DateTime.parse(map['createdAt'])
          : DateTime.now(),
      updatedAt: map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'])
          : DateTime.now(),
    );
  }

  // To Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'loyaltyLevel': loyaltyLevel,
      'loyaltyPoints': loyaltyPoints,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'gender': gender,
      'city': city,
      'country': country,
      'isVip': isVip,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Get loyalty level display name
  String get loyaltyLevelDisplayName {
    switch (loyaltyLevel) {
      case 'bronze':
        return 'برونزي';
      case 'silver':
        return 'فضي';
      case 'gold':
        return 'ذهبي';
      case 'platinum':
        return 'بلاتيني';
      default:
        return 'برونزي';
    }
  }

  // Get loyalty level color
  String get loyaltyLevelColor {
    switch (loyaltyLevel) {
      case 'bronze':
        return '#CD7F32';
      case 'silver':
        return '#C0C0C0';
      case 'gold':
        return '#FFD700';
      case 'platinum':
        return '#E5E4E2';
      default:
        return '#CD7F32';
    }
  }

  // Get points needed for next level
  int get pointsNeededForNextLevel {
    switch (loyaltyLevel) {
      case 'bronze':
        return 500 - loyaltyPoints;
      case 'silver':
        return 1000 - loyaltyPoints;
      case 'gold':
        return 2000 - loyaltyPoints;
      case 'platinum':
        return 0; // Already at highest level
      default:
        return 500 - loyaltyPoints;
    }
  }

  // Get next loyalty level
  String get nextLoyaltyLevel {
    switch (loyaltyLevel) {
      case 'bronze':
        return 'silver';
      case 'silver':
        return 'gold';
      case 'gold':
        return 'platinum';
      case 'platinum':
        return 'platinum'; // Already at highest level
      default:
        return 'silver';
    }
  }

  // Check if user has enough points for redemption
  bool canRedeemPoints(int points) {
    return loyaltyPoints >= points;
  }

  // Get user initials for avatar
  String get initials {
    final names = name.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names[0][0].toUpperCase();
    }
    return 'U';
  }

  // Get display name (first name only)
  String get firstName {
    final names = name.split(' ');
    return names.isNotEmpty ? names[0] : name;
  }

  // Get age from date of birth
  int? get age {
    if (dateOfBirth == null) return null;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month ||
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, loyaltyLevel: $loyaltyLevel, loyaltyPoints: $loyaltyPoints)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
