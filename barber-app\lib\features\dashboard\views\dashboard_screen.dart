import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:animate_do/animate_do.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/app_router.dart';
import '../../../shared/widgets/status_card.dart';
import '../../../shared/widgets/earnings_chart.dart';
import '../../../shared/widgets/booking_request_card.dart';
import '../../../shared/widgets/quick_action_button.dart';
import '../providers/dashboard_provider.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  late DashboardProvider _dashboardProvider;

  @override
  void initState() {
    super.initState();
    _dashboardProvider = Provider.of<DashboardProvider>(context, listen: false);
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    await _dashboardProvider.loadDashboardData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _loadDashboardData,
          child: Consumer<DashboardProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading && provider.dashboardData == null) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header Section
                    _buildHeader(provider),
                    
                    const SizedBox(height: 24),
                    
                    // Status Toggle
                    _buildStatusToggle(provider),
                    
                    const SizedBox(height: 24),
                    
                    // Statistics Cards
                    _buildStatisticsCards(provider),
                    
                    const SizedBox(height: 24),
                    
                    // Quick Actions
                    _buildQuickActions(),
                    
                    const SizedBox(height: 24),
                    
                    // Earnings Chart
                    _buildEarningsChart(provider),
                    
                    const SizedBox(height: 24),
                    
                    // Recent Booking Requests
                    _buildRecentBookingRequests(provider),
                    
                    const SizedBox(height: 24),
                    
                    // Today's Schedule
                    _buildTodaySchedule(provider),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(DashboardProvider provider) {
    return FadeInDown(
      duration: const Duration(milliseconds: 600),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: AppColors.professionalGradient,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.3),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: provider.barber?.avatar != null
                      ? NetworkImage(provider.barber!.avatar!)
                      : null,
                  child: provider.barber?.avatar == null
                      ? const Icon(Icons.person, size: 30, color: Colors.white)
                      : null,
                ),
                
                const SizedBox(width: 16),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مرحباً، ${provider.barber?.name ?? 'الحلاق'}',
                        style: AppTextStyles.h5.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      
                      const SizedBox(height: 4),
                      
                      Text(
                        'جاهز لتقديم أفضل الخدمات',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                
                IconButton(
                  onPressed: () => AppRouter.toNotifications(),
                  icon: Stack(
                    children: [
                      const Icon(
                        Icons.notifications_outlined,
                        color: Colors.white,
                        size: 28,
                      ),
                      if (provider.unreadNotificationsCount > 0)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              color: AppColors.error,
                              shape: BoxShape.circle,
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 16,
                              minHeight: 16,
                            ),
                            child: Text(
                              '${provider.unreadNotificationsCount}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Icon(
                  Icons.star,
                  color: AppColors.secondary,
                  size: 20,
                ),
                
                const SizedBox(width: 8),
                
                Text(
                  '${provider.barber?.rating?.toStringAsFixed(1) ?? '0.0'} (${provider.barber?.totalRatings ?? 0} تقييم)',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                
                const Spacer(),
                
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    provider.barber?.isVip == true ? 'حلاق VIP' : 'حلاق عادي',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusToggle(DashboardProvider provider) {
    return FadeInUp(
      delay: const Duration(milliseconds: 200),
      duration: const Duration(milliseconds: 600),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(
              Icons.power_settings_new,
              color: AppColors.getStatusColor(provider.currentStatus),
              size: 24,
            ),
            
            const SizedBox(width: 12),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'حالة التوفر',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  
                  Text(
                    _getStatusText(provider.currentStatus),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            
            Switch(
              value: provider.currentStatus == 'online',
              onChanged: (value) {
                provider.toggleStatus(value ? 'online' : 'offline');
              },
              activeColor: AppColors.success,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCards(DashboardProvider provider) {
    final stats = [
      {
        'title': 'حجوزات اليوم',
        'value': '${provider.todayBookings}',
        'icon': Icons.calendar_today,
        'color': AppColors.info,
        'change': '+12%',
      },
      {
        'title': 'الأرباح اليوم',
        'value': '${provider.todayEarnings} ج.م',
        'icon': Icons.attach_money,
        'color': AppColors.success,
        'change': '+8%',
      },
      {
        'title': 'التقييم',
        'value': '${provider.barber?.rating?.toStringAsFixed(1) ?? '0.0'}',
        'icon': Icons.star,
        'color': AppColors.secondary,
        'change': '+0.2',
      },
      {
        'title': 'طلبات جديدة',
        'value': '${provider.pendingRequests}',
        'icon': Icons.notifications_active,
        'color': AppColors.warning,
        'change': '+3',
      },
    ];

    return FadeInUp(
      delay: const Duration(milliseconds: 400),
      duration: const Duration(milliseconds: 600),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
        ),
        itemCount: stats.length,
        itemBuilder: (context, index) {
          final stat = stats[index];
          return StatusCard(
            title: stat['title'] as String,
            value: stat['value'] as String,
            icon: stat['icon'] as IconData,
            color: stat['color'] as Color,
            change: stat['change'] as String,
          );
        },
      ),
    );
  }

  Widget _buildQuickActions() {
    final actions = [
      {
        'title': 'الحجوزات',
        'icon': Icons.calendar_month,
        'color': AppColors.info,
        'onTap': () => AppRouter.toBookings(),
      },
      {
        'title': 'الأرباح',
        'icon': Icons.account_balance_wallet,
        'color': AppColors.success,
        'onTap': () => AppRouter.toEarnings(),
      },
      {
        'title': 'الجدول',
        'icon': Icons.schedule,
        'color': AppColors.warning,
        'onTap': () => AppRouter.toSchedule(),
      },
      {
        'title': 'الخدمات',
        'icon': Icons.content_cut,
        'color': AppColors.secondary,
        'onTap': () => AppRouter.toServices(),
      },
    ];

    return FadeInUp(
      delay: const Duration(milliseconds: 600),
      duration: const Duration(milliseconds: 600),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات سريعة',
            style: AppTextStyles.h6.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: actions.map((action) {
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: QuickActionButton(
                    title: action['title'] as String,
                    icon: action['icon'] as IconData,
                    color: action['color'] as Color,
                    onTap: action['onTap'] as VoidCallback,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEarningsChart(DashboardProvider provider) {
    return FadeInUp(
      delay: const Duration(milliseconds: 800),
      duration: const Duration(milliseconds: 600),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الأرباح الأسبوعية',
                  style: AppTextStyles.h6.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                TextButton(
                  onPressed: () => AppRouter.toEarnings(),
                  child: Text(
                    'عرض التفاصيل',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            SizedBox(
              height: 200,
              child: EarningsChart(
                data: provider.weeklyEarnings,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentBookingRequests(DashboardProvider provider) {
    return FadeInUp(
      delay: const Duration(milliseconds: 1000),
      duration: const Duration(milliseconds: 600),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'طلبات الحجز الجديدة',
                style: AppTextStyles.h6.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              TextButton(
                onPressed: () => AppRouter.toBookingRequests(),
                child: Text(
                  'عرض الكل',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          if (provider.recentBookingRequests.isEmpty)
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.borderLight),
              ),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.inbox_outlined,
                      size: 48,
                      color: AppColors.textLight,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    Text(
                      'لا توجد طلبات حجز جديدة',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ...provider.recentBookingRequests.take(3).map((request) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: BookingRequestCard(
                  booking: request,
                  onAccept: () => provider.acceptBooking(request.id),
                  onReject: () => provider.rejectBooking(request.id),
                ),
              );
            }).toList(),
        ],
      ),
    );
  }

  Widget _buildTodaySchedule(DashboardProvider provider) {
    return FadeInUp(
      delay: const Duration(milliseconds: 1200),
      duration: const Duration(milliseconds: 600),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'جدول اليوم',
                  style: AppTextStyles.h6.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                TextButton(
                  onPressed: () => AppRouter.toSchedule(),
                  child: Text(
                    'عرض الجدول الكامل',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (provider.todaySchedule.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Text(
                    'لا توجد مواعيد محجوزة اليوم',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              )
            else
              ...provider.todaySchedule.take(3).map((appointment) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.greyLight,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.borderLight),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 4,
                        height: 40,
                        decoration: BoxDecoration(
                          color: AppColors.getBookingStatusColor(appointment.status),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      
                      const SizedBox(width: 12),
                      
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              appointment.customerName,
                              style: AppTextStyles.bodyMedium.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            
                            Text(
                              '${appointment.serviceName} - ${appointment.time}',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      Text(
                        '${appointment.price} ج.م',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
          ],
        ),
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'online':
        return 'متاح لاستقبال الطلبات';
      case 'offline':
        return 'غير متاح حالياً';
      case 'busy':
        return 'مشغول في خدمة عميل';
      case 'break':
        return 'في استراحة';
      default:
        return 'غير محدد';
    }
  }
}
