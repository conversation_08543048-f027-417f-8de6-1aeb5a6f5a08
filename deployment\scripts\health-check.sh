#!/bin/bash

# Health Check Script for Barber App
# This script performs comprehensive health checks on all system components
# Usage: ./health-check.sh [--verbose] [--component=<component>]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
VERBOSE=false
SPECIFIC_COMPONENT=""
HEALTH_CHECK_TIMEOUT=30
LOG_FILE="/var/log/barber-app/health-check.log"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --verbose)
            VERBOSE=true
            shift
            ;;
        --component=*)
            SPECIFIC_COMPONENT="${1#*=}"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--verbose] [--component=<component>]"
            echo "Components: app, database, redis, nginx, elasticsearch, monitoring"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

# Logging function
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    if [[ "$VERBOSE" == "true" ]] || [[ "$level" == "ERROR" ]]; then
        case $level in
            "INFO")
                echo -e "${BLUE}[INFO]${NC} $message"
                ;;
            "SUCCESS")
                echo -e "${GREEN}[SUCCESS]${NC} $message"
                ;;
            "WARNING")
                echo -e "${YELLOW}[WARNING]${NC} $message"
                ;;
            "ERROR")
                echo -e "${RED}[ERROR]${NC} $message"
                ;;
        esac
    fi
}

# Function to check if a service is running
check_service_status() {
    local service_name=$1
    local container_name=$2
    
    log "INFO" "Checking $service_name status..."
    
    if docker ps --format "table {{.Names}}" | grep -q "^$container_name$"; then
        log "SUCCESS" "$service_name is running"
        return 0
    else
        log "ERROR" "$service_name is not running"
        return 1
    fi
}

# Function to check HTTP endpoint
check_http_endpoint() {
    local name=$1
    local url=$2
    local expected_status=${3:-200}
    
    log "INFO" "Checking $name endpoint: $url"
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" --max-time $HEALTH_CHECK_TIMEOUT "$url" 2>/dev/null || echo "000")
    
    if [[ "$response" == "$expected_status" ]]; then
        log "SUCCESS" "$name endpoint is healthy (HTTP $response)"
        return 0
    else
        log "ERROR" "$name endpoint is unhealthy (HTTP $response)"
        return 1
    fi
}

# Function to check database connectivity
check_database() {
    log "INFO" "Checking database connectivity..."
    
    local db_check=$(docker-compose exec -T db mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT 1;" 2>/dev/null | grep -c "1" || echo "0")
    
    if [[ "$db_check" == "1" ]]; then
        log "SUCCESS" "Database is accessible"
        
        # Check database size
        local db_size=$(docker-compose exec -T db mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' FROM information_schema.tables WHERE table_schema='barber_db';" 2>/dev/null | tail -n 1)
        log "INFO" "Database size: ${db_size} MB"
        
        # Check active connections
        local connections=$(docker-compose exec -T db mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW STATUS LIKE 'Threads_connected';" 2>/dev/null | tail -n 1 | awk '{print $2}')
        log "INFO" "Active database connections: $connections"
        
        return 0
    else
        log "ERROR" "Database is not accessible"
        return 1
    fi
}

# Function to check Redis
check_redis() {
    log "INFO" "Checking Redis connectivity..."
    
    local redis_check=$(docker-compose exec -T redis redis-cli ping 2>/dev/null || echo "FAILED")
    
    if [[ "$redis_check" == "PONG" ]]; then
        log "SUCCESS" "Redis is accessible"
        
        # Check Redis memory usage
        local memory_usage=$(docker-compose exec -T redis redis-cli info memory | grep "used_memory_human" | cut -d: -f2 | tr -d '\r')
        log "INFO" "Redis memory usage: $memory_usage"
        
        # Check connected clients
        local clients=$(docker-compose exec -T redis redis-cli info clients | grep "connected_clients" | cut -d: -f2 | tr -d '\r')
        log "INFO" "Redis connected clients: $clients"
        
        return 0
    else
        log "ERROR" "Redis is not accessible"
        return 1
    fi
}

# Function to check application health
check_application() {
    log "INFO" "Checking application health..."
    
    # Check if app container is running
    if ! check_service_status "Application" "barber-app_app_1"; then
        return 1
    fi
    
    # Check application health endpoint
    if ! check_http_endpoint "Application Health" "http://localhost/api/health"; then
        return 1
    fi
    
    # Check application metrics
    local app_metrics=$(curl -s "http://localhost/api/metrics" --max-time 10 2>/dev/null || echo "")
    if [[ -n "$app_metrics" ]]; then
        log "SUCCESS" "Application metrics are available"
        
        # Parse some basic metrics
        local memory_usage=$(echo "$app_metrics" | grep "memory_usage" | head -n 1 | awk '{print $2}' || echo "N/A")
        local cpu_usage=$(echo "$app_metrics" | grep "cpu_usage" | head -n 1 | awk '{print $2}' || echo "N/A")
        
        log "INFO" "Application memory usage: ${memory_usage}%"
        log "INFO" "Application CPU usage: ${cpu_usage}%"
    else
        log "WARNING" "Application metrics are not available"
    fi
    
    return 0
}

# Function to check Nginx
check_nginx() {
    log "INFO" "Checking Nginx status..."
    
    if ! check_service_status "Nginx" "barber-app_webserver_1"; then
        return 1
    fi
    
    # Check Nginx configuration
    local nginx_config_test=$(docker-compose exec -T webserver nginx -t 2>&1 || echo "FAILED")
    if echo "$nginx_config_test" | grep -q "syntax is ok"; then
        log "SUCCESS" "Nginx configuration is valid"
    else
        log "ERROR" "Nginx configuration has errors"
        return 1
    fi
    
    # Check main endpoints
    check_http_endpoint "Main Website" "http://localhost"
    check_http_endpoint "API Endpoint" "http://localhost/api/health"
    check_http_endpoint "Admin Panel" "http://localhost/admin"
    
    return 0
}

# Function to check Elasticsearch
check_elasticsearch() {
    log "INFO" "Checking Elasticsearch status..."
    
    if ! check_service_status "Elasticsearch" "barber-app_elasticsearch_1"; then
        return 1
    fi
    
    # Check Elasticsearch health
    local es_health=$(curl -s "http://localhost:9200/_cluster/health" --max-time 10 2>/dev/null || echo "")
    if [[ -n "$es_health" ]]; then
        local es_status=$(echo "$es_health" | jq -r '.status' 2>/dev/null || echo "unknown")
        
        case $es_status in
            "green")
                log "SUCCESS" "Elasticsearch cluster is healthy (green)"
                ;;
            "yellow")
                log "WARNING" "Elasticsearch cluster has warnings (yellow)"
                ;;
            "red")
                log "ERROR" "Elasticsearch cluster is unhealthy (red)"
                return 1
                ;;
            *)
                log "ERROR" "Elasticsearch cluster status is unknown"
                return 1
                ;;
        esac
        
        # Get cluster info
        local node_count=$(echo "$es_health" | jq -r '.number_of_nodes' 2>/dev/null || echo "N/A")
        local data_nodes=$(echo "$es_health" | jq -r '.number_of_data_nodes' 2>/dev/null || echo "N/A")
        
        log "INFO" "Elasticsearch nodes: $node_count (data nodes: $data_nodes)"
    else
        log "ERROR" "Elasticsearch is not responding"
        return 1
    fi
    
    return 0
}

# Function to check monitoring services
check_monitoring() {
    log "INFO" "Checking monitoring services..."
    
    local monitoring_status=0
    
    # Check Prometheus
    if check_service_status "Prometheus" "barber-app_prometheus_1"; then
        if check_http_endpoint "Prometheus" "http://localhost:9090/-/healthy"; then
            log "SUCCESS" "Prometheus is healthy"
        else
            monitoring_status=1
        fi
    else
        monitoring_status=1
    fi
    
    # Check Grafana
    if check_service_status "Grafana" "barber-app_grafana_1"; then
        if check_http_endpoint "Grafana" "http://localhost:3000/api/health"; then
            log "SUCCESS" "Grafana is healthy"
        else
            monitoring_status=1
        fi
    else
        monitoring_status=1
    fi
    
    # Check Alertmanager
    if check_service_status "Alertmanager" "barber-app_alertmanager_1"; then
        if check_http_endpoint "Alertmanager" "http://localhost:9093/-/healthy"; then
            log "SUCCESS" "Alertmanager is healthy"
        else
            monitoring_status=1
        fi
    else
        monitoring_status=1
    fi
    
    return $monitoring_status
}

# Function to check system resources
check_system_resources() {
    log "INFO" "Checking system resources..."
    
    # Check disk space
    local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $disk_usage -gt 90 ]]; then
        log "ERROR" "Disk usage is critical: ${disk_usage}%"
        return 1
    elif [[ $disk_usage -gt 80 ]]; then
        log "WARNING" "Disk usage is high: ${disk_usage}%"
    else
        log "SUCCESS" "Disk usage is normal: ${disk_usage}%"
    fi
    
    # Check memory usage
    local memory_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [[ $memory_usage -gt 90 ]]; then
        log "ERROR" "Memory usage is critical: ${memory_usage}%"
        return 1
    elif [[ $memory_usage -gt 80 ]]; then
        log "WARNING" "Memory usage is high: ${memory_usage}%"
    else
        log "SUCCESS" "Memory usage is normal: ${memory_usage}%"
    fi
    
    # Check CPU load
    local cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local cpu_load_percent=$(echo "$cpu_load $cpu_cores" | awk '{printf "%.0f", ($1/$2)*100}')
    
    if [[ $cpu_load_percent -gt 90 ]]; then
        log "ERROR" "CPU load is critical: ${cpu_load_percent}%"
        return 1
    elif [[ $cpu_load_percent -gt 80 ]]; then
        log "WARNING" "CPU load is high: ${cpu_load_percent}%"
    else
        log "SUCCESS" "CPU load is normal: ${cpu_load_percent}%"
    fi
    
    return 0
}

# Function to check SSL certificates
check_ssl_certificates() {
    log "INFO" "Checking SSL certificates..."
    
    local domains=("api.barber-app.com" "admin.barber-app.com" "barber-app.com")
    local ssl_status=0
    
    for domain in "${domains[@]}"; do
        local cert_info=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "")
        
        if [[ -n "$cert_info" ]]; then
            local expiry_date=$(echo "$cert_info" | grep "notAfter" | cut -d= -f2)
            local expiry_timestamp=$(date -d "$expiry_date" +%s 2>/dev/null || echo "0")
            local current_timestamp=$(date +%s)
            local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
            
            if [[ $days_until_expiry -lt 7 ]]; then
                log "ERROR" "SSL certificate for $domain expires in $days_until_expiry days"
                ssl_status=1
            elif [[ $days_until_expiry -lt 30 ]]; then
                log "WARNING" "SSL certificate for $domain expires in $days_until_expiry days"
            else
                log "SUCCESS" "SSL certificate for $domain is valid ($days_until_expiry days remaining)"
            fi
        else
            log "ERROR" "Could not check SSL certificate for $domain"
            ssl_status=1
        fi
    done
    
    return $ssl_status
}

# Main health check function
run_health_checks() {
    local overall_status=0
    local checks_run=0
    local checks_passed=0
    
    log "INFO" "Starting comprehensive health check..."
    
    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Run specific component check if requested
    if [[ -n "$SPECIFIC_COMPONENT" ]]; then
        case $SPECIFIC_COMPONENT in
            "app"|"application")
                check_application && checks_passed=$((checks_passed + 1)) || overall_status=1
                checks_run=1
                ;;
            "database"|"db")
                check_database && checks_passed=$((checks_passed + 1)) || overall_status=1
                checks_run=1
                ;;
            "redis")
                check_redis && checks_passed=$((checks_passed + 1)) || overall_status=1
                checks_run=1
                ;;
            "nginx")
                check_nginx && checks_passed=$((checks_passed + 1)) || overall_status=1
                checks_run=1
                ;;
            "elasticsearch")
                check_elasticsearch && checks_passed=$((checks_passed + 1)) || overall_status=1
                checks_run=1
                ;;
            "monitoring")
                check_monitoring && checks_passed=$((checks_passed + 1)) || overall_status=1
                checks_run=1
                ;;
            *)
                log "ERROR" "Unknown component: $SPECIFIC_COMPONENT"
                exit 1
                ;;
        esac
    else
        # Run all checks
        check_system_resources && checks_passed=$((checks_passed + 1)) || overall_status=1
        checks_run=$((checks_run + 1))
        
        check_application && checks_passed=$((checks_passed + 1)) || overall_status=1
        checks_run=$((checks_run + 1))
        
        check_database && checks_passed=$((checks_passed + 1)) || overall_status=1
        checks_run=$((checks_run + 1))
        
        check_redis && checks_passed=$((checks_passed + 1)) || overall_status=1
        checks_run=$((checks_run + 1))
        
        check_nginx && checks_passed=$((checks_passed + 1)) || overall_status=1
        checks_run=$((checks_run + 1))
        
        check_elasticsearch && checks_passed=$((checks_passed + 1)) || overall_status=1
        checks_run=$((checks_run + 1))
        
        check_monitoring && checks_passed=$((checks_passed + 1)) || overall_status=1
        checks_run=$((checks_run + 1))
        
        check_ssl_certificates && checks_passed=$((checks_passed + 1)) || overall_status=1
        checks_run=$((checks_run + 1))
    fi
    
    # Summary
    log "INFO" "Health check completed: $checks_passed/$checks_run checks passed"
    
    if [[ $overall_status -eq 0 ]]; then
        log "SUCCESS" "All health checks passed! System is healthy."
        echo -e "${GREEN}✓ System is healthy${NC}"
    else
        log "ERROR" "Some health checks failed! System needs attention."
        echo -e "${RED}✗ System has issues${NC}"
    fi
    
    return $overall_status
}

# Run the health checks
run_health_checks
exit $?
