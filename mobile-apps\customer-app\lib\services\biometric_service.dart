import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_ios/local_auth_ios.dart';
import 'package:crypto/crypto.dart';
import 'package:pointycastle/export.dart';

class BiometricService {
  static final BiometricService _instance = BiometricService._internal();
  factory BiometricService() => _instance;
  BiometricService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();
  bool _isInitialized = false;
  List<BiometricType> _availableBiometrics = [];
  
  // Biometric data storage
  final Map<String, BiometricTemplate> _biometricTemplates = {};
  final Map<String, String> _encryptedBiometricData = {};

  /// Initialize biometric service
  Future<bool> initialize() async {
    try {
      // Check if biometric authentication is available
      final bool isAvailable = await _localAuth.isDeviceSupported();
      if (!isAvailable) {
        print('Biometric authentication not supported on this device');
        return false;
      }

      // Check if biometrics are enrolled
      final bool canCheckBiometrics = await _localAuth.canCheckBiometrics;
      if (!canCheckBiometrics) {
        print('No biometrics enrolled on this device');
        return false;
      }

      // Get available biometric types
      _availableBiometrics = await _localAuth.getAvailableBiometrics();
      
      _isInitialized = true;
      return true;
    } catch (e) {
      print('Biometric service initialization failed: $e');
      return false;
    }
  }

  /// Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    return _availableBiometrics.isNotEmpty;
  }

  /// Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    return _availableBiometrics;
  }

  /// Authenticate using biometrics
  Future<BiometricAuthResult> authenticateWithBiometrics({
    String reason = 'Please authenticate to continue',
    bool useErrorDialogs = true,
    bool stickyAuth = false,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (_availableBiometrics.isEmpty) {
        return BiometricAuthResult(
          success: false,
          error: 'No biometric authentication methods available',
          errorType: BiometricErrorType.notAvailable,
        );
      }

      final bool didAuthenticate = await _localAuth.authenticate(
        localizedFallbackTitle: 'Use PIN',
        authMessages: const [
          AndroidAuthMessages(
            signInTitle: 'Biometric Authentication',
            cancelButton: 'Cancel',
            deviceCredentialsRequiredTitle: 'Device Credential Required',
            deviceCredentialsSetupDescription: 'Please set up device credentials',
            goToSettingsButton: 'Go to Settings',
            goToSettingsDescription: 'Please set up biometric authentication',
          ),
          IOSAuthMessages(
            cancelButton: 'Cancel',
            goToSettingsButton: 'Go to Settings',
            goToSettingsDescription: 'Please set up biometric authentication',
            lockOut: 'Please re-enable biometric authentication',
          ),
        ],
        options: AuthenticationOptions(
          useErrorDialogs: useErrorDialogs,
          stickyAuth: stickyAuth,
          biometricOnly: false,
        ),
      );

      if (didAuthenticate) {
        // Generate biometric session token
        final sessionToken = await _generateBiometricSessionToken();
        
        return BiometricAuthResult(
          success: true,
          sessionToken: sessionToken,
          authenticatedBiometrics: _availableBiometrics,
          timestamp: DateTime.now(),
        );
      } else {
        return BiometricAuthResult(
          success: false,
          error: 'Authentication failed or was cancelled',
          errorType: BiometricErrorType.authenticationFailed,
        );
      }
    } on PlatformException catch (e) {
      return BiometricAuthResult(
        success: false,
        error: e.message ?? 'Platform error occurred',
        errorType: _mapPlatformErrorToBiometricError(e.code),
        platformError: e,
      );
    } catch (e) {
      return BiometricAuthResult(
        success: false,
        error: 'Unexpected error: $e',
        errorType: BiometricErrorType.unknown,
      );
    }
  }

  /// Enroll biometric template (for advanced biometric matching)
  Future<BiometricEnrollmentResult> enrollBiometricTemplate({
    required String userId,
    required BiometricType biometricType,
    String? templateName,
  }) async {
    try {
      // First authenticate to ensure user consent
      final authResult = await authenticateWithBiometrics(
        reason: 'Please authenticate to enroll your biometric template',
      );

      if (!authResult.success) {
        return BiometricEnrollmentResult(
          success: false,
          error: 'Authentication required for enrollment',
        );
      }

      // Generate biometric template (simulated)
      final template = await _generateBiometricTemplate(biometricType);
      
      // Encrypt and store template
      final encryptedTemplate = await _encryptBiometricTemplate(template);
      final templateId = _generateTemplateId(userId, biometricType);
      
      _biometricTemplates[templateId] = template;
      _encryptedBiometricData[templateId] = encryptedTemplate;

      return BiometricEnrollmentResult(
        success: true,
        templateId: templateId,
        biometricType: biometricType,
        enrollmentDate: DateTime.now(),
        templateName: templateName ?? 'Template_${biometricType.name}',
      );
    } catch (e) {
      return BiometricEnrollmentResult(
        success: false,
        error: 'Enrollment failed: $e',
      );
    }
  }

  /// Verify biometric template
  Future<BiometricVerificationResult> verifyBiometricTemplate({
    required String templateId,
    required BiometricType biometricType,
  }) async {
    try {
      // Check if template exists
      if (!_biometricTemplates.containsKey(templateId)) {
        return BiometricVerificationResult(
          success: false,
          error: 'Biometric template not found',
          confidence: 0.0,
        );
      }

      // Authenticate with device biometrics
      final authResult = await authenticateWithBiometrics(
        reason: 'Please authenticate for biometric verification',
      );

      if (!authResult.success) {
        return BiometricVerificationResult(
          success: false,
          error: 'Device authentication failed',
          confidence: 0.0,
        );
      }

      // Generate current biometric sample
      final currentTemplate = await _generateBiometricTemplate(biometricType);
      
      // Compare templates
      final storedTemplate = _biometricTemplates[templateId]!;
      final matchResult = await _compareBiometricTemplates(
        storedTemplate,
        currentTemplate,
      );

      return BiometricVerificationResult(
        success: matchResult.isMatch,
        confidence: matchResult.confidence,
        templateId: templateId,
        verificationDate: DateTime.now(),
        matchScore: matchResult.score,
      );
    } catch (e) {
      return BiometricVerificationResult(
        success: false,
        error: 'Verification failed: $e',
        confidence: 0.0,
      );
    }
  }

  /// Get biometric security level
  Future<BiometricSecurityLevel> getBiometricSecurityLevel() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (_availableBiometrics.isEmpty) {
        return BiometricSecurityLevel.none;
      }

      // Determine security level based on available biometrics
      if (_availableBiometrics.contains(BiometricType.face) &&
          _availableBiometrics.contains(BiometricType.fingerprint)) {
        return BiometricSecurityLevel.high;
      } else if (_availableBiometrics.contains(BiometricType.face) ||
                 _availableBiometrics.contains(BiometricType.fingerprint)) {
        return BiometricSecurityLevel.medium;
      } else if (_availableBiometrics.contains(BiometricType.weak)) {
        return BiometricSecurityLevel.low;
      }

      return BiometricSecurityLevel.medium;
    } catch (e) {
      print('Failed to get biometric security level: $e');
      return BiometricSecurityLevel.none;
    }
  }

  /// Enable continuous biometric monitoring
  Future<void> enableContinuousMonitoring({
    required Function(BiometricMonitoringEvent) onEvent,
    Duration interval = const Duration(minutes: 5),
  }) async {
    Timer.periodic(interval, (timer) async {
      try {
        // Check if user is still present
        final isPresent = await _checkUserPresence();
        
        if (!isPresent) {
          onEvent(BiometricMonitoringEvent(
            type: BiometricEventType.userAbsent,
            timestamp: DateTime.now(),
            confidence: 0.0,
          ));
        } else {
          onEvent(BiometricMonitoringEvent(
            type: BiometricEventType.userPresent,
            timestamp: DateTime.now(),
            confidence: 0.95,
          ));
        }
      } catch (e) {
        onEvent(BiometricMonitoringEvent(
          type: BiometricEventType.error,
          timestamp: DateTime.now(),
          error: e.toString(),
        ));
      }
    });
  }

  /// Generate biometric-based encryption key
  Future<String> generateBiometricEncryptionKey({
    required String userId,
    required BiometricType biometricType,
  }) async {
    try {
      // Authenticate first
      final authResult = await authenticateWithBiometrics(
        reason: 'Authenticate to generate encryption key',
      );

      if (!authResult.success) {
        throw Exception('Authentication required for key generation');
      }

      // Generate biometric template
      final template = await _generateBiometricTemplate(biometricType);
      
      // Create deterministic key from biometric data
      final keyMaterial = template.features.join('');
      final keyBytes = sha256.convert(utf8.encode(keyMaterial + userId)).bytes;
      
      return base64Encode(keyBytes);
    } catch (e) {
      throw Exception('Failed to generate biometric encryption key: $e');
    }
  }

  /// Secure biometric data storage
  Future<void> secureStoreBiometricData({
    required String key,
    required String data,
    required String userId,
  }) async {
    try {
      // Generate encryption key from biometrics
      final encryptionKey = await generateBiometricEncryptionKey(
        userId: userId,
        biometricType: BiometricType.fingerprint,
      );

      // Encrypt data
      final encryptedData = await _encryptData(data, encryptionKey);
      
      // Store encrypted data
      _encryptedBiometricData[key] = encryptedData;
    } catch (e) {
      throw Exception('Failed to securely store biometric data: $e');
    }
  }

  /// Retrieve securely stored biometric data
  Future<String?> secureRetrieveBiometricData({
    required String key,
    required String userId,
  }) async {
    try {
      // Check if data exists
      if (!_encryptedBiometricData.containsKey(key)) {
        return null;
      }

      // Generate decryption key from biometrics
      final decryptionKey = await generateBiometricEncryptionKey(
        userId: userId,
        biometricType: BiometricType.fingerprint,
      );

      // Decrypt data
      final encryptedData = _encryptedBiometricData[key]!;
      final decryptedData = await _decryptData(encryptedData, decryptionKey);
      
      return decryptedData;
    } catch (e) {
      print('Failed to retrieve biometric data: $e');
      return null;
    }
  }

  /// Anti-spoofing detection
  Future<AntiSpoofingResult> detectSpoofing({
    required BiometricType biometricType,
  }) async {
    try {
      // Simulate liveness detection
      final livenessScore = await _performLivenessDetection(biometricType);
      
      // Check for presentation attacks
      final presentationAttackScore = await _detectPresentationAttack(biometricType);
      
      // Calculate overall spoofing confidence
      final spoofingConfidence = (livenessScore + presentationAttackScore) / 2;
      
      return AntiSpoofingResult(
        isLive: livenessScore > 0.8,
        isPresentationAttack: presentationAttackScore < 0.3,
        livenessScore: livenessScore,
        presentationAttackScore: presentationAttackScore,
        overallConfidence: spoofingConfidence,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      return AntiSpoofingResult(
        isLive: false,
        isPresentationAttack: true,
        livenessScore: 0.0,
        presentationAttackScore: 1.0,
        overallConfidence: 0.0,
        timestamp: DateTime.now(),
        error: e.toString(),
      );
    }
  }

  // Private helper methods

  Future<String> _generateBiometricSessionToken() async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomBytes = List.generate(16, (i) => timestamp % 256);
    final tokenData = base64Encode(randomBytes);
    return 'bio_session_$tokenData';
  }

  BiometricErrorType _mapPlatformErrorToBiometricError(String errorCode) {
    switch (errorCode) {
      case 'NotAvailable':
        return BiometricErrorType.notAvailable;
      case 'NotEnrolled':
        return BiometricErrorType.notEnrolled;
      case 'PasscodeNotSet':
        return BiometricErrorType.passcodeNotSet;
      case 'LockedOut':
        return BiometricErrorType.lockedOut;
      case 'PermanentlyLockedOut':
        return BiometricErrorType.permanentlyLockedOut;
      default:
        return BiometricErrorType.unknown;
    }
  }

  Future<BiometricTemplate> _generateBiometricTemplate(BiometricType type) async {
    // Simulate biometric template generation
    final features = List.generate(128, (i) => (i * 0.1) % 1.0);
    
    return BiometricTemplate(
      id: 'template_${DateTime.now().millisecondsSinceEpoch}',
      type: type,
      features: features,
      quality: 0.95,
      createdAt: DateTime.now(),
    );
  }

  Future<String> _encryptBiometricTemplate(BiometricTemplate template) async {
    final templateJson = jsonEncode(template.toJson());
    final key = List.generate(32, (i) => i % 256);
    return await _encryptData(templateJson, base64Encode(key));
  }

  String _generateTemplateId(String userId, BiometricType type) {
    final combined = '$userId:${type.name}:${DateTime.now().millisecondsSinceEpoch}';
    return sha256.convert(utf8.encode(combined)).toString();
  }

  Future<BiometricMatchResult> _compareBiometricTemplates(
    BiometricTemplate template1,
    BiometricTemplate template2,
  ) async {
    // Simulate template comparison
    double similarity = 0.0;
    
    for (int i = 0; i < template1.features.length && i < template2.features.length; i++) {
      final diff = (template1.features[i] - template2.features[i]).abs();
      similarity += (1.0 - diff);
    }
    
    similarity /= template1.features.length;
    
    return BiometricMatchResult(
      isMatch: similarity > 0.8,
      confidence: similarity,
      score: (similarity * 100).round(),
    );
  }

  Future<bool> _checkUserPresence() async {
    // Simulate user presence detection
    return true; // In real implementation, this would use camera/sensors
  }

  Future<double> _performLivenessDetection(BiometricType type) async {
    // Simulate liveness detection
    return 0.95; // High liveness score
  }

  Future<double> _detectPresentationAttack(BiometricType type) async {
    // Simulate presentation attack detection
    return 0.1; // Low presentation attack score (good)
  }

  Future<String> _encryptData(String data, String key) async {
    // Simple encryption simulation
    final keyBytes = base64Decode(key);
    final dataBytes = utf8.encode(data);
    final encrypted = List.generate(dataBytes.length, (i) => 
        dataBytes[i] ^ keyBytes[i % keyBytes.length]);
    return base64Encode(encrypted);
  }

  Future<String> _decryptData(String encryptedData, String key) async {
    // Simple decryption simulation
    final keyBytes = base64Decode(key);
    final encryptedBytes = base64Decode(encryptedData);
    final decrypted = List.generate(encryptedBytes.length, (i) => 
        encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
    return utf8.decode(decrypted);
  }

  /// Dispose biometric service
  void dispose() {
    _biometricTemplates.clear();
    _encryptedBiometricData.clear();
    _isInitialized = false;
  }
}

// Data Models

class BiometricAuthResult {
  final bool success;
  final String? error;
  final BiometricErrorType? errorType;
  final String? sessionToken;
  final List<BiometricType>? authenticatedBiometrics;
  final DateTime? timestamp;
  final PlatformException? platformError;

  BiometricAuthResult({
    required this.success,
    this.error,
    this.errorType,
    this.sessionToken,
    this.authenticatedBiometrics,
    this.timestamp,
    this.platformError,
  });
}

class BiometricEnrollmentResult {
  final bool success;
  final String? error;
  final String? templateId;
  final BiometricType? biometricType;
  final DateTime? enrollmentDate;
  final String? templateName;

  BiometricEnrollmentResult({
    required this.success,
    this.error,
    this.templateId,
    this.biometricType,
    this.enrollmentDate,
    this.templateName,
  });
}

class BiometricVerificationResult {
  final bool success;
  final String? error;
  final double confidence;
  final String? templateId;
  final DateTime? verificationDate;
  final int? matchScore;

  BiometricVerificationResult({
    required this.success,
    this.error,
    required this.confidence,
    this.templateId,
    this.verificationDate,
    this.matchScore,
  });
}

class BiometricTemplate {
  final String id;
  final BiometricType type;
  final List<double> features;
  final double quality;
  final DateTime createdAt;

  BiometricTemplate({
    required this.id,
    required this.type,
    required this.features,
    required this.quality,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type.name,
    'features': features,
    'quality': quality,
    'createdAt': createdAt.toIso8601String(),
  };
}

class BiometricMatchResult {
  final bool isMatch;
  final double confidence;
  final int score;

  BiometricMatchResult({
    required this.isMatch,
    required this.confidence,
    required this.score,
  });
}

class BiometricMonitoringEvent {
  final BiometricEventType type;
  final DateTime timestamp;
  final double? confidence;
  final String? error;

  BiometricMonitoringEvent({
    required this.type,
    required this.timestamp,
    this.confidence,
    this.error,
  });
}

class AntiSpoofingResult {
  final bool isLive;
  final bool isPresentationAttack;
  final double livenessScore;
  final double presentationAttackScore;
  final double overallConfidence;
  final DateTime timestamp;
  final String? error;

  AntiSpoofingResult({
    required this.isLive,
    required this.isPresentationAttack,
    required this.livenessScore,
    required this.presentationAttackScore,
    required this.overallConfidence,
    required this.timestamp,
    this.error,
  });
}

enum BiometricErrorType {
  notAvailable,
  notEnrolled,
  passcodeNotSet,
  lockedOut,
  permanentlyLockedOut,
  authenticationFailed,
  unknown,
}

enum BiometricSecurityLevel {
  none,
  low,
  medium,
  high,
}

enum BiometricEventType {
  userPresent,
  userAbsent,
  authenticationSuccess,
  authenticationFailure,
  spoofingDetected,
  error,
}
