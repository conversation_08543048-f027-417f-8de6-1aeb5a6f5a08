# 🚀 **دليل التشغيل المبسط - مشروع "حلاق على بابك"**

---

## ⚠️ **المتطلبات غير مثبتة - نحتاج لتثبيتها أولاً**

### 📥 **تحميل وتثبيت المتطلبات (خطوة واحدة):**

#### 🔧 **XAMPP (الكل في واحد)**
- **الرابط**: https://www.apachefriends.org/download.html
- **اختر**: XAMPP for Windows (PHP 8.1+)
- **يحتوي على**: PHP + MySQL + Apache + phpMyAdmin

#### 🟢 **Node.js**
- **الرابط**: https://nodejs.org/
- **اختر**: LTS Version (الأخضر)

---

## 🔧 **خطوات التثبيت السريعة:**

### 1️⃣ **تثبيت XAMPP:**
1. **حمل** XAMPP من الرابط أعلاه
2. **شغل** الملف كمدير (Run as Administrator)
3. **اتبع** خطوات التثبيت (Next, Next, Install)
4. **شغل** XAMPP Control Panel
5. **اضغط Start** بجانب Apache و MySQL

### 2️⃣ **تثبيت Node.js:**
1. **حمل** Node.js من الرابط أعلاه
2. **شغل** الملف وتابع التثبيت
3. **تأكد** من تفعيل "Add to PATH"

### 3️⃣ **تثبيت Composer:**
1. **افتح** Command Prompt كمدير
2. **اكتب**:
```bash
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
php composer-setup.php
php -r "unlink('composer-setup.php');"
move composer.phar C:\xampp\php\composer.phar
echo @php "%~dp0composer.phar" %*>C:\xampp\php\composer.bat
```

---

## 🗄️ **إعداد قاعدة البيانات:**

### 1️⃣ **تشغيل XAMPP:**
- **افتح** XAMPP Control Panel
- **اضغط Start** بجانب Apache
- **اضغط Start** بجانب MySQL

### 2️⃣ **إنشاء قاعدة البيانات:**
1. **اذهب إلى**: http://localhost/phpmyadmin
2. **اضغط "New"** في الجانب الأيسر
3. **اكتب اسم**: `barber_app`
4. **اختر Collation**: `utf8mb4_unicode_ci`
5. **اضغط "Create"**

---

## ✅ **التحقق من التثبيت:**

**افتح Command Prompt جديد واكتب:**

```bash
php --version
node --version
npm --version
composer --version
```

**إذا ظهرت أرقام الإصدارات، فالتثبيت نجح! 🎉**

---

## 🚀 **تشغيل المشروع:**

### 📂 **افتح Command Prompt في مجلد المشروع واكتب:**

```bash
# تشغيل السكريبت التلقائي
start.bat
```

**أو التشغيل اليدوي:**

```bash
# 1. تشغيل Backend
cd backend
composer install
php artisan key:generate
php artisan migrate --seed
php artisan serve --port=8000

# 2. في Command Prompt جديد - تشغيل Admin Dashboard
cd admin-dashboard
npm install
npm run dev
```

---

## 🌐 **الوصول للنظام:**

- **🖥️ لوحة الإدارة**: http://localhost:3000
- **📡 Backend API**: http://localhost:8000

### 🔐 **تسجيل الدخول:**
- **البريد**: <EMAIL>
- **كلمة المرور**: admin123

---

## 🆘 **إذا واجهت مشاكل:**

### ❌ **خطأ: "php is not recognized"**
- تأكد من تثبيت XAMPP
- أضف مسار PHP للـ PATH: `C:\xampp\php`

### ❌ **خطأ: "node is not recognized"**
- تأكد من تثبيت Node.js
- أعد تشغيل Command Prompt

### ❌ **خطأ: "composer is not recognized"**
- اتبع خطوات تثبيت Composer أعلاه
- أو حمل Composer-Setup.exe من موقعهم

### ❌ **خطأ: "Database connection failed"**
- تأكد من تشغيل MySQL في XAMPP
- تأكد من إنشاء قاعدة البيانات `barber_app`

---

## 📞 **للمساعدة:**

**إذا احتجت مساعدة، أخبرني في أي خطوة توقفت وسأساعدك! 🤝**

---

## 🎯 **الهدف:**

**رؤية لوحة الإدارة تعمل على http://localhost:3000 🎉**

**لنبدأ! 🚀**
