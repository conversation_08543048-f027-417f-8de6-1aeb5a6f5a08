<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingsService
{
    /**
     * Get all settings grouped by category
     */
    public function getAllSettings()
    {
        return Setting::all()->groupBy('category');
    }

    /**
     * Get settings for admin dashboard
     */
    public function getAdminSettings()
    {
        return Setting::select('key', 'value', 'type', 'category', 'description')
            ->orderBy('category')
            ->orderBy('key')
            ->get()
            ->groupBy('category');
    }

    /**
     * Update multiple settings
     */
    public function updateSettings(array $settings)
    {
        $updated = [];
        
        foreach ($settings as $key => $value) {
            $setting = Setting::where('key', $key)->first();
            
            if ($setting) {
                $setting->update(['value' => $value]);
                $updated[$key] = $setting->value;
            }
        }

        // Clear all cache
        Setting::clearCache();
        
        return $updated;
    }

    /**
     * Get feature flags for mobile apps
     */
    public function getFeatureFlags()
    {
        $features = [
            'registration_enabled',
            'booking_enabled',
            'vip_enabled',
            'online_payment_enabled',
            'group_booking_enabled',
            'rating_system_enabled',
            'store_enabled',
            'loyalty_points_enabled',
            'van_vip_enabled',
            'monthly_vip_enabled',
            'live_streaming_enabled'
        ];

        $flags = [];
        foreach ($features as $feature) {
            $flags[$feature] = Setting::get($feature, false);
        }

        return $flags;
    }

    /**
     * Get app configuration for mobile apps
     */
    public function getAppConfig()
    {
        return [
            'app_name' => Setting::get('app_name', 'حلاق على بابك'),
            'app_name_en' => Setting::get('app_name_en', 'Barber at Your Door'),
            'app_version' => Setting::get('app_version', '1.0.0'),
            'app_logo' => Setting::get('app_logo', '/images/logo.png'),
            'support_phone' => Setting::get('support_phone', '+201234567890'),
            'support_email' => Setting::get('support_email', '<EMAIL>'),
            'currency' => Setting::get('default_currency', 'EGP'),
            'currency_symbol' => Setting::get('currency_symbol', 'ج.م'),
            'maintenance_mode' => Setting::get('maintenance_mode', false),
            'maintenance_message' => Setting::get('maintenance_message', 'التطبيق تحت الصيانة، سنعود قريباً'),
        ];
    }

    /**
     * Get booking configuration
     */
    public function getBookingConfig()
    {
        return [
            'booking_advance_hours' => Setting::get('booking_advance_hours', 2),
            'booking_cancel_hours' => Setting::get('booking_cancel_hours', 1),
            'max_daily_bookings_per_barber' => Setting::get('max_daily_bookings_per_barber', 8),
            'default_service_duration' => Setting::get('default_service_duration', 30),
            'auto_assign_barber' => Setting::get('auto_assign_barber', true),
        ];
    }

    /**
     * Get financial configuration
     */
    public function getFinancialConfig()
    {
        return [
            'default_commission_rate' => Setting::get('default_commission_rate', 15.00),
            'vip_commission_rate' => Setting::get('vip_commission_rate', 10.00),
            'min_withdrawal_amount' => Setting::get('min_withdrawal_amount', 100.00),
            'withdrawal_fee' => Setting::get('withdrawal_fee', 5.00),
            'default_currency' => Setting::get('default_currency', 'EGP'),
        ];
    }

    /**
     * Get VIP configuration
     */
    public function getVipConfig()
    {
        return [
            'vip_enabled' => Setting::get('vip_enabled', true),
            'vip_monthly_price' => Setting::get('vip_monthly_price', 299.00),
            'vip_yearly_price' => Setting::get('vip_yearly_price', 2999.00),
            'vip_discount_percentage' => Setting::get('vip_discount_percentage', 20.00),
            'vip_free_bookings_monthly' => Setting::get('vip_free_bookings_monthly', 2),
            'max_vip_barbers' => Setting::get('max_vip_barbers', 50),
        ];
    }

    /**
     * Get payment configuration
     */
    public function getPaymentConfig()
    {
        return [
            'online_payment_enabled' => Setting::get('online_payment_enabled', true),
            'paymob_enabled' => Setting::get('paymob_enabled', true),
            'stripe_enabled' => Setting::get('stripe_enabled', false),
            'cash_payment_enabled' => Setting::get('cash_payment_enabled', true),
            'wallet_payment_enabled' => Setting::get('wallet_payment_enabled', true),
        ];
    }

    /**
     * Get loyalty points configuration
     */
    public function getLoyaltyConfig()
    {
        return [
            'loyalty_points_enabled' => Setting::get('loyalty_points_enabled', true),
            'points_per_booking' => Setting::get('points_per_booking', 10),
            'points_per_egp' => Setting::get('points_per_egp', 1),
            'points_redemption_rate' => Setting::get('points_redemption_rate', 100),
            'min_points_redemption' => Setting::get('min_points_redemption', 500),
        ];
    }

    /**
     * Get notification configuration
     */
    public function getNotificationConfig()
    {
        return [
            'push_notifications_enabled' => Setting::get('push_notifications_enabled', true),
            'sms_notifications_enabled' => Setting::get('sms_notifications_enabled', true),
            'email_notifications_enabled' => Setting::get('email_notifications_enabled', true),
            'booking_reminder_hours' => Setting::get('booking_reminder_hours', 2),
        ];
    }

    /**
     * Check if system is in maintenance mode
     */
    public function isMaintenanceMode()
    {
        return Setting::get('maintenance_mode', false);
    }

    /**
     * Enable/disable feature
     */
    public function toggleFeature($feature, $enabled = null)
    {
        $key = $feature . '_enabled';
        $currentValue = Setting::get($key, false);
        
        $newValue = $enabled !== null ? $enabled : !$currentValue;
        
        Setting::set($key, $newValue, 'boolean', 'features');
        
        return $newValue;
    }

    /**
     * Reset settings to default values
     */
    public function resetToDefaults()
    {
        // This would typically run the default settings seeder
        // For now, we'll just clear cache
        Setting::clearCache();
        
        return true;
    }

    /**
     * Export settings as JSON
     */
    public function exportSettings()
    {
        return Setting::all()->map(function ($setting) {
            return [
                'key' => $setting->key,
                'value' => $setting->value,
                'type' => $setting->type,
                'category' => $setting->category,
                'description' => $setting->description,
                'is_public' => $setting->is_public,
            ];
        });
    }

    /**
     * Import settings from array
     */
    public function importSettings(array $settings)
    {
        $imported = 0;
        
        foreach ($settings as $settingData) {
            if (isset($settingData['key'])) {
                Setting::updateOrCreate(
                    ['key' => $settingData['key']],
                    $settingData
                );
                $imported++;
            }
        }

        Setting::clearCache();
        
        return $imported;
    }
}
