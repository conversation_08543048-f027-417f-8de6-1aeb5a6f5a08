import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

class VoiceAIService {
  static final VoiceAIService _instance = VoiceAIService._internal();
  factory VoiceAIService() => _instance;
  VoiceAIService._internal();

  // Speech Recognition
  final SpeechToText _speechToText = SpeechToText();
  bool _speechEnabled = false;
  String _lastWords = '';

  // Text to Speech
  final FlutterTts _flutterTts = FlutterTts();
  bool _ttsEnabled = false;

  // Voice AI Configuration
  String? _voiceApiKey;
  String? _voiceApiUrl;
  String _selectedLanguage = 'ar-EG'; // Arabic Egypt
  String _selectedVoice = 'female';
  double _speechRate = 0.5;
  double _speechPitch = 1.0;
  double _speechVolume = 0.8;

  // Voice Commands
  final Map<String, VoiceCommand> _voiceCommands = {};
  final List<String> _conversationHistory = [];
  bool _isListening = false;
  bool _isSpeaking = false;

  // AI Context
  String _currentContext = 'general';
  Map<String, dynamic> _userPreferences = {};
  List<String> _recentBookings = [];

  /// Initialize Voice AI Service
  Future<bool> initialize() async {
    try {
      // Request microphone permission
      final micPermission = await Permission.microphone.request();
      if (micPermission != PermissionStatus.granted) {
        print('Microphone permission denied');
        return false;
      }

      // Initialize Speech to Text
      _speechEnabled = await _speechToText.initialize(
        onError: _onSpeechError,
        onStatus: _onSpeechStatus,
      );

      // Initialize Text to Speech
      await _initializeTTS();

      // Load voice commands
      _loadVoiceCommands();

      // Load user preferences
      await _loadUserPreferences();

      print('Voice AI Service initialized successfully');
      return true;
    } catch (e) {
      print('Voice AI Service initialization failed: $e');
      return false;
    }
  }

  /// Initialize Text to Speech
  Future<void> _initializeTTS() async {
    try {
      _ttsEnabled = true;

      // Configure TTS settings
      await _flutterTts.setLanguage(_selectedLanguage);
      await _flutterTts.setSpeechRate(_speechRate);
      await _flutterTts.setPitch(_speechPitch);
      await _flutterTts.setVolume(_speechVolume);

      // Set voice if available
      if (Platform.isIOS) {
        await _flutterTts.setSharedInstance(true);
        await _flutterTts.setIosAudioCategory(
          IosTextToSpeechAudioCategory.playback,
          [IosTextToSpeechAudioCategoryOptions.allowBluetooth],
        );
      }

      // Set completion handlers
      _flutterTts.setCompletionHandler(() {
        _isSpeaking = false;
      });

      _flutterTts.setErrorHandler((message) {
        print('TTS Error: $message');
        _isSpeaking = false;
      });

    } catch (e) {
      print('TTS initialization failed: $e');
      _ttsEnabled = false;
    }
  }

  /// Load predefined voice commands
  void _loadVoiceCommands() {
    _voiceCommands.addAll({
      // Booking Commands
      'احجز حلاق': VoiceCommand(
        pattern: r'احجز|حجز|أريد حجز',
        action: VoiceAction.bookBarber,
        response: 'سأساعدك في حجز حلاق. ما نوع الخدمة التي تريدها؟',
        context: 'booking',
      ),
      'book barber': VoiceCommand(
        pattern: r'book|booking|schedule',
        action: VoiceAction.bookBarber,
        response: 'I\'ll help you book a barber. What service do you need?',
        context: 'booking',
      ),

      // Search Commands
      'ابحث عن حلاق': VoiceCommand(
        pattern: r'ابحث|بحث|أبحث عن',
        action: VoiceAction.searchBarber,
        response: 'ما المنطقة التي تريد البحث فيها؟',
        context: 'search',
      ),
      'find barber': VoiceCommand(
        pattern: r'find|search|look for',
        action: VoiceAction.searchBarber,
        response: 'Which area would you like to search in?',
        context: 'search',
      ),

      // Profile Commands
      'ملفي الشخصي': VoiceCommand(
        pattern: r'ملف|ملفي|الملف الشخصي',
        action: VoiceAction.openProfile,
        response: 'سأفتح ملفك الشخصي',
        context: 'profile',
      ),
      'my profile': VoiceCommand(
        pattern: r'profile|my profile|account',
        action: VoiceAction.openProfile,
        response: 'Opening your profile',
        context: 'profile',
      ),

      // History Commands
      'تاريخ الحجوزات': VoiceCommand(
        pattern: r'تاريخ|سجل|الحجوزات السابقة',
        action: VoiceAction.showHistory,
        response: 'إليك تاريخ حجوزاتك',
        context: 'history',
      ),
      'booking history': VoiceCommand(
        pattern: r'history|past bookings|previous',
        action: VoiceAction.showHistory,
        response: 'Here\'s your booking history',
        context: 'history',
      ),

      // Help Commands
      'مساعدة': VoiceCommand(
        pattern: r'مساعدة|ساعدني|كيف',
        action: VoiceAction.showHelp,
        response: 'كيف يمكنني مساعدتك؟',
        context: 'help',
      ),
      'help': VoiceCommand(
        pattern: r'help|assist|how to',
        action: VoiceAction.showHelp,
        response: 'How can I help you?',
        context: 'help',
      ),

      // Cancel Commands
      'إلغاء': VoiceCommand(
        pattern: r'إلغاء|توقف|كفى',
        action: VoiceAction.cancel,
        response: 'تم الإلغاء',
        context: 'general',
      ),
      'cancel': VoiceCommand(
        pattern: r'cancel|stop|nevermind',
        action: VoiceAction.cancel,
        response: 'Cancelled',
        context: 'general',
      ),
    });
  }

  /// Start listening for voice commands
  Future<void> startListening() async {
    if (!_speechEnabled || _isListening) return;

    try {
      _isListening = true;
      await _speechToText.listen(
        onResult: _onSpeechResult,
        listenFor: Duration(seconds: 30),
        pauseFor: Duration(seconds: 3),
        partialResults: true,
        localeId: _selectedLanguage,
        cancelOnError: true,
        listenMode: ListenMode.confirmation,
      );
    } catch (e) {
      print('Error starting speech recognition: $e');
      _isListening = false;
    }
  }

  /// Stop listening
  Future<void> stopListening() async {
    if (_speechToText.isListening) {
      await _speechToText.stop();
    }
    _isListening = false;
  }

  /// Process speech result
  void _onSpeechResult(result) {
    _lastWords = result.recognizedWords;
    
    if (result.finalResult) {
      _processVoiceCommand(_lastWords);
      _conversationHistory.add('User: $_lastWords');
    }
  }

  /// Process voice command
  Future<void> _processVoiceCommand(String command) async {
    try {
      // Find matching command
      VoiceCommand? matchedCommand = _findMatchingCommand(command);
      
      if (matchedCommand != null) {
        // Execute command action
        await _executeVoiceAction(matchedCommand.action, command);
        
        // Speak response
        await speak(matchedCommand.response);
        
        // Update context
        _currentContext = matchedCommand.context;
        
        _conversationHistory.add('AI: ${matchedCommand.response}');
      } else {
        // Use AI to understand and respond
        await _processWithAI(command);
      }
    } catch (e) {
      print('Error processing voice command: $e');
      await speak('عذراً، حدث خطأ في معالجة الطلب');
    }
  }

  /// Find matching voice command
  VoiceCommand? _findMatchingCommand(String input) {
    for (final command in _voiceCommands.values) {
      final regex = RegExp(command.pattern, caseSensitive: false);
      if (regex.hasMatch(input)) {
        return command;
      }
    }
    return null;
  }

  /// Execute voice action
  Future<void> _executeVoiceAction(VoiceAction action, String input) async {
    switch (action) {
      case VoiceAction.bookBarber:
        await _handleBookingRequest(input);
        break;
      case VoiceAction.searchBarber:
        await _handleSearchRequest(input);
        break;
      case VoiceAction.openProfile:
        await _handleProfileRequest();
        break;
      case VoiceAction.showHistory:
        await _handleHistoryRequest();
        break;
      case VoiceAction.showHelp:
        await _handleHelpRequest();
        break;
      case VoiceAction.cancel:
        await _handleCancelRequest();
        break;
    }
  }

  /// Process with AI when no command matches
  Future<void> _processWithAI(String input) async {
    try {
      final aiResponse = await _getAIResponse(input);
      await speak(aiResponse);
      _conversationHistory.add('AI: $aiResponse');
    } catch (e) {
      await speak('عذراً، لم أفهم طلبك. يمكنك المحاولة مرة أخرى');
    }
  }

  /// Get AI response from server
  Future<String> _getAIResponse(String input) async {
    try {
      final response = await http.post(
        Uri.parse('${_voiceApiUrl}/ai/chat'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_voiceApiKey',
        },
        body: jsonEncode({
          'message': input,
          'context': _currentContext,
          'language': _selectedLanguage,
          'user_preferences': _userPreferences,
          'conversation_history': _conversationHistory.take(10).toList(),
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['response'] ?? 'عذراً، لم أستطع فهم طلبك';
      } else {
        throw Exception('AI API error: ${response.statusCode}');
      }
    } catch (e) {
      print('AI response error: $e');
      return 'عذراً، حدث خطأ في الخدمة';
    }
  }

  /// Speak text using TTS
  Future<void> speak(String text) async {
    if (!_ttsEnabled || _isSpeaking) return;

    try {
      _isSpeaking = true;
      await _flutterTts.speak(text);
    } catch (e) {
      print('TTS error: $e');
      _isSpeaking = false;
    }
  }

  /// Stop speaking
  Future<void> stopSpeaking() async {
    if (_isSpeaking) {
      await _flutterTts.stop();
      _isSpeaking = false;
    }
  }

  /// Handle booking request
  Future<void> _handleBookingRequest(String input) async {
    // Extract service type from input
    String serviceType = _extractServiceType(input);
    
    // Navigate to booking screen or show service selection
    // This would integrate with your app's navigation system
    print('Handling booking request for: $serviceType');
  }

  /// Handle search request
  Future<void> _handleSearchRequest(String input) async {
    // Extract location from input
    String location = _extractLocation(input);
    
    // Navigate to search screen with location
    print('Handling search request for location: $location');
  }

  /// Handle profile request
  Future<void> _handleProfileRequest() async {
    // Navigate to profile screen
    print('Opening user profile');
  }

  /// Handle history request
  Future<void> _handleHistoryRequest() async {
    // Navigate to booking history
    print('Showing booking history');
  }

  /// Handle help request
  Future<void> _handleHelpRequest() async {
    await speak('يمكنك قول "احجز حلاق" للحجز، أو "ابحث عن حلاق" للبحث، أو "ملفي الشخصي" لفتح ملفك');
  }

  /// Handle cancel request
  Future<void> _handleCancelRequest() async {
    await stopListening();
    await stopSpeaking();
    _currentContext = 'general';
  }

  /// Extract service type from voice input
  String _extractServiceType(String input) {
    final serviceKeywords = {
      'حلاقة': 'haircut',
      'حلق': 'shave',
      'لحية': 'beard',
      'شعر': 'hair',
      'غسيل': 'wash',
    };

    for (final keyword in serviceKeywords.keys) {
      if (input.contains(keyword)) {
        return serviceKeywords[keyword]!;
      }
    }

    return 'general';
  }

  /// Extract location from voice input
  String _extractLocation(String input) {
    final locationKeywords = [
      'القاهرة', 'الإسكندرية', 'الجيزة', 'المنصورة', 'أسوان',
      'cairo', 'alexandria', 'giza', 'mansoura', 'aswan'
    ];

    for (final location in locationKeywords) {
      if (input.toLowerCase().contains(location.toLowerCase())) {
        return location;
      }
    }

    return 'current';
  }

  /// Load user preferences
  Future<void> _loadUserPreferences() async {
    // Load from local storage or API
    _userPreferences = {
      'preferred_language': _selectedLanguage,
      'preferred_voice': _selectedVoice,
      'speech_rate': _speechRate,
      'favorite_services': ['haircut', 'beard'],
      'preferred_locations': ['القاهرة'],
    };
  }

  /// Configure voice settings
  Future<void> configureVoice({
    String? language,
    String? voice,
    double? speechRate,
    double? pitch,
    double? volume,
  }) async {
    if (language != null) {
      _selectedLanguage = language;
      await _flutterTts.setLanguage(language);
    }

    if (speechRate != null) {
      _speechRate = speechRate;
      await _flutterTts.setSpeechRate(speechRate);
    }

    if (pitch != null) {
      _speechPitch = pitch;
      await _flutterTts.setPitch(pitch);
    }

    if (volume != null) {
      _speechVolume = volume;
      await _flutterTts.setVolume(volume);
    }
  }

  /// Get available voices
  Future<List<dynamic>> getAvailableVoices() async {
    try {
      return await _flutterTts.getVoices;
    } catch (e) {
      print('Error getting voices: $e');
      return [];
    }
  }

  /// Get available languages
  Future<List<dynamic>> getAvailableLanguages() async {
    try {
      return await _flutterTts.getLanguages;
    } catch (e) {
      print('Error getting languages: $e');
      return [];
    }
  }

  /// Add custom voice command
  void addCustomCommand(String key, VoiceCommand command) {
    _voiceCommands[key] = command;
  }

  /// Remove voice command
  void removeCommand(String key) {
    _voiceCommands.remove(key);
  }

  /// Get conversation history
  List<String> getConversationHistory() {
    return List.from(_conversationHistory);
  }

  /// Clear conversation history
  void clearConversationHistory() {
    _conversationHistory.clear();
  }

  /// Check if currently listening
  bool get isListening => _isListening;

  /// Check if currently speaking
  bool get isSpeaking => _isSpeaking;

  /// Check if speech recognition is enabled
  bool get speechEnabled => _speechEnabled;

  /// Check if TTS is enabled
  bool get ttsEnabled => _ttsEnabled;

  /// Get last recognized words
  String get lastWords => _lastWords;

  /// Speech error handler
  void _onSpeechError(error) {
    print('Speech recognition error: $error');
    _isListening = false;
  }

  /// Speech status handler
  void _onSpeechStatus(status) {
    print('Speech recognition status: $status');
    if (status == 'done' || status == 'notListening') {
      _isListening = false;
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    await stopListening();
    await stopSpeaking();
    _conversationHistory.clear();
    _voiceCommands.clear();
  }
}

// Data Models

class VoiceCommand {
  final String pattern;
  final VoiceAction action;
  final String response;
  final String context;
  final Map<String, dynamic>? parameters;

  VoiceCommand({
    required this.pattern,
    required this.action,
    required this.response,
    required this.context,
    this.parameters,
  });
}

enum VoiceAction {
  bookBarber,
  searchBarber,
  openProfile,
  showHistory,
  showHelp,
  cancel,
  navigate,
  call,
  message,
}

class VoiceResponse {
  final String text;
  final VoiceAction? action;
  final Map<String, dynamic>? data;
  final String context;

  VoiceResponse({
    required this.text,
    this.action,
    this.data,
    required this.context,
  });
}
