<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\Barber;
use App\Models\Booking;
use App\Models\Service;
use Carbon\Carbon;

class DataMigrationCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'data:migrate 
                            {source : Source type (csv|json|sql|api)}
                            {--file= : Source file path}
                            {--url= : Source API URL}
                            {--table= : Target table name}
                            {--mapping= : Field mapping JSON file}
                            {--batch-size=1000 : Batch size for processing}
                            {--dry-run : Run without making changes}
                            {--backup : Create backup before migration}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate data from various sources to the barber app database';

    protected $batchSize;
    protected $dryRun;
    protected $backup;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $source = $this->argument('source');
        $this->batchSize = $this->option('batch-size');
        $this->dryRun = $this->option('dry-run');
        $this->backup = $this->option('backup');

        $this->info("Starting data migration from {$source}...");

        if ($this->dryRun) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made');
        }

        if ($this->backup && !$this->dryRun) {
            $this->createBackup();
        }

        try {
            switch ($source) {
                case 'csv':
                    return $this->migrateCsv();
                case 'json':
                    return $this->migrateJson();
                case 'sql':
                    return $this->migrateSql();
                case 'api':
                    return $this->migrateApi();
                default:
                    $this->error("Unsupported source type: {$source}");
                    return 1;
            }
        } catch (\Exception $e) {
            $this->error("Migration failed: {$e->getMessage()}");
            Log::error('Data migration failed', [
                'source' => $source,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    protected function migrateCsv()
    {
        $file = $this->option('file');
        if (!$file || !Storage::exists($file)) {
            $this->error('CSV file not found or not specified');
            return 1;
        }

        $this->info("Reading CSV file: {$file}");
        
        $csvData = array_map('str_getcsv', file(Storage::path($file)));
        $headers = array_shift($csvData);
        
        $this->info("Found " . count($csvData) . " records with headers: " . implode(', ', $headers));

        $mapping = $this->getFieldMapping();
        $table = $this->option('table');

        if (!$table) {
            $table = $this->choice('Select target table:', [
                'users', 'barbers', 'bookings', 'services', 'reviews'
            ]);
        }

        $processed = 0;
        $errors = 0;
        $batches = array_chunk($csvData, $this->batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $this->info("Processing batch " . ($batchIndex + 1) . "/" . count($batches));
            
            $batchData = [];
            
            foreach ($batch as $row) {
                try {
                    $record = array_combine($headers, $row);
                    $mappedRecord = $this->mapFields($record, $mapping);
                    $validatedRecord = $this->validateRecord($mappedRecord, $table);
                    
                    if ($validatedRecord) {
                        $batchData[] = $validatedRecord;
                        $processed++;
                    }
                } catch (\Exception $e) {
                    $errors++;
                    $this->warn("Error processing record: {$e->getMessage()}");
                }
            }

            if (!$this->dryRun && !empty($batchData)) {
                DB::table($table)->insert($batchData);
            }

            $this->line("Batch processed: " . count($batchData) . " records");
        }

        $this->info("✅ CSV migration completed!");
        $this->info("Processed: {$processed} records");
        $this->info("Errors: {$errors} records");

        return 0;
    }

    protected function migrateJson()
    {
        $file = $this->option('file');
        if (!$file || !Storage::exists($file)) {
            $this->error('JSON file not found or not specified');
            return 1;
        }

        $this->info("Reading JSON file: {$file}");
        
        $jsonContent = Storage::get($file);
        $data = json_decode($jsonContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->error('Invalid JSON format: ' . json_last_error_msg());
            return 1;
        }

        $this->info("Found " . count($data) . " records");

        $mapping = $this->getFieldMapping();
        $table = $this->option('table');

        if (!$table) {
            $table = $this->choice('Select target table:', [
                'users', 'barbers', 'bookings', 'services', 'reviews'
            ]);
        }

        $processed = 0;
        $errors = 0;
        $batches = array_chunk($data, $this->batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $this->info("Processing batch " . ($batchIndex + 1) . "/" . count($batches));
            
            $batchData = [];
            
            foreach ($batch as $record) {
                try {
                    $mappedRecord = $this->mapFields($record, $mapping);
                    $validatedRecord = $this->validateRecord($mappedRecord, $table);
                    
                    if ($validatedRecord) {
                        $batchData[] = $validatedRecord;
                        $processed++;
                    }
                } catch (\Exception $e) {
                    $errors++;
                    $this->warn("Error processing record: {$e->getMessage()}");
                }
            }

            if (!$this->dryRun && !empty($batchData)) {
                DB::table($table)->insert($batchData);
            }

            $this->line("Batch processed: " . count($batchData) . " records");
        }

        $this->info("✅ JSON migration completed!");
        $this->info("Processed: {$processed} records");
        $this->info("Errors: {$errors} records");

        return 0;
    }

    protected function migrateSql()
    {
        $file = $this->option('file');
        if (!$file || !Storage::exists($file)) {
            $this->error('SQL file not found or not specified');
            return 1;
        }

        $this->info("Executing SQL file: {$file}");
        
        $sqlContent = Storage::get($file);
        $statements = array_filter(array_map('trim', explode(';', $sqlContent)));

        $this->info("Found " . count($statements) . " SQL statements");

        $executed = 0;
        $errors = 0;

        foreach ($statements as $statement) {
            if (empty($statement)) continue;

            try {
                if (!$this->dryRun) {
                    DB::statement($statement);
                }
                $executed++;
                $this->line("✅ Executed: " . substr($statement, 0, 50) . "...");
            } catch (\Exception $e) {
                $errors++;
                $this->error("❌ Failed: " . substr($statement, 0, 50) . "... - " . $e->getMessage());
            }
        }

        $this->info("✅ SQL migration completed!");
        $this->info("Executed: {$executed} statements");
        $this->info("Errors: {$errors} statements");

        return 0;
    }

    protected function migrateApi()
    {
        $url = $this->option('url');
        if (!$url) {
            $this->error('API URL not specified');
            return 1;
        }

        $this->info("Fetching data from API: {$url}");

        try {
            $response = file_get_contents($url);
            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error('Invalid JSON response: ' . json_last_error_msg());
                return 1;
            }

            // Handle paginated APIs
            $allData = [];
            if (isset($data['data']) && isset($data['pagination'])) {
                $allData = $data['data'];
                $currentPage = 1;
                $totalPages = $data['pagination']['total_pages'] ?? 1;

                while ($currentPage < $totalPages) {
                    $currentPage++;
                    $pageUrl = $url . (strpos($url, '?') ? '&' : '?') . "page={$currentPage}";
                    
                    $this->info("Fetching page {$currentPage}/{$totalPages}...");
                    
                    $pageResponse = file_get_contents($pageUrl);
                    $pageData = json_decode($pageResponse, true);
                    
                    if (isset($pageData['data'])) {
                        $allData = array_merge($allData, $pageData['data']);
                    }
                }
            } else {
                $allData = is_array($data) ? $data : [$data];
            }

            $this->info("Found " . count($allData) . " records from API");

            // Process the data similar to JSON migration
            $mapping = $this->getFieldMapping();
            $table = $this->option('table');

            if (!$table) {
                $table = $this->choice('Select target table:', [
                    'users', 'barbers', 'bookings', 'services', 'reviews'
                ]);
            }

            $processed = 0;
            $errors = 0;
            $batches = array_chunk($allData, $this->batchSize);

            foreach ($batches as $batchIndex => $batch) {
                $this->info("Processing batch " . ($batchIndex + 1) . "/" . count($batches));
                
                $batchData = [];
                
                foreach ($batch as $record) {
                    try {
                        $mappedRecord = $this->mapFields($record, $mapping);
                        $validatedRecord = $this->validateRecord($mappedRecord, $table);
                        
                        if ($validatedRecord) {
                            $batchData[] = $validatedRecord;
                            $processed++;
                        }
                    } catch (\Exception $e) {
                        $errors++;
                        $this->warn("Error processing record: {$e->getMessage()}");
                    }
                }

                if (!$this->dryRun && !empty($batchData)) {
                    DB::table($table)->insert($batchData);
                }

                $this->line("Batch processed: " . count($batchData) . " records");
            }

            $this->info("✅ API migration completed!");
            $this->info("Processed: {$processed} records");
            $this->info("Errors: {$errors} records");

            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to fetch data from API: {$e->getMessage()}");
            return 1;
        }
    }

    protected function getFieldMapping()
    {
        $mappingFile = $this->option('mapping');
        
        if ($mappingFile && Storage::exists($mappingFile)) {
            $mappingContent = Storage::get($mappingFile);
            return json_decode($mappingContent, true);
        }

        return [];
    }

    protected function mapFields($record, $mapping)
    {
        if (empty($mapping)) {
            return $record;
        }

        $mappedRecord = [];
        
        foreach ($mapping as $sourceField => $targetField) {
            if (isset($record[$sourceField])) {
                $mappedRecord[$targetField] = $record[$sourceField];
            }
        }

        return $mappedRecord;
    }

    protected function validateRecord($record, $table)
    {
        // Add timestamps if not present
        if (!isset($record['created_at'])) {
            $record['created_at'] = now();
        }
        if (!isset($record['updated_at'])) {
            $record['updated_at'] = now();
        }

        // Table-specific validations
        switch ($table) {
            case 'users':
                return $this->validateUser($record);
            case 'barbers':
                return $this->validateBarber($record);
            case 'bookings':
                return $this->validateBooking($record);
            case 'services':
                return $this->validateService($record);
            default:
                return $record;
        }
    }

    protected function validateUser($record)
    {
        // Required fields
        if (empty($record['name']) || empty($record['email'])) {
            throw new \Exception('Missing required fields: name or email');
        }

        // Validate email
        if (!filter_var($record['email'], FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('Invalid email format');
        }

        // Check for duplicates
        if (User::where('email', $record['email'])->exists()) {
            throw new \Exception('User with this email already exists');
        }

        // Set default password if not provided
        if (empty($record['password'])) {
            $record['password'] = bcrypt('password123');
        }

        return $record;
    }

    protected function validateBarber($record)
    {
        // Check if user exists
        if (!empty($record['user_id']) && !User::find($record['user_id'])) {
            throw new \Exception('User not found for barber');
        }

        // Set defaults
        $record['is_verified'] = $record['is_verified'] ?? false;
        $record['is_available'] = $record['is_available'] ?? true;
        $record['rating'] = $record['rating'] ?? 0;
        $record['total_reviews'] = $record['total_reviews'] ?? 0;
        $record['total_bookings'] = $record['total_bookings'] ?? 0;

        return $record;
    }

    protected function validateBooking($record)
    {
        // Validate required relationships
        if (!empty($record['customer_id']) && !User::find($record['customer_id'])) {
            throw new \Exception('Customer not found');
        }

        if (!empty($record['barber_id']) && !Barber::find($record['barber_id'])) {
            throw new \Exception('Barber not found');
        }

        // Validate dates
        if (!empty($record['scheduled_date'])) {
            try {
                Carbon::parse($record['scheduled_date']);
            } catch (\Exception $e) {
                throw new \Exception('Invalid scheduled date format');
            }
        }

        // Set defaults
        $record['status'] = $record['status'] ?? 'pending';
        $record['total_amount'] = $record['total_amount'] ?? 0;

        return $record;
    }

    protected function validateService($record)
    {
        // Required fields
        if (empty($record['name'])) {
            throw new \Exception('Service name is required');
        }

        // Set defaults
        $record['base_price'] = $record['base_price'] ?? 0;
        $record['duration_minutes'] = $record['duration_minutes'] ?? 30;
        $record['is_active'] = $record['is_active'] ?? true;

        return $record;
    }

    protected function createBackup()
    {
        $this->info('Creating database backup...');
        
        $backupFile = 'backups/migration_backup_' . date('Y_m_d_H_i_s') . '.sql';
        
        $command = sprintf(
            'mysqldump -h %s -u %s -p%s %s > %s',
            config('database.connections.mysql.host'),
            config('database.connections.mysql.username'),
            config('database.connections.mysql.password'),
            config('database.connections.mysql.database'),
            Storage::path($backupFile)
        );

        exec($command, $output, $returnCode);

        if ($returnCode === 0) {
            $this->info("✅ Backup created: {$backupFile}");
        } else {
            $this->error('❌ Backup failed');
            throw new \Exception('Database backup failed');
        }
    }
}
