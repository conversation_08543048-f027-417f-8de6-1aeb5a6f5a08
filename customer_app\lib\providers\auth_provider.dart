import 'package:flutter/foundation.dart';
import '../core/services/storage_service.dart';
import '../models/user_model.dart';

class AuthProvider extends ChangeNotifier {
  User? _user;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  User? get user => _user;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Check authentication status
  Future<void> checkAuthStatus() async {
    try {
      _setLoading(true);
      
      final token = StorageService.getString('auth_token');
      final userData = StorageService.getString('user_data');
      
      if (token != null && userData != null) {
        // Parse user data and validate token
        _user = User.fromJson(userData);
        _isAuthenticated = true;
      } else {
        _isAuthenticated = false;
        _user = null;
      }
    } catch (e) {
      _setError('خطأ في التحقق من حالة المصادقة');
      _isAuthenticated = false;
      _user = null;
    } finally {
      _setLoading(false);
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    try {
      _setLoading(true);
      _clearError();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock successful login
      if (email.isNotEmpty && password.isNotEmpty) {
        _user = User(
          id: '1',
          name: 'أحمد محمد',
          email: email,
          phone: '+966501234567',
          avatar: '',
          loyaltyLevel: 'gold',
          loyaltyPoints: 1250,
        );
        
        _isAuthenticated = true;
        
        // Save to storage
        await StorageService.setString('auth_token', 'mock_token_123');
        await StorageService.setString('user_data', _user!.toJson());
        
        return true;
      } else {
        _setError('البريد الإلكتروني وكلمة المرور مطلوبان');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تسجيل الدخول');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Register
  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String phone,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock successful registration
      _user = User(
        id: '1',
        name: name,
        email: email,
        phone: phone,
        avatar: '',
        loyaltyLevel: 'bronze',
        loyaltyPoints: 0,
      );
      
      _isAuthenticated = true;
      
      // Save to storage
      await StorageService.setString('auth_token', 'mock_token_123');
      await StorageService.setString('user_data', _user!.toJson());
      
      return true;
    } catch (e) {
      _setError('خطأ في إنشاء الحساب');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      _setLoading(true);
      
      // Clear storage
      await StorageService.remove('auth_token');
      await StorageService.remove('user_data');
      
      // Clear state
      _user = null;
      _isAuthenticated = false;
      _clearError();
    } catch (e) {
      _setError('خطأ في تسجيل الخروج');
    } finally {
      _setLoading(false);
    }
  }

  // Update profile
  Future<bool> updateProfile({
    String? name,
    String? phone,
    String? avatar,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      if (_user != null) {
        _user = _user!.copyWith(
          name: name ?? _user!.name,
          phone: phone ?? _user!.phone,
          avatar: avatar ?? _user!.avatar,
        );
        
        // Save to storage
        await StorageService.setString('user_data', _user!.toJson());
        
        return true;
      }
      return false;
    } catch (e) {
      _setError('خطأ في تحديث الملف الشخصي');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Add loyalty points
  void addLoyaltyPoints(int points) {
    if (_user != null) {
      _user = _user!.copyWith(
        loyaltyPoints: _user!.loyaltyPoints + points,
      );
      
      // Update loyalty level based on points
      String newLevel = 'bronze';
      if (_user!.loyaltyPoints >= 2000) {
        newLevel = 'platinum';
      } else if (_user!.loyaltyPoints >= 1000) {
        newLevel = 'gold';
      } else if (_user!.loyaltyPoints >= 500) {
        newLevel = 'silver';
      }
      
      if (newLevel != _user!.loyaltyLevel) {
        _user = _user!.copyWith(loyaltyLevel: newLevel);
      }
      
      // Save to storage
      StorageService.setString('user_data', _user!.toJson());
      notifyListeners();
    }
  }

  // Redeem loyalty points
  bool redeemLoyaltyPoints(int points) {
    if (_user != null && _user!.loyaltyPoints >= points) {
      _user = _user!.copyWith(
        loyaltyPoints: _user!.loyaltyPoints - points,
      );
      
      // Save to storage
      StorageService.setString('user_data', _user!.toJson());
      notifyListeners();
      return true;
    }
    return false;
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
