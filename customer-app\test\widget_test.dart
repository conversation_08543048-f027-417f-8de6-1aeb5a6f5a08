import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';

import 'package:customer_app/main.dart';
import 'package:customer_app/services/auth_service.dart';
import 'package:customer_app/services/booking_service.dart';
import 'package:customer_app/services/location_service.dart';
import 'package:customer_app/presentation/pages/auth/login_page.dart';
import 'package:customer_app/presentation/pages/home/<USER>';
import 'package:customer_app/presentation/pages/booking/booking_page.dart';

// Generate mocks
@GenerateMocks([AuthService, BookingService, LocationService])
import 'widget_test.mocks.dart';

void main() {
  group('Customer App Widget Tests', () {
    late MockAuthService mockAuthService;
    late MockBookingService mockBookingService;
    late MockLocationService mockLocationService;

    setUp(() {
      mockAuthService = MockAuthService();
      mockBookingService = MockBookingService();
      mockLocationService = MockLocationService();
    });

    testWidgets('App starts with login page when not authenticated', (WidgetTester tester) async {
      // Arrange
      when(mockAuthService.isAuthenticated).thenReturn(false);
      when(mockAuthService.authStateChanges).thenAnswer((_) => Stream.value(null));

      // Act
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            Provider<AuthService>.value(value: mockAuthService),
            Provider<BookingService>.value(value: mockBookingService),
            Provider<LocationService>.value(value: mockLocationService),
          ],
          child: const MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(LoginPage), findsOneWidget);
      expect(find.text('تسجيل الدخول'), findsOneWidget);
    });

    testWidgets('Login page has required form fields', (WidgetTester tester) async {
      // Arrange
      when(mockAuthService.isAuthenticated).thenReturn(false);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
            ],
            child: const LoginPage(),
          ),
        ),
      );

      // Assert
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email and password
      expect(find.text('البريد الإلكتروني'), findsOneWidget);
      expect(find.text('كلمة المرور'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('Login form validates empty fields', (WidgetTester tester) async {
      // Arrange
      when(mockAuthService.isAuthenticated).thenReturn(false);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
            ],
            child: const LoginPage(),
          ),
        ),
      );

      // Tap login button without entering data
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Assert
      expect(find.text('يرجى إدخال البريد الإلكتروني'), findsOneWidget);
      expect(find.text('يرجى إدخال كلمة المرور'), findsOneWidget);
    });

    testWidgets('Successful login navigates to home page', (WidgetTester tester) async {
      // Arrange
      when(mockAuthService.isAuthenticated).thenReturn(false);
      when(mockAuthService.login(any, any)).thenAnswer((_) async => {
        'success': true,
        'user': {'id': 1, 'name': 'Test User', 'email': '<EMAIL>'}
      });

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
            ],
            child: const LoginPage(),
          ),
          routes: {
            '/home': (context) => const HomePage(),
          },
        ),
      );

      // Enter valid credentials
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).last, 'password123');
      
      // Tap login button
      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();

      // Assert
      verify(mockAuthService.login('<EMAIL>', 'password123')).called(1);
    });

    testWidgets('Home page displays welcome message', (WidgetTester tester) async {
      // Arrange
      when(mockAuthService.isAuthenticated).thenReturn(true);
      when(mockAuthService.currentUser).thenReturn({
        'id': 1,
        'name': 'Test User',
        'email': '<EMAIL>'
      });

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
              Provider<BookingService>.value(value: mockBookingService),
              Provider<LocationService>.value(value: mockLocationService),
            ],
            child: const HomePage(),
          ),
        ),
      );

      // Assert
      expect(find.text('مرحباً Test User'), findsOneWidget);
      expect(find.text('ابحث عن حلاق'), findsOneWidget);
    });

    testWidgets('Home page shows service categories', (WidgetTester tester) async {
      // Arrange
      when(mockAuthService.isAuthenticated).thenReturn(true);
      when(mockAuthService.currentUser).thenReturn({
        'id': 1,
        'name': 'Test User'
      });

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
              Provider<BookingService>.value(value: mockBookingService),
              Provider<LocationService>.value(value: mockLocationService),
            ],
            child: const HomePage(),
          ),
        ),
      );

      // Assert
      expect(find.text('قص الشعر'), findsOneWidget);
      expect(find.text('الحلاقة'), findsOneWidget);
      expect(find.text('العناية بالشعر'), findsOneWidget);
    });

    testWidgets('Booking page displays barber information', (WidgetTester tester) async {
      // Arrange
      final mockBarber = {
        'id': 1,
        'user': {'name': 'أحمد محمد'},
        'rating': 4.8,
        'total_reviews': 150,
        'experience_years': 5,
        'services': [
          {'id': 1, 'name': 'قص شعر عادي', 'price': 50.0},
          {'id': 2, 'name': 'حلاقة ذقن', 'price': 30.0},
        ]
      };

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
              Provider<BookingService>.value(value: mockBookingService),
            ],
            child: BookingPage(barber: mockBarber),
          ),
        ),
      );

      // Assert
      expect(find.text('أحمد محمد'), findsOneWidget);
      expect(find.text('4.8'), findsOneWidget);
      expect(find.text('150 تقييم'), findsOneWidget);
      expect(find.text('5 سنوات خبرة'), findsOneWidget);
    });

    testWidgets('Booking page shows available services', (WidgetTester tester) async {
      // Arrange
      final mockBarber = {
        'id': 1,
        'user': {'name': 'أحمد محمد'},
        'services': [
          {'id': 1, 'name': 'قص شعر عادي', 'price': 50.0},
          {'id': 2, 'name': 'حلاقة ذقن', 'price': 30.0},
        ]
      };

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
              Provider<BookingService>.value(value: mockBookingService),
            ],
            child: BookingPage(barber: mockBarber),
          ),
        ),
      );

      // Assert
      expect(find.text('قص شعر عادي'), findsOneWidget);
      expect(find.text('50.0 ج.م'), findsOneWidget);
      expect(find.text('حلاقة ذقن'), findsOneWidget);
      expect(find.text('30.0 ج.م'), findsOneWidget);
    });

    testWidgets('Service selection updates total price', (WidgetTester tester) async {
      // Arrange
      final mockBarber = {
        'id': 1,
        'user': {'name': 'أحمد محمد'},
        'services': [
          {'id': 1, 'name': 'قص شعر عادي', 'price': 50.0},
          {'id': 2, 'name': 'حلاقة ذقن', 'price': 30.0},
        ]
      };

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
              Provider<BookingService>.value(value: mockBookingService),
            ],
            child: BookingPage(barber: mockBarber),
          ),
        ),
      );

      // Select first service
      await tester.tap(find.byType(Checkbox).first);
      await tester.pump();

      // Assert
      expect(find.text('المجموع: 50.0 ج.م'), findsOneWidget);

      // Select second service
      await tester.tap(find.byType(Checkbox).last);
      await tester.pump();

      // Assert
      expect(find.text('المجموع: 80.0 ج.م'), findsOneWidget);
    });

    testWidgets('Date picker shows available dates', (WidgetTester tester) async {
      // Arrange
      final mockBarber = {
        'id': 1,
        'user': {'name': 'أحمد محمد'},
        'services': [
          {'id': 1, 'name': 'قص شعر عادي', 'price': 50.0},
        ]
      };

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
              Provider<BookingService>.value(value: mockBookingService),
            ],
            child: BookingPage(barber: mockBarber),
          ),
        ),
      );

      // Tap date picker
      await tester.tap(find.text('اختر التاريخ'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(DatePickerDialog), findsOneWidget);
    });

    testWidgets('Time slots are displayed after date selection', (WidgetTester tester) async {
      // Arrange
      final mockBarber = {
        'id': 1,
        'user': {'name': 'أحمد محمد'},
        'services': [
          {'id': 1, 'name': 'قص شعر عادي', 'price': 50.0},
        ]
      };

      when(mockBookingService.getAvailableTimeSlots(any, any))
          .thenAnswer((_) async => ['09:00', '10:00', '11:00', '14:00', '15:00']);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
              Provider<BookingService>.value(value: mockBookingService),
            ],
            child: BookingPage(barber: mockBarber),
          ),
        ),
      );

      // Select a date
      await tester.tap(find.text('اختر التاريخ'));
      await tester.pumpAndSettle();
      
      // Select today's date (assuming it's available)
      await tester.tap(find.text('OK'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('09:00'), findsOneWidget);
      expect(find.text('10:00'), findsOneWidget);
      expect(find.text('11:00'), findsOneWidget);
    });

    testWidgets('Booking confirmation shows all details', (WidgetTester tester) async {
      // Arrange
      final mockBarber = {
        'id': 1,
        'user': {'name': 'أحمد محمد'},
        'services': [
          {'id': 1, 'name': 'قص شعر عادي', 'price': 50.0},
        ]
      };

      when(mockBookingService.createBooking(any)).thenAnswer((_) async => {
        'success': true,
        'booking': {
          'id': 1,
          'booking_number': 'BK001',
          'total_amount': 50.0,
          'scheduled_date': '2024-01-15',
          'scheduled_time': '10:00'
        }
      });

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
              Provider<BookingService>.value(value: mockBookingService),
            ],
            child: BookingPage(barber: mockBarber),
          ),
        ),
      );

      // Complete booking flow
      await tester.tap(find.byType(Checkbox).first); // Select service
      await tester.pump();

      await tester.tap(find.text('اختر التاريخ'));
      await tester.pumpAndSettle();
      await tester.tap(find.text('OK'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('10:00')); // Select time
      await tester.pump();

      await tester.tap(find.text('تأكيد الحجز'));
      await tester.pumpAndSettle();

      // Assert
      verify(mockBookingService.createBooking(any)).called(1);
    });

    testWidgets('Error message is shown when booking fails', (WidgetTester tester) async {
      // Arrange
      final mockBarber = {
        'id': 1,
        'user': {'name': 'أحمد محمد'},
        'services': [
          {'id': 1, 'name': 'قص شعر عادي', 'price': 50.0},
        ]
      };

      when(mockBookingService.createBooking(any)).thenAnswer((_) async => {
        'success': false,
        'message': 'هذا الموعد غير متاح'
      });

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              Provider<AuthService>.value(value: mockAuthService),
              Provider<BookingService>.value(value: mockBookingService),
            ],
            child: BookingPage(barber: mockBarber),
          ),
        ),
      );

      // Complete booking flow
      await tester.tap(find.byType(Checkbox).first);
      await tester.pump();

      await tester.tap(find.text('تأكيد الحجز'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('هذا الموعد غير متاح'), findsOneWidget);
    });
  });

  group('Integration Tests', () {
    testWidgets('Complete booking flow works end-to-end', (WidgetTester tester) async {
      // This would be a more comprehensive test that goes through
      // the entire booking process from login to confirmation
      
      // Arrange
      final mockAuthService = MockAuthService();
      final mockBookingService = MockBookingService();
      final mockLocationService = MockLocationService();

      when(mockAuthService.isAuthenticated).thenReturn(true);
      when(mockAuthService.currentUser).thenReturn({
        'id': 1,
        'name': 'Test User'
      });

      // Act & Assert
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            Provider<AuthService>.value(value: mockAuthService),
            Provider<BookingService>.value(value: mockBookingService),
            Provider<LocationService>.value(value: mockLocationService),
          ],
          child: const MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Verify home page is shown
      expect(find.byType(HomePage), findsOneWidget);
      
      // This test would continue with navigation through the app
      // and verification of the complete user journey
    });
  });
}
