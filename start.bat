@echo off
chcp 65001 >nul
title مشروع حلاق على بابك - تشغيل سريع

echo.
echo ========================================
echo 🚀 مرحباً بك في مشروع "حلاق على بابك"
echo ========================================
echo.

echo 📋 التحقق من المتطلبات...

:: التحقق من PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP غير مثبت. يرجى تثبيت PHP 8.1 أو أحدث
    pause
    exit /b 1
)
echo ✅ PHP مثبت

:: التحقق من Composer
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Composer غير مثبت. يرجى تثبيت Composer
    pause
    exit /b 1
)
echo ✅ Composer مثبت

:: التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js 18 أو أحدث
    pause
    exit /b 1
)
echo ✅ Node.js مثبت

echo.
echo 🏗️ إعداد Backend (Laravel)...
echo.

:: الانتقال إلى مجلد Backend
if not exist "backend" (
    echo ❌ مجلد backend غير موجود
    pause
    exit /b 1
)

cd backend

:: التحقق من وجود ملف .env
if not exist ".env" (
    echo 📝 إنشاء ملف .env...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
    ) else (
        echo ❌ ملف .env.example غير موجود
        pause
        exit /b 1
    )
)

:: تثبيت تبعيات Laravel
echo 📦 تثبيت تبعيات Laravel...
composer install --no-dev --optimize-autoloader

:: توليد مفتاح التطبيق
echo 🔑 توليد مفتاح التطبيق...
php artisan key:generate --force

:: إنشاء رابط التخزين
echo 🔗 إنشاء رابط التخزين...
php artisan storage:link

:: تشغيل خادم Laravel في الخلفية
echo 🚀 تشغيل خادم Laravel...
start "Laravel Server" cmd /k "php artisan serve --port=8000"

:: العودة للمجلد الرئيسي
cd ..

echo.
echo 🖥️ إعداد لوحة الإدارة...
echo.

:: التحقق من مجلد لوحة الإدارة
if not exist "admin-dashboard" (
    echo ❌ مجلد admin-dashboard غير موجود
    pause
    exit /b 1
)

cd admin-dashboard

:: التحقق من وجود ملف .env
if not exist ".env" (
    echo 📝 إنشاء ملف .env للوحة الإدارة...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
    )
)

:: تثبيت تبعيات Node.js
echo 📦 تثبيت تبعيات لوحة الإدارة...
npm install

:: تشغيل خادم التطوير في الخلفية
echo 🚀 تشغيل لوحة الإدارة...
start "Admin Dashboard" cmd /k "npm run dev"

:: العودة للمجلد الرئيسي
cd ..

echo.
echo ✅ تم تشغيل جميع الخدمات بنجاح!
echo.
echo 🌐 الروابط المتاحة:
echo ========================================
echo 📡 Backend API: http://localhost:8000
echo 🖥️ لوحة الإدارة: http://localhost:3000
echo 📚 توثيق API: http://localhost:8000/api/documentation
echo ========================================
echo.
echo 🔐 بيانات تسجيل الدخول للوحة الإدارة:
echo البريد الإلكتروني: <EMAIL>
echo كلمة المرور: admin123
echo.
echo 💡 نصائح:
echo - تأكد من تشغيل MySQL قبل استخدام النظام
echo - قم بإنشاء قاعدة بيانات باسم 'barber_app'
echo - راجع ملف QUICK_START_GUIDE.md للمزيد من التفاصيل
echo.

:: انتظار لمدة 5 ثوانٍ ثم فتح المتصفح
timeout /t 5 /nobreak >nul

:: فتح لوحة الإدارة في المتصفح
start http://localhost:3000

echo 🎉 مبروك! مشروع "حلاق على بابك" جاهز للاستخدام
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
