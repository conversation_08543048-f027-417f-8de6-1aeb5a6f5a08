# مشروع "حلاق على بابك" - Barber at Your Door

## 🏗️ نظرة عامة على المشروع

مشروع متكامل لخدمة الحلاقة المنزلية مع نظام ديناميكي شامل يتضمن:

- **تطبيق المستخدمين** (Flutter): حجز حلاق لزيارة المنزل
- **تطبيق الحلاقين** (Flutter): استقبال الطلبات والتنقل
- **لوحة التحكم** (Laravel + Vue.js): إدارة كاملة ديناميكية
- **API الخلفي** (Laravel): خدمات REST API
- **قاعدة البيانات** (MySQL): قابلة للتوسّع

## 🧩 المزايا الديناميكية

### ✨ النظام الديناميكي الكامل
- تفعيل/تعطيل أي ميزة من لوحة التحكم
- إدارة الصلاحيات ديناميكياً
- تحكم في الإعدادات بدون مبرمج
- نظام مدن ومحافظات قابل للتوسع

### 🎯 المزايا المتقدمة
- **نظام VIP**: اشتراكات وخدمات مميزة
- **ذكاء اصطناعي**: ترشيح الحلاق الأنسب
- **بث مباشر**: معاينة الخدمة بالفيديو
- **صالونات متنقلة**: خدمة Van VIP
- **نظام الولاء**: نقاط ومكافآت
- **متجر متكامل**: منتجات العناية

## 📁 هيكل المشروع

```
flutter_module_2/
├── backend/                    # Laravel API
│   ├── app/
│   ├── database/
│   ├── routes/
│   └── config/
├── admin-dashboard/           # لوحة التحكم (Vue.js)
├── customer-app/             # تطبيق المستخدمين (Flutter)
├── barber-app/              # تطبيق الحلاقين (Flutter)
├── database/                # تصميم قاعدة البيانات
│   ├── ERD/
│   └── migrations/
├── docs/                    # التوثيق
└── deployment/             # ملفات النشر
```

## 🚀 التقنيات المستخدمة

- **Backend**: Laravel 10+ (PHP 8.1+)
- **Frontend Admin**: Vue.js 3 + Vuetify
- **Mobile Apps**: Flutter 3.x
- **Database**: MySQL 8.0
- **Maps**: Google Maps API
- **Payments**: Paymob, Stripe
- **Notifications**: Firebase Cloud Messaging
- **Real-time**: Laravel WebSockets

## 🔧 متطلبات التشغيل

- PHP 8.1+
- Composer
- Node.js 16+
- Flutter SDK 3.x
- MySQL 8.0
- Redis (للكاش والجلسات)

## 📋 خطة التطوير

### المرحلة الأولى: الأساسيات ✅
- [x] إعداد هيكل المشروع
- [ ] تصميم قاعدة البيانات الديناميكية
- [ ] إعداد Laravel API
- [ ] لوحة التحكم الديناميكية

### المرحلة الثانية: التطبيقات
- [ ] تطبيق Flutter للمستخدمين
- [ ] تطبيق Flutter للحلاقين

### المرحلة الثالثة: المزايا المتقدمة
- [ ] نظام الدفع الإلكتروني
- [ ] نظام الإشعارات
- [ ] المزايا الذكية (AI, VIP, etc.)

## 🎯 الهدف

إنشاء منصة شاملة وديناميكية لخدمة الحلاقة المنزلية تتميز بـ:
- سهولة الإدارة والتحكم
- قابلية التوسع والتطوير
- تجربة مستخدم متميزة
- نظام مالي متكامل
- مزايا تنافسية فريدة

---

**تم إنشاؤه بواسطة**: Augment Agent
**التاريخ**: 2025-07-17
