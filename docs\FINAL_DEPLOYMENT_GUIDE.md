# 🚀 دليل النشر النهائي - مشروع "حلاق على بابك"

## 📋 نظرة عامة

هذا الدليل يوضح كيفية نشر مشروع "حلاق على بابك" بالكامل في بيئة الإنتاج مع جميع المكونات والخدمات.

## 🏗️ متطلبات النشر

### 🖥️ متطلبات الخادم
- **CPU**: 8 cores minimum (16 cores recommended)
- **RAM**: 16GB minimum (32GB recommended)
- **Storage**: 500GB SSD minimum (1TB recommended)
- **Network**: 1Gbps connection
- **OS**: Ubuntu 20.04 LTS or CentOS 8

### 🔧 البرامج المطلوبة
```bash
# Docker & Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Kubernetes (اختياري)
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Git
sudo apt update
sudo apt install git -y
```

## 🚀 خطوات النشر

### 1️⃣ تحضير البيئة

```bash
# إنشاء مجلد المشروع
sudo mkdir -p /opt/barber-app
cd /opt/barber-app

# استنساخ المشروع
git clone https://github.com/your-repo/barber-app.git .

# إعداد الصلاحيات
sudo chown -R $USER:$USER /opt/barber-app
chmod +x deployment/scripts/*.sh
```

### 2️⃣ إعداد متغيرات البيئة

```bash
# نسخ ملفات البيئة
cp .env.production.example .env.production

# تحرير متغيرات البيئة
nano .env.production
```

#### متغيرات البيئة المطلوبة:
```env
# Application
APP_NAME="حلاق على بابك"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://api.barber-app.com
APP_KEY=base64:your-32-character-secret-key

# Database
DB_CONNECTION=mysql
DB_HOST=db
DB_PORT=3306
DB_DATABASE=barber_db
DB_USERNAME=barber_user
DB_PASSWORD=your-secure-database-password

# Redis
REDIS_HOST=redis
REDIS_PASSWORD=your-secure-redis-password
REDIS_PORT=6379

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls

# Payment Gateways
STRIPE_KEY=pk_live_your-stripe-public-key
STRIPE_SECRET=sk_live_your-stripe-secret-key
PAYMOB_API_KEY=your-paymob-api-key
PAYMOB_INTEGRATION_ID=your-integration-id

# Firebase
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id

# Google Maps
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Agora (Live Streaming)
AGORA_APP_ID=your-agora-app-id
AGORA_APP_CERTIFICATE=your-agora-app-certificate

# AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=barber-app-storage

# Monitoring
GRAFANA_PASSWORD=your-secure-grafana-password
PROMETHEUS_PASSWORD=your-secure-prometheus-password
```

### 3️⃣ إعداد SSL Certificates

```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-nginx -y

# الحصول على شهادات SSL
sudo certbot certonly --standalone -d api.barber-app.com
sudo certbot certonly --standalone -d admin.barber-app.com
sudo certbot certonly --standalone -d barber-app.com

# نسخ الشهادات
sudo cp /etc/letsencrypt/live/api.barber-app.com/fullchain.pem deployment/ssl/
sudo cp /etc/letsencrypt/live/api.barber-app.com/privkey.pem deployment/ssl/
```

### 4️⃣ بناء ونشر التطبيق

```bash
# تشغيل سكريبت النشر
./deployment/scripts/deploy.sh production latest

# أو النشر اليدوي
docker-compose -f deployment/docker-compose.production.yml up -d
```

### 5️⃣ إعداد قاعدة البيانات

```bash
# تشغيل الترحيلات
docker-compose -f deployment/docker-compose.production.yml exec app php artisan migrate --force

# إدراج البيانات الأولية
docker-compose -f deployment/docker-compose.production.yml exec app php artisan db:seed --force

# إنشاء مستخدم مدير
docker-compose -f deployment/docker-compose.production.yml exec app php artisan make:admin
```

### 6️⃣ إعداد المراقبة

```bash
# الوصول إلى Grafana
# URL: http://your-server-ip:3000
# Username: admin
# Password: your-grafana-password

# الوصول إلى Prometheus
# URL: http://your-server-ip:9090

# إعداد التنبيهات
docker-compose -f deployment/docker-compose.production.yml exec prometheus promtool check rules /etc/prometheus/alert_rules.yml
```

## 🔧 إعداد النطاقات (DNS)

### إعداد DNS Records:
```
A     api.barber-app.com      → YOUR_SERVER_IP
A     admin.barber-app.com    → YOUR_SERVER_IP
A     barber-app.com          → YOUR_SERVER_IP
CNAME www.barber-app.com      → barber-app.com
```

## 📱 نشر التطبيقات المحمولة

### Android (Google Play Store):

1. **بناء التطبيق:**
```bash
cd customer-app
flutter build appbundle --release

cd ../barber-app
flutter build appbundle --release
```

2. **رفع إلى Play Console:**
- اذهب إلى [Google Play Console](https://play.google.com/console)
- أنشئ تطبيق جديد
- ارفع ملف `.aab`
- املأ معلومات التطبيق
- أضف screenshots ووصف
- انشر للمراجعة

### iOS (App Store):

1. **بناء التطبيق:**
```bash
cd customer-app
flutter build ios --release

cd ../barber-app
flutter build ios --release
```

2. **رفع إلى App Store:**
- افتح Xcode
- اختر Product > Archive
- اختر Distribute App
- اختر App Store Connect
- ارفع التطبيق

## 🔒 إعدادات الأمان

### 1. Firewall Configuration:
```bash
# تفعيل UFW
sudo ufw enable

# السماح بالمنافذ المطلوبة
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 3306/tcp  # MySQL (internal only)
sudo ufw allow 6379/tcp  # Redis (internal only)

# حظر الوصول المباشر لقاعدة البيانات من الخارج
sudo ufw deny from any to any port 3306
sudo ufw deny from any to any port 6379
```

### 2. إعداد Fail2Ban:
```bash
# تثبيت Fail2Ban
sudo apt install fail2ban -y

# إعداد التكوين
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# تحرير الإعدادات
sudo nano /etc/fail2ban/jail.local

# إعادة تشغيل الخدمة
sudo systemctl restart fail2ban
sudo systemctl enable fail2ban
```

### 3. تحديث النظام:
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# إعداد التحديثات التلقائية
sudo apt install unattended-upgrades -y
sudo dpkg-reconfigure -plow unattended-upgrades
```

## 📊 مراقبة الأداء

### 1. مراقبة الخدمات:
```bash
# فحص حالة الخدمات
docker-compose -f deployment/docker-compose.production.yml ps

# مراقبة السجلات
docker-compose -f deployment/docker-compose.production.yml logs -f app

# فحص استخدام الموارد
docker stats
```

### 2. إعداد التنبيهات:
```bash
# إعداد Slack Webhook للتنبيهات
export SLACK_WEBHOOK_URL="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

# إعداد البريد الإلكتروني للتنبيهات
export NOTIFICATION_EMAIL="<EMAIL>"
```

## 🔄 النسخ الاحتياطي

### 1. نسخ احتياطي لقاعدة البيانات:
```bash
# إنشاء نسخة احتياطية يومية
cat > /etc/cron.daily/barber-app-backup << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/backups/barber-app"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# نسخ قاعدة البيانات
docker-compose -f /opt/barber-app/deployment/docker-compose.production.yml exec -T db mysqldump -u root -p$MYSQL_ROOT_PASSWORD barber_db > $BACKUP_DIR/database_$DATE.sql

# نسخ الملفات
tar -czf $BACKUP_DIR/storage_$DATE.tar.gz -C /opt/barber-app storage/

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
EOF

chmod +x /etc/cron.daily/barber-app-backup
```

### 2. نسخ احتياطي للسحابة:
```bash
# تثبيت AWS CLI
sudo apt install awscli -y

# إعداد AWS credentials
aws configure

# رفع النسخ الاحتياطية للسحابة
aws s3 sync /opt/backups/barber-app s3://barber-app-backups/
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **التطبيق لا يعمل:**
```bash
# فحص السجلات
docker-compose logs app

# إعادة تشغيل الخدمات
docker-compose restart app
```

2. **قاعدة البيانات لا تتصل:**
```bash
# فحص حالة MySQL
docker-compose exec db mysql -u root -p -e "SHOW DATABASES;"

# إعادة تشغيل قاعدة البيانات
docker-compose restart db
```

3. **مشاكل الذاكرة:**
```bash
# فحص استخدام الذاكرة
free -h
docker stats

# تنظيف Docker
docker system prune -a
```

## 📞 الدعم والصيانة

### جهات الاتصال:
- **الدعم الفني**: <EMAIL>
- **الطوارئ**: +20 123 456 7890
- **المطور**: <EMAIL>

### صيانة دورية:
- **يومياً**: فحص السجلات والتنبيهات
- **أسبوعياً**: فحص الأداء والنسخ الاحتياطية
- **شهرياً**: تحديث النظام والأمان
- **ربع سنوياً**: مراجعة شاملة للنظام

---

**🎉 تم نشر مشروع "حلاق على بابك" بنجاح!**

للمزيد من المساعدة، راجع الوثائق الفنية أو اتصل بفريق الدعم.
