import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:arcore_flutter_plugin/arcore_flutter_plugin.dart';
import 'package:camera/camera.dart';
import 'package:path_provider/path_provider.dart';
import 'package:vector_math/vector_math_64.dart' as vector;

class ARService {
  static final ARService _instance = ARService._internal();
  factory ARService() => _instance;
  ARService._internal();

  ArCoreController? _arCoreController;
  CameraController? _cameraController;
  bool _isARSupported = false;
  bool _isInitialized = false;

  // AR Features
  bool get isARSupported => _isARSupported;
  bool get isInitialized => _isInitialized;

  /// Initialize AR service
  Future<bool> initialize() async {
    try {
      // Check AR support
      _isARSupported = await ArCoreController.checkArCoreAvailability();
      
      if (!_isARSupported) {
        print('AR Core not supported on this device');
        return false;
      }

      // Initialize camera
      await _initializeCamera();
      
      _isInitialized = true;
      return true;
    } catch (e) {
      print('AR Service initialization failed: $e');
      return false;
    }
  }

  /// Initialize camera for AR
  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isNotEmpty) {
        _cameraController = CameraController(
          cameras.first,
          ResolutionPreset.high,
          enableAudio: false,
        );
        await _cameraController!.initialize();
      }
    } catch (e) {
      print('Camera initialization failed: $e');
    }
  }

  /// Create AR view for virtual haircut preview
  Widget createHaircutPreviewAR({
    required Function(ArCoreController) onArCoreViewCreated,
    required String haircutStyle,
    required Map<String, dynamic> customerFaceData,
  }) {
    return ArCoreView(
      onArCoreViewCreated: (ArCoreController controller) {
        _arCoreController = controller;
        _setupHaircutPreview(controller, haircutStyle, customerFaceData);
        onArCoreViewCreated(controller);
      },
      enableTapRecognizer: true,
      enablePlaneRenderer: false,
      enableUpdateListener: true,
    );
  }

  /// Setup virtual haircut preview
  Future<void> _setupHaircutPreview(
    ArCoreController controller,
    String haircutStyle,
    Map<String, dynamic> faceData,
  ) async {
    try {
      // Load 3D haircut model
      final haircutModel = await _loadHaircutModel(haircutStyle);
      
      // Position model based on face detection
      final position = _calculateHaircutPosition(faceData);
      
      // Create AR node for haircut
      final haircutNode = ArCoreNode(
        shape: haircutModel,
        position: position,
        rotation: vector.Vector4(0, 0, 0, 1),
        scale: vector.Vector3(1, 1, 1),
      );

      // Add to AR scene
      await controller.addArCoreNode(haircutNode);
      
      // Add face tracking anchor
      await _addFaceTrackingAnchor(controller, faceData);
      
    } catch (e) {
      print('Haircut preview setup failed: $e');
    }
  }

  /// Load 3D haircut model
  Future<ArCoreShape> _loadHaircutModel(String haircutStyle) async {
    try {
      // Load model from assets based on style
      final modelPath = 'assets/3d_models/haircuts/$haircutStyle.sfb';
      
      return ArCoreShape(
        materials: [
          ArCoreMaterial(
            color: Colors.brown,
            metallic: 0.2,
            roughness: 0.8,
          ),
        ],
        geometry: ArCoreBox(
          materials: [
            ArCoreMaterial(
              textureBytes: await _loadTextureBytes(haircutStyle),
            ),
          ],
          size: vector.Vector3(0.2, 0.2, 0.2),
        ),
      );
    } catch (e) {
      print('Failed to load haircut model: $e');
      // Return default model
      return ArCoreShape(
        materials: [ArCoreMaterial(color: Colors.brown)],
        geometry: ArCoreBox(size: vector.Vector3(0.1, 0.1, 0.1)),
      );
    }
  }

  /// Load texture bytes for haircut model
  Future<Uint8List> _loadTextureBytes(String haircutStyle) async {
    try {
      final ByteData textureData = await rootBundle.load(
        'assets/textures/haircuts/$haircutStyle.png'
      );
      return textureData.buffer.asUint8List();
    } catch (e) {
      print('Failed to load texture: $e');
      // Return default texture
      return Uint8List(0);
    }
  }

  /// Calculate haircut position based on face data
  vector.Vector3 _calculateHaircutPosition(Map<String, dynamic> faceData) {
    try {
      // Extract face landmarks
      final faceCenter = faceData['center'] as Map<String, double>?;
      final faceSize = faceData['size'] as Map<String, double>?;
      
      if (faceCenter != null && faceSize != null) {
        return vector.Vector3(
          faceCenter['x']! * 0.001, // Convert to meters
          (faceCenter['y']! + faceSize['height']! * 0.3) * 0.001,
          faceCenter['z']! * 0.001,
        );
      }
    } catch (e) {
      print('Failed to calculate position: $e');
    }
    
    // Default position
    return vector.Vector3(0, 0.1, -0.5);
  }

  /// Add face tracking anchor
  Future<void> _addFaceTrackingAnchor(
    ArCoreController controller,
    Map<String, dynamic> faceData,
  ) async {
    try {
      // Create face tracking anchor
      final anchor = ArCoreAnchor(
        nodeName: 'face_anchor',
        pose: ArCorePose(
          translation: vector.Vector3(0, 0, 0),
          rotation: vector.Vector4(0, 0, 0, 1),
        ),
      );

      await controller.addArCoreAnchor(anchor);
    } catch (e) {
      print('Failed to add face tracking anchor: $e');
    }
  }

  /// Create AR view for barber location visualization
  Widget createLocationVisualizationAR({
    required Function(ArCoreController) onArCoreViewCreated,
    required List<Map<String, dynamic>> nearbyBarbers,
    required Map<String, double> userLocation,
  }) {
    return ArCoreView(
      onArCoreViewCreated: (ArCoreController controller) {
        _arCoreController = controller;
        _setupLocationVisualization(controller, nearbyBarbers, userLocation);
        onArCoreViewCreated(controller);
      },
      enableTapRecognizer: true,
      enablePlaneRenderer: true,
    );
  }

  /// Setup location visualization
  Future<void> _setupLocationVisualization(
    ArCoreController controller,
    List<Map<String, dynamic>> barbers,
    Map<String, double> userLocation,
  ) async {
    try {
      for (int i = 0; i < barbers.length; i++) {
        final barber = barbers[i];
        final position = _calculateBarberPosition(barber, userLocation, i);
        
        // Create barber marker
        final barberNode = ArCoreNode(
          shape: await _createBarberMarker(barber),
          position: position,
          rotation: vector.Vector4(0, 0, 0, 1),
          scale: vector.Vector3(0.5, 0.5, 0.5),
        );

        await controller.addArCoreNode(barberNode);
        
        // Add info panel
        await _addBarberInfoPanel(controller, barber, position);
      }
    } catch (e) {
      print('Location visualization setup failed: $e');
    }
  }

  /// Calculate barber position in AR space
  vector.Vector3 _calculateBarberPosition(
    Map<String, dynamic> barber,
    Map<String, double> userLocation,
    int index,
  ) {
    try {
      final barberLat = barber['latitude'] as double;
      final barberLng = barber['longitude'] as double;
      final userLat = userLocation['latitude']!;
      final userLng = userLocation['longitude']!;
      
      // Convert lat/lng to relative position
      final deltaLat = (barberLat - userLat) * 111000; // meters
      final deltaLng = (barberLng - userLng) * 111000 * 
          math.cos(userLat * math.pi / 180);
      
      // Scale down for AR visualization
      final x = deltaLng * 0.001;
      final z = -deltaLat * 0.001; // Negative for correct orientation
      final y = 0.0;
      
      return vector.Vector3(x, y, z);
    } catch (e) {
      print('Failed to calculate barber position: $e');
      // Fallback to circular arrangement
      final angle = (index * 2 * math.pi) / 8; // Up to 8 barbers in circle
      return vector.Vector3(
        math.cos(angle) * 2.0,
        0.0,
        math.sin(angle) * 2.0,
      );
    }
  }

  /// Create barber marker shape
  Future<ArCoreShape> _createBarberMarker(Map<String, dynamic> barber) async {
    try {
      final rating = barber['rating'] as double? ?? 0.0;
      final color = _getRatingColor(rating);
      
      return ArCoreShape(
        materials: [
          ArCoreMaterial(
            color: color,
            metallic: 0.3,
            roughness: 0.7,
          ),
        ],
        geometry: ArCoreCylinder(
          topRadius: 0.1,
          bottomRadius: 0.1,
          height: 0.3,
        ),
      );
    } catch (e) {
      print('Failed to create barber marker: $e');
      return ArCoreShape(
        materials: [ArCoreMaterial(color: Colors.blue)],
        geometry: ArCoreCylinder(
          topRadius: 0.1,
          bottomRadius: 0.1,
          height: 0.3,
        ),
      );
    }
  }

  /// Get color based on rating
  Color _getRatingColor(double rating) {
    if (rating >= 4.5) return Colors.green;
    if (rating >= 4.0) return Colors.lightGreen;
    if (rating >= 3.5) return Colors.yellow;
    if (rating >= 3.0) return Colors.orange;
    return Colors.red;
  }

  /// Add barber info panel
  Future<void> _addBarberInfoPanel(
    ArCoreController controller,
    Map<String, dynamic> barber,
    vector.Vector3 position,
  ) async {
    try {
      final panelPosition = vector.Vector3(
        position.x,
        position.y + 0.4,
        position.z,
      );
      
      final infoPanel = ArCoreNode(
        shape: ArCoreShape(
          materials: [
            ArCoreMaterial(
              color: Colors.white.withOpacity(0.9),
            ),
          ],
          geometry: ArCorePlane(
            width: 0.3,
            height: 0.2,
          ),
        ),
        position: panelPosition,
        rotation: vector.Vector4(0, 0, 0, 1),
      );

      await controller.addArCoreNode(infoPanel);
    } catch (e) {
      print('Failed to add info panel: $e');
    }
  }

  /// Create AR view for service demonstration
  Widget createServiceDemonstrationAR({
    required Function(ArCoreController) onArCoreViewCreated,
    required String serviceType,
    required Map<String, dynamic> demoData,
  }) {
    return ArCoreView(
      onArCoreViewCreated: (ArCoreController controller) {
        _arCoreController = controller;
        _setupServiceDemo(controller, serviceType, demoData);
        onArCoreViewCreated(controller);
      },
      enableTapRecognizer: true,
      enablePlaneRenderer: true,
    );
  }

  /// Setup service demonstration
  Future<void> _setupServiceDemo(
    ArCoreController controller,
    String serviceType,
    Map<String, dynamic> demoData,
  ) async {
    try {
      switch (serviceType) {
        case 'haircut':
          await _setupHaircutDemo(controller, demoData);
          break;
        case 'beard_trim':
          await _setupBeardTrimDemo(controller, demoData);
          break;
        case 'hair_wash':
          await _setupHairWashDemo(controller, demoData);
          break;
        default:
          await _setupGenericDemo(controller, demoData);
      }
    } catch (e) {
      print('Service demo setup failed: $e');
    }
  }

  /// Setup haircut demonstration
  Future<void> _setupHaircutDemo(
    ArCoreController controller,
    Map<String, dynamic> demoData,
  ) async {
    // Create animated scissors
    final scissorsNode = ArCoreNode(
      shape: await _createScissorsModel(),
      position: vector.Vector3(0, 0.2, -0.5),
      rotation: vector.Vector4(0, 0, 0, 1),
    );

    await controller.addArCoreNode(scissorsNode);
    
    // Add cutting animation
    _animateScissors(controller, scissorsNode);
  }

  /// Setup beard trim demonstration
  Future<void> _setupBeardTrimDemo(
    ArCoreController controller,
    Map<String, dynamic> demoData,
  ) async {
    // Create trimmer model
    final trimmerNode = ArCoreNode(
      shape: await _createTrimmerModel(),
      position: vector.Vector3(0, 0.1, -0.4),
      rotation: vector.Vector4(0, 0, 0, 1),
    );

    await controller.addArCoreNode(trimmerNode);
  }

  /// Setup hair wash demonstration
  Future<void> _setupHairWashDemo(
    ArCoreController controller,
    Map<String, dynamic> demoData,
  ) async {
    // Create water effect
    final waterNode = ArCoreNode(
      shape: await _createWaterEffect(),
      position: vector.Vector3(0, 0.3, -0.6),
      rotation: vector.Vector4(0, 0, 0, 1),
    );

    await controller.addArCoreNode(waterNode);
  }

  /// Setup generic demonstration
  Future<void> _setupGenericDemo(
    ArCoreController controller,
    Map<String, dynamic> demoData,
  ) async {
    // Create generic service visualization
    final serviceNode = ArCoreNode(
      shape: ArCoreShape(
        materials: [ArCoreMaterial(color: Colors.blue)],
        geometry: ArCoreSphere(radius: 0.1),
      ),
      position: vector.Vector3(0, 0.2, -0.5),
      rotation: vector.Vector4(0, 0, 0, 1),
    );

    await controller.addArCoreNode(serviceNode);
  }

  /// Create scissors 3D model
  Future<ArCoreShape> _createScissorsModel() async {
    return ArCoreShape(
      materials: [
        ArCoreMaterial(
          color: Colors.grey[700]!,
          metallic: 0.8,
          roughness: 0.2,
        ),
      ],
      geometry: ArCoreBox(
        size: vector.Vector3(0.05, 0.15, 0.02),
      ),
    );
  }

  /// Create trimmer 3D model
  Future<ArCoreShape> _createTrimmerModel() async {
    return ArCoreShape(
      materials: [
        ArCoreMaterial(
          color: Colors.black,
          metallic: 0.5,
          roughness: 0.3,
        ),
      ],
      geometry: ArCoreCylinder(
        topRadius: 0.03,
        bottomRadius: 0.03,
        height: 0.12,
      ),
    );
  }

  /// Create water effect
  Future<ArCoreShape> _createWaterEffect() async {
    return ArCoreShape(
      materials: [
        ArCoreMaterial(
          color: Colors.blue.withOpacity(0.6),
          metallic: 0.1,
          roughness: 0.9,
        ),
      ],
      geometry: ArCoreSphere(radius: 0.08),
    );
  }

  /// Animate scissors cutting motion
  void _animateScissors(ArCoreController controller, ArCoreNode scissorsNode) {
    Timer.periodic(Duration(milliseconds: 500), (timer) {
      if (_arCoreController == null) {
        timer.cancel();
        return;
      }
      
      // Animate opening and closing
      final rotation = timer.tick % 2 == 0 
          ? vector.Vector4(0, 0, 0.2, 1)
          : vector.Vector4(0, 0, 0, 1);
      
      // Update node rotation (simplified)
      // In real implementation, you'd use proper animation APIs
    });
  }

  /// Take AR screenshot
  Future<String?> takeARScreenshot() async {
    try {
      if (_arCoreController == null) return null;
      
      // Get temporary directory
      final directory = await getTemporaryDirectory();
      final path = '${directory.path}/ar_screenshot_${DateTime.now().millisecondsSinceEpoch}.png';
      
      // Take screenshot (this would need platform-specific implementation)
      // For now, return a placeholder path
      return path;
    } catch (e) {
      print('Failed to take AR screenshot: $e');
      return null;
    }
  }

  /// Record AR video
  Future<String?> startARVideoRecording() async {
    try {
      if (_arCoreController == null) return null;
      
      // Get temporary directory
      final directory = await getTemporaryDirectory();
      final path = '${directory.path}/ar_video_${DateTime.now().millisecondsSinceEpoch}.mp4';
      
      // Start recording (platform-specific implementation needed)
      return path;
    } catch (e) {
      print('Failed to start AR video recording: $e');
      return null;
    }
  }

  /// Stop AR video recording
  Future<void> stopARVideoRecording() async {
    try {
      // Stop recording (platform-specific implementation needed)
    } catch (e) {
      print('Failed to stop AR video recording: $e');
    }
  }

  /// Detect face landmarks for AR overlay
  Future<Map<String, dynamic>?> detectFaceLandmarks() async {
    try {
      if (_cameraController == null || !_cameraController!.value.isInitialized) {
        return null;
      }

      // Take picture for face detection
      final image = await _cameraController!.takePicture();
      
      // Process image for face detection (would integrate with ML Kit or similar)
      // For now, return mock data
      return {
        'center': {'x': 0.0, 'y': 0.0, 'z': 0.0},
        'size': {'width': 100.0, 'height': 120.0},
        'landmarks': {
          'left_eye': {'x': -20.0, 'y': 10.0},
          'right_eye': {'x': 20.0, 'y': 10.0},
          'nose': {'x': 0.0, 'y': 0.0},
          'mouth': {'x': 0.0, 'y': -15.0},
        },
        'confidence': 0.95,
      };
    } catch (e) {
      print('Face detection failed: $e');
      return null;
    }
  }

  /// Cleanup AR resources
  Future<void> dispose() async {
    try {
      await _arCoreController?.dispose();
      await _cameraController?.dispose();
      _arCoreController = null;
      _cameraController = null;
      _isInitialized = false;
    } catch (e) {
      print('AR Service disposal failed: $e');
    }
  }

  /// Check if device supports specific AR features
  Future<Map<String, bool>> checkARCapabilities() async {
    try {
      return {
        'face_tracking': await _checkFaceTrackingSupport(),
        'plane_detection': await _checkPlaneDetectionSupport(),
        'light_estimation': await _checkLightEstimationSupport(),
        'occlusion': await _checkOcclusionSupport(),
        'persistent_anchors': await _checkPersistentAnchorsSupport(),
      };
    } catch (e) {
      print('AR capabilities check failed: $e');
      return {
        'face_tracking': false,
        'plane_detection': false,
        'light_estimation': false,
        'occlusion': false,
        'persistent_anchors': false,
      };
    }
  }

  Future<bool> _checkFaceTrackingSupport() async {
    // Platform-specific implementation
    return true;
  }

  Future<bool> _checkPlaneDetectionSupport() async {
    // Platform-specific implementation
    return true;
  }

  Future<bool> _checkLightEstimationSupport() async {
    // Platform-specific implementation
    return true;
  }

  Future<bool> _checkOcclusionSupport() async {
    // Platform-specific implementation
    return false; // Advanced feature
  }

  Future<bool> _checkPersistentAnchorsSupport() async {
    // Platform-specific implementation
    return true;
  }
}

// Import math for calculations
import 'dart:math' as math;
