<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\SecurityService;
use App\Services\QuantumSecurityService;
use App\Services\NeuroAIService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\RateLimiter;
use Laravel\Sanctum\HasApiTokens;

class AuthController extends Controller
{
    protected $securityService;
    protected $quantumSecurity;
    protected $neuroAI;

    public function __construct(
        SecurityService $securityService,
        QuantumSecurityService $quantumSecurity,
        NeuroAIService $neuroAI
    ) {
        $this->securityService = $securityService;
        $this->quantumSecurity = $quantumSecurity;
        $this->neuroAI = $neuroAI;
    }

    /**
     * Register a new user with advanced security
     */
    public function register(Request $request)
    {
        try {
            // Rate limiting
            $key = 'register:' . $request->ip();
            if (RateLimiter::tooManyAttempts($key, 5)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many registration attempts. Please try again later.',
                    'retry_after' => RateLimiter::availableIn($key)
                ], 429);
            }

            // Validate input
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'phone' => 'required|string|max:20|unique:users',
                'password' => 'required|string|min:8|confirmed',
                'user_type' => 'required|in:customer,barber',
                'location' => 'required|array',
                'location.latitude' => 'required|numeric|between:-90,90',
                'location.longitude' => 'required|numeric|between:-180,180',
                'biometric_data' => 'sometimes|string',
                'neural_pattern' => 'sometimes|string'
            ]);

            if ($validator->fails()) {
                RateLimiter::hit($key);
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Security checks
            $securityCheck = $this->securityService->performSecurityCheck($request->all());
            if (!$securityCheck['passed']) {
                RateLimiter::hit($key);
                return response()->json([
                    'success' => false,
                    'message' => 'Security check failed',
                    'reason' => $securityCheck['reason']
                ], 403);
            }

            // Generate quantum-safe encryption keys
            $quantumKeys = $this->quantumSecurity->generateQuantumSafeKey();

            // Create user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'user_type' => $request->user_type,
                'location' => json_encode($request->location),
                'quantum_key_id' => $quantumKeys['key_id'],
                'security_level' => 'quantum_enhanced',
                'registration_ip' => $request->ip(),
                'registration_user_agent' => $request->userAgent(),
                'email_verified_at' => null,
                'phone_verified_at' => null,
                'status' => 'pending_verification'
            ]);

            // Store biometric data if provided
            if ($request->has('biometric_data')) {
                $this->securityService->storeBiometricData($user->id, $request->biometric_data);
            }

            // Analyze user behavior with Neuro-AI
            if ($request->has('neural_pattern')) {
                $this->neuroAI->analyzeCustomerBehavior($user->id, [
                    'registration_pattern' => $request->neural_pattern,
                    'device_info' => $request->header('User-Agent'),
                    'location' => $request->location
                ]);
            }

            // Generate tokens
            $token = $user->createToken('auth_token', ['*'], now()->addDays(30))->plainTextToken;
            $refreshToken = $user->createToken('refresh_token', ['refresh'], now()->addDays(90))->plainTextToken;

            // Send verification emails/SMS
            $this->sendVerificationNotifications($user);

            // Clear rate limiter on success
            RateLimiter::clear($key);

            return response()->json([
                'success' => true,
                'message' => 'User registered successfully',
                'data' => [
                    'user' => $user->makeHidden(['password', 'quantum_key_id']),
                    'access_token' => $token,
                    'refresh_token' => $refreshToken,
                    'token_type' => 'Bearer',
                    'expires_in' => 30 * 24 * 60 * 60, // 30 days in seconds
                    'quantum_secured' => true,
                    'security_level' => 'quantum_enhanced'
                ]
            ], 201);

        } catch (\Exception $e) {
            RateLimiter::hit($key);
            return response()->json([
                'success' => false,
                'message' => 'Registration failed',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Login with advanced authentication
     */
    public function login(Request $request)
    {
        try {
            // Rate limiting
            $key = 'login:' . $request->ip();
            if (RateLimiter::tooManyAttempts($key, 10)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many login attempts. Please try again later.',
                    'retry_after' => RateLimiter::availableIn($key)
                ], 429);
            }

            // Validate input
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
                'password' => 'required|string',
                'biometric_data' => 'sometimes|string',
                'device_fingerprint' => 'sometimes|string',
                'location' => 'sometimes|array'
            ]);

            if ($validator->fails()) {
                RateLimiter::hit($key);
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Find user
            $user = User::where('email', $request->email)->first();

            if (!$user || !Hash::check($request->password, $user->password)) {
                RateLimiter::hit($key);
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ], 401);
            }

            // Check user status
            if ($user->status === 'suspended') {
                return response()->json([
                    'success' => false,
                    'message' => 'Account suspended. Please contact support.'
                ], 403);
            }

            // Biometric verification if provided
            if ($request->has('biometric_data')) {
                $biometricVerification = $this->securityService->verifyBiometricData(
                    $user->id,
                    $request->biometric_data
                );

                if (!$biometricVerification['verified']) {
                    RateLimiter::hit($key);
                    return response()->json([
                        'success' => false,
                        'message' => 'Biometric verification failed'
                    ], 401);
                }
            }

            // Device fingerprinting
            $deviceTrusted = true;
            if ($request->has('device_fingerprint')) {
                $deviceTrusted = $this->securityService->verifyDeviceFingerprint(
                    $user->id,
                    $request->device_fingerprint
                );
            }

            // Behavioral analysis with Neuro-AI
            $behaviorAnalysis = $this->neuroAI->analyzeCustomerBehavior($user->id, [
                'login_pattern' => [
                    'time' => now()->toTimeString(),
                    'location' => $request->location ?? null,
                    'device' => $request->header('User-Agent'),
                    'ip' => $request->ip()
                ]
            ]);

            // Risk assessment
            $riskScore = $this->calculateLoginRiskScore($user, $request, $behaviorAnalysis);

            // Generate tokens with quantum security
            $tokenData = $this->generateSecureTokens($user, $riskScore);

            // Update user login info
            $user->update([
                'last_login_at' => now(),
                'last_login_ip' => $request->ip(),
                'last_login_user_agent' => $request->userAgent()
            ]);

            // Clear rate limiter on success
            RateLimiter::clear($key);

            $response = [
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => $user->makeHidden(['password', 'quantum_key_id']),
                    'access_token' => $tokenData['access_token'],
                    'refresh_token' => $tokenData['refresh_token'],
                    'token_type' => 'Bearer',
                    'expires_in' => $tokenData['expires_in'],
                    'quantum_secured' => true,
                    'device_trusted' => $deviceTrusted,
                    'risk_score' => $riskScore,
                    'behavior_analysis' => $behaviorAnalysis['insights'] ?? null
                ]
            ];

            // Add 2FA requirement for high-risk logins
            if ($riskScore > 0.7 || !$deviceTrusted) {
                $response['requires_2fa'] = true;
                $response['message'] = 'Two-factor authentication required';
                
                // Send 2FA code
                $this->send2FACode($user);
            }

            return response()->json($response, 200);

        } catch (\Exception $e) {
            RateLimiter::hit($key);
            return response()->json([
                'success' => false,
                'message' => 'Login failed',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Verify 2FA code
     */
    public function verify2FA(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'code' => 'required|string|size:6',
            'temp_token' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::where('email', $request->email)->first();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found'
            ], 404);
        }

        // Verify 2FA code
        $verified = $this->securityService->verify2FACode($user->id, $request->code);

        if (!$verified) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid 2FA code'
            ], 401);
        }

        // Generate final tokens
        $tokenData = $this->generateSecureTokens($user, 0.1); // Low risk after 2FA

        return response()->json([
            'success' => true,
            'message' => '2FA verification successful',
            'data' => [
                'user' => $user->makeHidden(['password', 'quantum_key_id']),
                'access_token' => $tokenData['access_token'],
                'refresh_token' => $tokenData['refresh_token'],
                'token_type' => 'Bearer',
                'expires_in' => $tokenData['expires_in'],
                'quantum_secured' => true
            ]
        ], 200);
    }

    /**
     * Refresh access token
     */
    public function refresh(Request $request)
    {
        $user = $request->user();
        
        // Revoke current tokens
        $user->tokens()->delete();
        
        // Generate new tokens
        $tokenData = $this->generateSecureTokens($user, 0.2);

        return response()->json([
            'success' => true,
            'message' => 'Token refreshed successfully',
            'data' => [
                'access_token' => $tokenData['access_token'],
                'refresh_token' => $tokenData['refresh_token'],
                'token_type' => 'Bearer',
                'expires_in' => $tokenData['expires_in']
            ]
        ], 200);
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        $user = $request->user();
        
        // Revoke current token
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ], 200);
    }

    /**
     * Logout from all devices
     */
    public function logoutAll(Request $request)
    {
        $user = $request->user();
        
        // Revoke all tokens
        $user->tokens()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logged out from all devices successfully'
        ], 200);
    }

    // Helper methods

    protected function sendVerificationNotifications($user)
    {
        // Send email verification
        // Send SMS verification
        // Implementation would go here
    }

    protected function calculateLoginRiskScore($user, $request, $behaviorAnalysis)
    {
        $riskScore = 0.0;

        // Check for unusual location
        if ($user->location) {
            $lastLocation = json_decode($user->location, true);
            $currentLocation = $request->location;
            
            if ($currentLocation && $this->calculateDistance($lastLocation, $currentLocation) > 100) {
                $riskScore += 0.3;
            }
        }

        // Check for unusual time
        $currentHour = now()->hour;
        if ($currentHour < 6 || $currentHour > 23) {
            $riskScore += 0.2;
        }

        // Check behavior analysis
        if (isset($behaviorAnalysis['predictions']['churn_risk']) && 
            $behaviorAnalysis['predictions']['churn_risk'] > 0.5) {
            $riskScore += 0.2;
        }

        return min($riskScore, 1.0);
    }

    protected function generateSecureTokens($user, $riskScore)
    {
        $expiresIn = $riskScore > 0.5 ? 3600 : 86400; // 1 hour for high risk, 24 hours for low risk
        
        $accessToken = $user->createToken('auth_token', ['*'], now()->addSeconds($expiresIn))->plainTextToken;
        $refreshToken = $user->createToken('refresh_token', ['refresh'], now()->addDays(30))->plainTextToken;

        return [
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'expires_in' => $expiresIn
        ];
    }

    protected function send2FACode($user)
    {
        // Generate and send 2FA code
        // Implementation would go here
    }

    protected function calculateDistance($location1, $location2)
    {
        // Calculate distance between two coordinates
        $earthRadius = 6371; // km

        $dLat = deg2rad($location2['latitude'] - $location1['latitude']);
        $dLng = deg2rad($location2['longitude'] - $location1['longitude']);

        $a = sin($dLat/2) * sin($dLat/2) +
             cos(deg2rad($location1['latitude'])) * cos(deg2rad($location2['latitude'])) *
             sin($dLng/2) * sin($dLng/2);

        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }
}
