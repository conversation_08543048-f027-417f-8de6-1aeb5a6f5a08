# 🚀 **ابدأ هنا - تشغيل مشروع "حلاق على بابك"**

---

## ⚡ **تشغيل سريع (3 خطوات فقط)**

### 1️⃣ **إعداد قاعدة البيانات**
```bash
# تشغيل MySQL وإنشاء قاعدة البيانات
mysql -u root -p
CREATE DATABASE barber_app;
exit
```

### 2️⃣ **تشغيل Backend**
```bash
cd backend
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate --seed
php artisan serve --port=8000
```

### 3️⃣ **تشغيل لوحة الإدارة**
```bash
# في terminal جديد
cd admin-dashboard
npm install
npm run dev
```

---

## 🌐 **الوصول للنظام**

### 🔗 **الروابط:**
- **🖥️ لوحة الإدارة**: http://localhost:3000
- **📡 Backend API**: http://localhost:8000

### 🔐 **تسجيل الدخول:**
- **البريد**: <EMAIL>
- **كلمة المرور**: admin123

---

## 🛠️ **إذا واجهت مشاكل**

### ❌ **مشكلة في قاعدة البيانات:**
```bash
# استخدم السكريبت الجاهز
mysql -u root -p < setup-database.sql
```

### ❌ **مشكلة في الصلاحيات:**
```bash
cd backend
chmod -R 775 storage bootstrap/cache
```

### ❌ **مشكلة في المنافذ:**
```bash
# غير المنفذ
php artisan serve --port=8001
npm run dev -- --port=3001
```

---

## 🎯 **بعد التشغيل الناجح**

1. **✅ سجل دخول للوحة الإدارة**
2. **✅ أضف مدن ومناطق**
3. **✅ أضف خدمات**
4. **✅ أنشئ حسابات حلاقين**
5. **✅ اختبر النظام**

---

## 📞 **للمساعدة**

راجع الملفات التفصيلية:
- **QUICK_START_GUIDE.md** - دليل شامل
- **RUN_PROJECT.md** - تعليمات مفصلة
- **DEPLOYMENT_GUIDE.md** - دليل النشر

---

**🎉 مبروك! النظام جاهز للاستخدام! 🚀**
