_fe_analyzer_shared
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/lib/
_flutterfire_internals
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.35/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.35/lib/
analyzer
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-6.11.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-6.11.0/lib/
archive
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/archive-3.6.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/archive-3.6.1/lib/
args
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/lib/
async
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/lib/
boolean_selector
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
build
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build-2.5.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build-2.5.4/lib/
build_config
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_config-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_config-1.1.2/lib/
build_daemon
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_daemon-4.0.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_daemon-4.0.4/lib/
build_resolvers
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_resolvers-2.5.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_resolvers-2.5.4/lib/
build_runner
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner-2.5.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner-2.5.4/lib/
build_runner_core
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner_core-9.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner_core-9.1.2/lib/
built_collection
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_collection-5.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_collection-5.1.1/lib/
built_value
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_value-8.11.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_value-8.11.0/lib/
cached_network_image
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image-3.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image-3.4.1/lib/
cached_network_image_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/
cached_network_image_web
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib/
characters
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/lib/
checked_yaml
3.8
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/checked_yaml-2.0.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/checked_yaml-2.0.4/lib/
clock
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/lib/
code_builder
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/code_builder-4.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/code_builder-4.10.1/lib/
collection
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/lib/
connectivity_plus
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus-5.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus-5.0.2/lib/
connectivity_plus_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/lib/
convert
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2/lib/
cross_file
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/lib/
csslib
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/csslib-1.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/csslib-1.0.2/lib/
cupertino_icons
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dart_style
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dart_style-2.3.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dart_style-2.3.8/lib/
dbus
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11/lib/
device_info_plus
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus-9.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus-9.1.2/lib/
device_info_plus_platform_interface
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/lib/
dio
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dio-5.8.0+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dio-5.8.0+1/lib/
dio_web_adapter
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dio_web_adapter-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dio_web_adapter-2.1.1/lib/
fake_async
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/lib/
ffi
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/lib/
file_picker
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_picker-6.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_picker-6.2.1/lib/
file_selector_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/
file_selector_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
firebase_auth
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-4.16.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-4.16.0/lib/
firebase_auth_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.3.0/lib/
firebase_auth_web
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.8.13/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.8.13/lib/
firebase_core
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-2.32.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-2.32.0/lib/
firebase_core_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/
firebase_core_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.24.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.24.0/lib/
firebase_messaging
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging-14.7.10/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging-14.7.10/lib/
firebase_messaging_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/lib/
firebase_messaging_web
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging_web-3.5.18/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging_web-3.5.18/lib/
fixnum
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib/
flutter_animate
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_animate-4.5.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_animate-4.5.2/lib/
flutter_cache_manager
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/
flutter_lints
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-3.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-3.0.2/lib/
flutter_plugin_android_lifecycle
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib/
flutter_rating_bar
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_rating_bar-4.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_rating_bar-4.0.1/lib/
flutter_shaders
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_shaders-0.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_shaders-0.1.3/lib/
flutter_stripe
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_stripe-9.6.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_stripe-9.6.0/lib/
flutter_svg
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_svg-2.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_svg-2.2.0/lib/
freezed_annotation
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/freezed_annotation-2.4.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/freezed_annotation-2.4.4/lib/
frontend_server_client
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/frontend_server_client-4.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/frontend_server_client-4.0.0/lib/
geocoding
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding-2.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding-2.2.2/lib/
geocoding_android
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_android-3.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_android-3.3.1/lib/
geocoding_ios
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_ios-2.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_ios-2.3.0/lib/
geocoding_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/
geolocator
2.15
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator-10.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator-10.1.1/lib/
geolocator_android
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_android-4.6.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_android-4.6.2/lib/
geolocator_apple
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_apple-2.3.13/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/
geolocator_platform_interface
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/
geolocator_web
2.15
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_web-2.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_web-2.2.1/lib/
geolocator_windows
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_windows-0.2.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_windows-0.2.5/lib/
get
2.15
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/get-4.7.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/get-4.7.2/lib/
glob
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3/lib/
go_router
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/go_router-12.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/go_router-12.1.3/lib/
google_fonts
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_fonts-6.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_fonts-6.2.1/lib/
google_maps
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps-8.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps-8.1.1/lib/
google_maps_flutter
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter-2.12.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter-2.12.3/lib/
google_maps_flutter_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_android-2.17.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_android-2.17.0/lib/
google_maps_flutter_ios
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/lib/
google_maps_flutter_platform_interface
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/
google_maps_flutter_web
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_web-0.5.12+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_web-0.5.12+2/lib/
graphs
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/graphs-2.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/graphs-2.3.2/lib/
hive
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hive-2.2.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hive-2.2.3/lib/
hive_flutter
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hive_flutter-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hive_flutter-1.1.0/lib/
hive_generator
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hive_generator-2.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hive_generator-2.0.1/lib/
html
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/html-0.15.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/html-0.15.6/lib/
http
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0/lib/
http_multi_server
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_multi_server-3.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_multi_server-3.2.2/lib/
http_parser
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/lib/
image_picker
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2/lib/
image_picker_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+24/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+24/lib/
image_picker_for_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/
image_picker_ios
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
intl
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.18.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.18.1/lib/
io
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/io-1.0.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/io-1.0.5/lib/
js
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/lib/
leak_tracker
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-3.0.0/lib/
local_auth
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth-2.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth-2.3.0/lib/
local_auth_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_android-1.0.50/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_android-1.0.50/lib/
local_auth_darwin
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_darwin-1.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/
local_auth_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/
local_auth_windows
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_windows-1.0.11/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/
location
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/location-5.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/location-5.0.3/lib/
location_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/location_platform_interface-3.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/location_platform_interface-3.1.2/lib/
location_web
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/location_web-4.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/location_web-4.2.0/lib/
logging
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/lib/
lottie
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lottie-2.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lottie-2.7.0/lib/
macros
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/macros-0.1.3-main.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/macros-0.1.3-main.0/lib/
matcher
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/lib/
mime
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-1.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-1.0.6/lib/
nested
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/lib/
nm
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nm-0.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nm-0.5.0/lib/
octo_image
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/octo_image-2.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/octo_image-2.1.0/lib/
package_config
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.2.0/lib/
package_info_plus
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus-4.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus-4.2.0/lib/
package_info_plus_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-2.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-2.0.1/lib/
path
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/lib/
path_parsing
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_parsing-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_parsing-1.1.0/lib/
path_provider
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.17/lib/
path_provider_foundation
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
permission_handler
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler-11.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler-11.4.0/lib/
permission_handler_android
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_android-12.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_android-12.1.0/lib/
permission_handler_apple
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0/lib/
platform
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pool
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pool-1.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pool-1.5.1/lib/
provider
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5/lib/
pub_semver
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.2.0/lib/
pubspec_parse
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pubspec_parse-1.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pubspec_parse-1.5.0/lib/
qr
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr-3.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr-3.0.2/lib/
qr_code_scanner
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr_code_scanner-1.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr_code_scanner-1.0.1/lib/
qr_flutter
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr_flutter-4.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr_flutter-4.1.0/lib/
rive
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rive-0.12.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rive-0.12.4/lib/
rive_common
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rive_common-0.2.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rive_common-0.2.8/lib/
rxdart
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rxdart-0.28.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rxdart-0.28.0/lib/
sanitize_html
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sanitize_html-2.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sanitize_html-2.1.0/lib/
share_plus
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus-7.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus-7.2.2/lib/
share_plus_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/lib/
shared_preferences
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
shelf
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf-1.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf-1.4.2/lib/
shelf_web_socket
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_web_socket-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_web_socket-3.0.0/lib/
shimmer
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shimmer-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shimmer-3.0.0/lib/
source_gen
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_gen-1.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_gen-1.5.0/lib/
source_helper
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_helper-1.3.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_helper-1.3.5/lib/
source_span
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqflite
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.2/lib/
sqflite_android
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.1/lib/
sqflite_common
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.5/lib/
sqflite_darwin
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/
sqflite_platform_interface
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/
stack_trace
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/lib/
stream_channel
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/lib/
stream_transform
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/lib/
stripe_android
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_android-9.6.0+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_android-9.6.0+2/lib/
stripe_ios
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_ios-9.6.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_ios-9.6.0/lib/
stripe_platform_interface
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_platform_interface-9.6.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_platform_interface-9.6.0/lib/
synchronized
3.8
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.4.0/lib/
term_glyph
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/lib/
timing
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timing-1.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timing-1.0.2/lib/
typed_data
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib/
url_launcher
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher-6.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher-6.3.2/lib/
url_launcher_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.16/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/
url_launcher_ios
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/
url_launcher_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1/lib/
url_launcher_windows
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/
uuid
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_graphics
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics-1.1.19/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics-1.1.19/lib/
vector_graphics_codec
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_codec-1.1.13/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib/
vector_graphics_compiler
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/lib/
vector_math
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/lib/
watcher
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.2/lib/
web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/lib/
web_socket
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-1.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-1.0.1/lib/
web_socket_channel
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/
win32
3.8
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.14.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.14.0/lib/
win32_registry
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32_registry-1.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32_registry-1.1.5/lib/
xdg_directories
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3/lib/
_macros
3.5
file:///C:/flutter/bin/cache/dart-sdk/pkg/_macros/
file:///C:/flutter/bin/cache/dart-sdk/pkg/_macros/lib/
sky_engine
3.7
file:///C:/flutter/bin/cache/pkg/sky_engine/
file:///C:/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///C:/flutter/packages/flutter/
file:///C:/flutter/packages/flutter/lib/
flutter_test
3.7
file:///C:/flutter/packages/flutter_test/
file:///C:/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///C:/flutter/packages/flutter_web_plugins/
file:///C:/flutter/packages/flutter_web_plugins/lib/
barber_customer_app
3.0
file:///D:/qqq/htdocs/flutter_module_2/customer_app/
file:///D:/qqq/htdocs/flutter_module_2/customer_app/lib/
2
