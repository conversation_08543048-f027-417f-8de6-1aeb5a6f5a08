{"name": "barber-admin-dashboard", "version": "1.0.0", "description": "لوحة التحكم الديناميكية لمشروع حلاق على بابك", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "vuetify": "^3.3.15", "@mdi/font": "^7.2.96", "axios": "^1.5.0", "chart.js": "^4.4.0", "vue-chartjs": "^5.2.0", "vue-i18n": "^9.4.1", "date-fns": "^2.30.0", "lodash": "^4.17.21", "sweetalert2": "^11.7.28", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.3", "sass": "^1.66.1"}, "keywords": ["vue", "vuetify", "admin", "dashboard", "barber", "dynamic"], "author": "Barber App Team", "license": "MIT"}