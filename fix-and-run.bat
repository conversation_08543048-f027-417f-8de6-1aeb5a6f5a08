@echo off
chcp 65001 >nul
title إصلاح وتشغيل مشروع حلاق على بابك

echo.
echo ========================================
echo 🔧 إصلاح وتشغيل مشروع "حلاق على بابك"
echo ========================================
echo.

echo 📍 مسار المشروع الحالي: %CD%
echo 📍 مسار PHP المتوقع: D:\qqq\php\php.exe
echo.

:: التحقق من وجود PHP
if exist "D:\qqq\php\php.exe" (
    echo ✅ تم العثور على PHP
    set PHP_PATH=D:\qqq\php\php.exe
) else (
    echo ❌ لم يتم العثور على PHP في D:\qqq\php\
    echo 🔍 البحث عن PHP في مسارات أخرى...
    
    if exist "C:\xampp\php\php.exe" (
        echo ✅ تم العثور على PHP في XAMPP
        set PHP_PATH=C:\xampp\php\php.exe
    ) else if exist "C:\wamp64\bin\php\php8.1.0\php.exe" (
        echo ✅ تم العثور على PHP في WAMP
        set PHP_PATH=C:\wamp64\bin\php\php8.1.0\php.exe
    ) else (
        echo ❌ لم يتم العثور على PHP
        echo 📥 يرجى تثبيت XAMPP من: https://www.apachefriends.org/
        pause
        exit /b 1
    )
)

echo 🔧 استخدام PHP من: %PHP_PATH%
echo.

:: التحقق من وجود Composer
set COMPOSER_PATH=%PHP_PATH:php.exe=composer.phar%

if not exist "%COMPOSER_PATH%" (
    echo 📥 تحميل Composer...
    %PHP_PATH% -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
    %PHP_PATH% composer-setup.php --install-dir=%PHP_PATH:php.exe=%
    %PHP_PATH% -r "unlink('composer-setup.php');"
    echo ✅ تم تثبيت Composer
)

echo.
echo 🏗️ إعداد Backend...

:: الانتقال إلى مجلد Backend
if not exist "backend" (
    echo ❌ مجلد backend غير موجود في: %CD%
    echo 💡 تأكد من تشغيل هذا الملف من مجلد المشروع الصحيح
    pause
    exit /b 1
)

cd backend

:: التحقق من وجود composer.json
if not exist "composer.json" (
    echo ❌ ملف composer.json غير موجود
    echo 💡 هذا ليس مشروع Laravel صحيح
    pause
    exit /b 1
)

:: تثبيت تبعيات Laravel
echo 📦 تثبيت تبعيات Laravel...
%PHP_PATH% %COMPOSER_PATH% install --no-dev --optimize-autoloader

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات
    echo 💡 تحقق من اتصال الإنترنت
    pause
    exit /b 1
)

:: إنشاء ملف .env إذا لم يكن موجوداً
if not exist ".env" (
    echo 📝 إنشاء ملف .env...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
    ) else (
        echo APP_NAME="حلاق على بابك" > .env
        echo APP_ENV=local >> .env
        echo APP_KEY= >> .env
        echo APP_DEBUG=true >> .env
        echo APP_URL=http://localhost:8000 >> .env
        echo. >> .env
        echo DB_CONNECTION=mysql >> .env
        echo DB_HOST=127.0.0.1 >> .env
        echo DB_PORT=3306 >> .env
        echo DB_DATABASE=barber_app >> .env
        echo DB_USERNAME=root >> .env
        echo DB_PASSWORD= >> .env
    )
)

:: توليد مفتاح التطبيق
echo 🔑 توليد مفتاح التطبيق...
%PHP_PATH% artisan key:generate --force

:: إنشاء مجلدات التخزين
echo 📁 إنشاء مجلدات التخزين...
if not exist "storage\app\public" mkdir storage\app\public
if not exist "storage\framework\cache" mkdir storage\framework\cache
if not exist "storage\framework\sessions" mkdir storage\framework\sessions
if not exist "storage\framework\views" mkdir storage\framework\views
if not exist "storage\logs" mkdir storage\logs
if not exist "bootstrap\cache" mkdir bootstrap\cache

:: إنشاء رابط التخزين
echo 🔗 إنشاء رابط التخزين...
%PHP_PATH% artisan storage:link

echo.
echo 🚀 تشغيل خادم Laravel...
echo 📡 Backend سيعمل على: http://localhost:8000
echo.

:: تشغيل خادم Laravel
start "Laravel Server" cmd /k "%PHP_PATH% artisan serve --port=8000"

:: انتظار قصير
timeout /t 3 /nobreak >nul

echo.
echo ✅ تم إصلاح وتشغيل Backend بنجاح!
echo.
echo 🌐 يمكنك الآن الوصول إلى:
echo 📡 Backend: http://localhost:8000
echo 📊 API: http://localhost:8000/api/v1
echo.
echo 💡 لتشغيل لوحة الإدارة أيضاً، شغل:
echo npm install (في مجلد admin-dashboard)
echo npm run dev
echo.

:: فتح المتصفح
start http://localhost:8000

echo اضغط أي مفتاح للخروج...
pause >nul
