# 📚 API Documentation - Barber App

## 🌐 Base URL
```
Production: https://api.barber-app.com
Development: http://localhost:8000/api
```

## 🔐 Authentication

### Headers Required
```http
Authorization: Bearer {access_token}
Content-Type: application/json
Accept: application/json
```

---

## 👤 Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "name": "أحمد محمد",
  "email": "<EMAIL>",
  "phone": "+************",
  "password": "password123",
  "password_confirmation": "password123",
  "user_type": "customer" // or "barber"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "أحمد محمد",
      "email": "<EMAIL>",
      "phone": "+************"
    },
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  }
}
```

### POST /auth/login
Login with email/phone and password.

**Request Body:**
```json
{
  "email": "<EMAIL>", // or phone
  "password": "password123"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "أحمد محمد",
      "email": "<EMAIL>"
    },
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  }
}
```

### POST /auth/logout
Logout and invalidate token.

**Response (200):**
```json
{
  "success": true,
  "message": "تم تسجيل الخروج بنجاح"
}
```

---

## 🏠 General Endpoints

### GET /health
Check API health status.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0.0",
    "services": {
      "database": "connected",
      "redis": "connected",
      "elasticsearch": "connected"
    }
  }
}
```

### GET /cities
Get list of available cities.

**Query Parameters:**
- `is_active` (boolean): Filter by active status

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "القاهرة",
      "name_en": "Cairo",
      "latitude": 30.0444,
      "longitude": 31.2357
    }
  ]
}
```

### GET /cities/{id}/areas
Get areas within a city.

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "مدينة نصر",
      "name_en": "Nasr City",
      "city_id": 1
    }
  ]
}
```

---

## 🛍️ Services Endpoints

### GET /services
Get list of available services.

**Query Parameters:**
- `category_id` (integer): Filter by category
- `is_active` (boolean): Filter by active status
- `search` (string): Search by name

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "قص شعر عادي",
      "description": "قص شعر كلاسيكي",
      "base_price": 50.00,
      "duration_minutes": 30,
      "category": {
        "id": 1,
        "name": "قص الشعر"
      }
    }
  ]
}
```

### GET /service-categories
Get service categories.

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "قص الشعر",
      "description": "خدمات قص وتصفيف الشعر",
      "icon": "scissors",
      "services_count": 5
    }
  ]
}
```

---

## 💇‍♂️ Barbers Endpoints

### GET /barbers
Get list of barbers with filters.

**Query Parameters:**
- `city_id` (integer): Filter by city
- `area_id` (integer): Filter by area
- `service_ids` (array): Filter by services offered
- `latitude` (float): User latitude for distance calculation
- `longitude` (float): User longitude for distance calculation
- `radius` (integer): Search radius in km (default: 10)
- `rating_min` (float): Minimum rating filter
- `is_available` (boolean): Filter by availability
- `sort` (string): Sort by (rating, distance, price)
- `page` (integer): Page number
- `per_page` (integer): Items per page (max: 50)

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "user": {
        "id": 2,
        "name": "أحمد محمد",
        "avatar": "https://example.com/avatar.jpg"
      },
      "experience_years": 5,
      "rating": 4.8,
      "total_reviews": 150,
      "total_bookings": 500,
      "is_verified": true,
      "is_available": true,
      "distance": 2.5,
      "services": [
        {
          "id": 1,
          "name": "قص شعر عادي",
          "price": 50.00
        }
      ],
      "current_location": {
        "address": "مدينة نصر، القاهرة",
        "latitude": 30.0444,
        "longitude": 31.2357
      }
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 20,
    "total": 45,
    "last_page": 3
  }
}
```

### GET /barbers/{id}
Get detailed barber information.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "user": {
      "id": 2,
      "name": "أحمد محمد",
      "avatar": "https://example.com/avatar.jpg"
    },
    "bio": "حلاق محترف مع خبرة 5 سنوات",
    "experience_years": 5,
    "specialties": ["قص شعر", "حلاقة ذقن"],
    "rating": 4.8,
    "total_reviews": 150,
    "is_verified": true,
    "is_available": true,
    "working_hours": {
      "saturday": {"start": "09:00", "end": "22:00"},
      "sunday": {"start": "09:00", "end": "22:00"}
    },
    "services": [...],
    "reviews": [...],
    "gallery": [...]
  }
}
```

### GET /barbers/{id}/availability
Get barber's availability for specific date.

**Query Parameters:**
- `date` (required): Date in YYYY-MM-DD format

**Response (200):**
```json
{
  "success": true,
  "data": {
    "date": "2024-01-15",
    "available_slots": [
      "09:00", "09:30", "10:00", "14:00", "14:30"
    ],
    "booked_slots": [
      "10:30", "11:00", "15:00"
    ]
  }
}
```

---

## 📅 Bookings Endpoints

### POST /bookings
Create a new booking.

**Request Body:**
```json
{
  "barber_id": 1,
  "services": [1, 2],
  "scheduled_date": "2024-01-15",
  "scheduled_time": "14:00",
  "customer_address": "123 شارع التحرير، القاهرة",
  "customer_latitude": 30.0444,
  "customer_longitude": 31.2357,
  "notes": "يرجى الالتزام بالموعد"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "booking_number": "BK20240115001",
    "status": "pending",
    "total_amount": 80.00,
    "commission_amount": 12.00,
    "barber_amount": 68.00,
    "scheduled_date": "2024-01-15",
    "scheduled_time": "14:00",
    "customer_address": "123 شارع التحرير، القاهرة",
    "barber": {
      "id": 1,
      "user": {"name": "أحمد محمد"}
    },
    "services": [
      {
        "id": 1,
        "name": "قص شعر عادي",
        "price": 50.00
      }
    ]
  }
}
```

### GET /bookings
Get user's bookings.

**Query Parameters:**
- `status` (string): Filter by status
- `page` (integer): Page number
- `per_page` (integer): Items per page

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "booking_number": "BK20240115001",
      "status": "confirmed",
      "total_amount": 80.00,
      "scheduled_date": "2024-01-15",
      "scheduled_time": "14:00",
      "barber": {
        "id": 1,
        "user": {"name": "أحمد محمد"}
      },
      "can_cancel": true,
      "can_reschedule": true
    }
  ]
}
```

### GET /bookings/{id}
Get booking details.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "booking_number": "BK20240115001",
    "status": "confirmed",
    "total_amount": 80.00,
    "scheduled_date": "2024-01-15",
    "scheduled_time": "14:00",
    "customer_address": "123 شارع التحرير، القاهرة",
    "notes": "يرجى الالتزام بالموعد",
    "barber": {...},
    "services": [...],
    "payment": {...},
    "timeline": [
      {
        "status": "pending",
        "timestamp": "2024-01-14T10:00:00Z"
      },
      {
        "status": "confirmed",
        "timestamp": "2024-01-14T10:15:00Z"
      }
    ]
  }
}
```

### PUT /bookings/{id}/cancel
Cancel a booking.

**Request Body:**
```json
{
  "reason": "تغيير في الخطط"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "تم إلغاء الحجز بنجاح",
  "data": {
    "refund_amount": 80.00,
    "refund_method": "original_payment_method"
  }
}
```

---

## 💳 Payments Endpoints

### POST /payments
Process payment for a booking.

**Request Body:**
```json
{
  "booking_id": 1,
  "payment_method_id": 1,
  "amount": 80.00,
  "payment_details": {
    "card_number": "****************",
    "exp_month": "12",
    "exp_year": "2025",
    "cvc": "123"
  }
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "transaction_id": "txn_1234567890",
    "amount": 80.00,
    "status": "completed",
    "payment_method": {
      "name": "فيزا/ماستركارد",
      "type": "card"
    }
  }
}
```

### GET /payment-methods
Get available payment methods.

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "فيزا/ماستركارد",
      "type": "card",
      "provider": "stripe",
      "is_active": true
    }
  ]
}
```

---

## ⭐ Reviews Endpoints

### POST /bookings/{id}/review
Add review for completed booking.

**Request Body:**
```json
{
  "rating": 5,
  "comment": "خدمة ممتازة وحلاق محترف",
  "is_anonymous": false
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "rating": 5,
    "comment": "خدمة ممتازة وحلاق محترف",
    "customer": {
      "name": "محمد أحمد"
    },
    "created_at": "2024-01-15T16:00:00Z"
  }
}
```

---

## 🔔 Notifications Endpoints

### GET /notifications
Get user notifications.

**Query Parameters:**
- `unread_only` (boolean): Get only unread notifications
- `page` (integer): Page number

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "تم تأكيد حجزك",
      "body": "تم تأكيد حجزك مع أحمد محمد ليوم 15 يناير",
      "type": "booking_confirmed",
      "read_at": null,
      "created_at": "2024-01-14T10:15:00Z"
    }
  ]
}
```

### PUT /notifications/{id}/read
Mark notification as read.

**Response (200):**
```json
{
  "success": true,
  "message": "تم تحديد الإشعار كمقروء"
}
```

---

## 🎯 VIP & Loyalty Endpoints

### GET /vip/packages
Get available VIP packages.

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "VIP برونز",
      "price": 99.00,
      "duration_days": 30,
      "discount_percentage": 10.00,
      "features": ["خصم 10%", "أولوية في الحجز"]
    }
  ]
}
```

### GET /loyalty/status
Get user's loyalty status.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "current_tier": {
      "name": "فضي",
      "level": 2
    },
    "points": {
      "available": 750,
      "lifetime": 1200
    },
    "next_tier": {
      "name": "ذهبي",
      "points_required": 1500
    }
  }
}
```

---

## 📊 Error Responses

### Standard Error Format
```json
{
  "success": false,
  "message": "رسالة الخطأ",
  "errors": {
    "field_name": ["رسالة الخطأ التفصيلية"]
  },
  "error_code": "VALIDATION_ERROR"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Too Many Requests
- `500` - Internal Server Error

---

## 🔄 Rate Limiting

- **General API**: 1000 requests per hour per user
- **Authentication**: 10 requests per minute per IP
- **Search**: 100 requests per minute per user
- **Booking Creation**: 5 requests per minute per user

Rate limit headers:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

---

## 📱 Mobile App Specific

### Device Registration
```http
POST /device/register
{
  "device_token": "fcm_token_here",
  "platform": "android", // or "ios"
  "app_version": "1.0.0"
}
```

### Location Updates
```http
POST /location/update
{
  "latitude": 30.0444,
  "longitude": 31.2357,
  "accuracy": 10.0
}
```

---

## 🧪 Testing

### Test Environment
```
Base URL: https://api-test.barber-app.com
```

### Test Credentials
```
Customer: <EMAIL> / password123
Barber: <EMAIL> / password123
```

---

**📞 Support**: <EMAIL>  
**📖 Version**: 1.0.0  
**🔄 Last Updated**: 2024-01-15
