# Prometheus Alert Rules for Barber App

groups:
  # Application Health Alerts
  - name: application_health
    rules:
      - alert: ApplicationDown
        expr: up{job="barber-app"} == 0
        for: 1m
        labels:
          severity: critical
          service: barber-app
        annotations:
          summary: "Barber App is down"
          description: "The Barber App has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: barber-app
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes."

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: barber-app
        annotations:
          summary: "High response time"
          description: "95th percentile response time is {{ $value }}s for the last 5 minutes."

  # Database Alerts
  - name: database_health
    rules:
      - alert: DatabaseDown
        expr: up{job="mysql"} == 0
        for: 1m
        labels:
          severity: critical
          service: mysql
        annotations:
          summary: "MySQL database is down"
          description: "MySQL database has been down for more than 1 minute."

      - alert: DatabaseHighConnections
        expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          service: mysql
        annotations:
          summary: "High database connections"
          description: "Database connections are at {{ $value | humanizePercentage }} of maximum."

      - alert: DatabaseSlowQueries
        expr: rate(mysql_global_status_slow_queries[5m]) > 10
        for: 5m
        labels:
          severity: warning
          service: mysql
        annotations:
          summary: "High number of slow queries"
          description: "{{ $value }} slow queries per second in the last 5 minutes."

      - alert: DatabaseDiskSpaceHigh
        expr: (mysql_global_status_innodb_data_fsyncs / mysql_global_status_uptime) > 100
        for: 5m
        labels:
          severity: warning
          service: mysql
        annotations:
          summary: "High database disk I/O"
          description: "Database is performing {{ $value }} fsyncs per second."

  # Redis Alerts
  - name: redis_health
    rules:
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 1 minute."

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value | humanizePercentage }}."

      - alert: RedisHighConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "High Redis connections"
          description: "Redis has {{ $value }} connected clients."

  # System Resource Alerts
  - name: system_resources
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}."

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value | humanizePercentage }} available on {{ $labels.instance }}."

      - alert: HighDiskIOWait
        expr: rate(node_cpu_seconds_total{mode="iowait"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High disk I/O wait"
          description: "Disk I/O wait is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

  # Queue Alerts
  - name: queue_health
    rules:
      - alert: QueueBacklog
        expr: horizon_pending_jobs > 1000
        for: 5m
        labels:
          severity: warning
          service: queue
        annotations:
          summary: "High queue backlog"
          description: "There are {{ $value }} pending jobs in the queue."

      - alert: QueueWorkerDown
        expr: horizon_processes == 0
        for: 1m
        labels:
          severity: critical
          service: queue
        annotations:
          summary: "No queue workers running"
          description: "All queue workers are down."

      - alert: HighJobFailureRate
        expr: rate(horizon_failed_jobs_total[5m]) / rate(horizon_processed_jobs_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: queue
        annotations:
          summary: "High job failure rate"
          description: "Job failure rate is {{ $value | humanizePercentage }}."

  # Business Logic Alerts
  - name: business_metrics
    rules:
      - alert: LowBookingRate
        expr: rate(bookings_created_total[1h]) < 5
        for: 30m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "Low booking rate"
          description: "Only {{ $value }} bookings created per hour in the last 30 minutes."

      - alert: HighCancellationRate
        expr: rate(bookings_cancelled_total[1h]) / rate(bookings_created_total[1h]) > 0.2
        for: 15m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "High cancellation rate"
          description: "Booking cancellation rate is {{ $value | humanizePercentage }}."

      - alert: PaymentFailureSpike
        expr: rate(payments_failed_total[5m]) > 10
        for: 5m
        labels:
          severity: critical
          service: payments
        annotations:
          summary: "Payment failure spike"
          description: "{{ $value }} payment failures per second in the last 5 minutes."

      - alert: NoActiveBarbers
        expr: active_barbers_count < 5
        for: 10m
        labels:
          severity: critical
          service: business
        annotations:
          summary: "Very few active barbers"
          description: "Only {{ $value }} barbers are currently active."

  # Security Alerts
  - name: security
    rules:
      - alert: SuspiciousLoginActivity
        expr: rate(failed_login_attempts_total[5m]) > 50
        for: 2m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High failed login attempts"
          description: "{{ $value }} failed login attempts per second in the last 5 minutes."

      - alert: UnauthorizedAPIAccess
        expr: rate(http_requests_total{status="401"}[5m]) > 20
        for: 5m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High unauthorized access attempts"
          description: "{{ $value }} unauthorized API access attempts per second."

      - alert: RateLimitExceeded
        expr: rate(http_requests_total{status="429"}[5m]) > 100
        for: 5m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "Rate limit frequently exceeded"
          description: "{{ $value }} rate limit violations per second."

  # External Services Alerts
  - name: external_services
    rules:
      - alert: PaymentGatewayDown
        expr: probe_success{instance="https://api.stripe.com/health"} == 0
        for: 2m
        labels:
          severity: critical
          service: external
        annotations:
          summary: "Payment gateway unreachable"
          description: "Stripe payment gateway is unreachable."

      - alert: PushNotificationServiceDown
        expr: probe_success{instance="https://fcm.googleapis.com"} == 0
        for: 5m
        labels:
          severity: warning
          service: external
        annotations:
          summary: "Push notification service down"
          description: "Firebase Cloud Messaging is unreachable."

      - alert: LiveStreamingServiceDown
        expr: probe_success{instance="https://api.agora.io"} == 0
        for: 5m
        labels:
          severity: warning
          service: external
        annotations:
          summary: "Live streaming service down"
          description: "Agora live streaming service is unreachable."

  # SSL Certificate Alerts
  - name: ssl_certificates
    rules:
      - alert: SSLCertificateExpiringSoon
        expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 7
        for: 1h
        labels:
          severity: warning
          service: ssl
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate for {{ $labels.instance }} expires in {{ $value | humanizeDuration }}."

      - alert: SSLCertificateExpired
        expr: probe_ssl_earliest_cert_expiry - time() <= 0
        for: 1m
        labels:
          severity: critical
          service: ssl
        annotations:
          summary: "SSL certificate expired"
          description: "SSL certificate for {{ $labels.instance }} has expired."
