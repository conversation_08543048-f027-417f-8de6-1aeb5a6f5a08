class AppConstants {
  // App Info
  static const String appName = 'حلاق على بابك';
  static const String appNameEn = 'Barber at Your Door';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'خدمة الحلاقة المنزلية الأولى في مصر';

  // API Configuration
  static const String baseUrl = 'http://localhost:8000/api/v1';
  static const String apiTimeout = '30'; // seconds
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String settingsKey = 'app_settings';
  static const String languageKey = 'app_language';
  static const String themeKey = 'app_theme';
  static const String locationKey = 'user_location';
  static const String onboardingKey = 'onboarding_completed';
  static const String notificationKey = 'notification_settings';

  // Hive Boxes
  static const String userBox = 'user_box';
  static const String settingsBox = 'settings_box';
  static const String cacheBox = 'cache_box';
  static const String bookingBox = 'booking_box';

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 20.0;

  // Image Sizes
  static const double avatarSize = 80.0;
  static const double smallAvatarSize = 40.0;
  static const double largeAvatarSize = 120.0;
  static const double iconSize = 24.0;
  static const double smallIconSize = 16.0;
  static const double largeIconSize = 32.0;

  // Map Configuration
  static const double defaultZoom = 15.0;
  static const double maxZoom = 20.0;
  static const double minZoom = 10.0;
  static const double locationRadius = 5000.0; // meters

  // Booking Configuration
  static const int maxAdvanceBookingDays = 30;
  static const int minAdvanceBookingHours = 2;
  static const int maxDailyBookings = 5;
  static const Duration defaultServiceDuration = Duration(minutes: 30);

  // Payment Configuration
  static const double minOrderAmount = 50.0;
  static const double maxOrderAmount = 1000.0;
  static const double deliveryFee = 25.0;
  static const double freeDeliveryThreshold = 200.0;

  // VIP Configuration
  static const double vipMonthlyPrice = 299.0;
  static const double vipYearlyPrice = 2999.0;
  static const double vipDiscountPercentage = 20.0;
  static const int vipFreeBookingsMonthly = 2;

  // Loyalty Points
  static const int pointsPerBooking = 10;
  static const int pointsPerEgp = 1;
  static const int pointsRedemptionRate = 100; // 100 points = 1 EGP
  static const int minPointsRedemption = 500;

  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];

  // Notification Types
  static const String bookingNotification = 'booking';
  static const String paymentNotification = 'payment';
  static const String promotionNotification = 'promotion';
  static const String systemNotification = 'system';

  // Error Messages
  static const String networkError = 'خطأ في الاتصال بالإنترنت';
  static const String serverError = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
  static const String unknownError = 'حدث خطأ غير متوقع';
  static const String validationError = 'يرجى التحقق من البيانات المدخلة';
  static const String authError = 'خطأ في المصادقة، يرجى تسجيل الدخول مرة أخرى';
  static const String permissionError = 'ليس لديك صلاحية للوصول لهذه الخدمة';

  // Success Messages
  static const String bookingSuccess = 'تم حجز الموعد بنجاح';
  static const String paymentSuccess = 'تم الدفع بنجاح';
  static const String profileUpdateSuccess = 'تم تحديث الملف الشخصي بنجاح';
  static const String passwordChangeSuccess = 'تم تغيير كلمة المرور بنجاح';

  // Validation Rules
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int phoneLength = 11;

  // Regular Expressions
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phoneRegex = r'^01[0-2,5]{1}[0-9]{8}$';
  static const String nameRegex = r'^[\u0600-\u06FFa-zA-Z\s]+$';

  // Social Media
  static const String facebookUrl = 'https://facebook.com/barberapp';
  static const String instagramUrl = 'https://instagram.com/barberapp';
  static const String twitterUrl = 'https://twitter.com/barberapp';
  static const String whatsappNumber = '+201234567890';

  // Support
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+201234567890';
  static const String privacyPolicyUrl = 'https://barberapp.com/privacy';
  static const String termsOfServiceUrl = 'https://barberapp.com/terms';

  // Feature Flags (will be overridden by dynamic settings)
  static const bool defaultRegistrationEnabled = true;
  static const bool defaultBookingEnabled = true;
  static const bool defaultVipEnabled = true;
  static const bool defaultOnlinePaymentEnabled = true;
  static const bool defaultStoreEnabled = true;
  static const bool defaultLoyaltyPointsEnabled = true;

  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const Duration shortCacheExpiration = Duration(hours: 1);
  static const Duration longCacheExpiration = Duration(days: 7);

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Rating
  static const double minRating = 1.0;
  static const double maxRating = 5.0;
  static const double defaultMinRatingToShow = 3.0;

  // Currency
  static const String defaultCurrency = 'EGP';
  static const String currencySymbol = 'ج.م';

  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayTimeFormat = 'hh:mm a';
  static const String displayDateTimeFormat = 'dd/MM/yyyy hh:mm a';

  // Booking Status
  static const String bookingPending = 'pending';
  static const String bookingConfirmed = 'confirmed';
  static const String bookingInProgress = 'in_progress';
  static const String bookingCompleted = 'completed';
  static const String bookingCancelled = 'cancelled';

  // Payment Methods
  static const String paymentCash = 'cash';
  static const String paymentOnline = 'online';
  static const String paymentWallet = 'wallet';

  // Payment Status
  static const String paymentPending = 'pending';
  static const String paymentPaid = 'paid';
  static const String paymentRefunded = 'refunded';

  // User Types
  static const String userTypeCustomer = 'customer';
  static const String userTypeBarber = 'barber';
  static const String userTypeAdmin = 'admin';

  // Service Categories
  static const String categoryHaircut = 'haircut';
  static const String categoryBeard = 'beard';
  static const String categorySkincare = 'skincare';
  static const String categoryPackage = 'package';

  // VIP Plan Types
  static const String vipMonthly = 'monthly';
  static const String vipPerBooking = 'per_booking';
  static const String vipYearly = 'yearly';
}
