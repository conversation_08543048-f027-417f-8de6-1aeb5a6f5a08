import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/constants/app_constants.dart';

enum ButtonType { primary, secondary, outline, text, gradient }
enum ButtonSize { small, medium, large }

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final bool isLoading;
  final bool isDisabled;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;
  final Gradient? gradient;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.boxShadow,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    final isEnabled = !isDisabled && !isLoading && onPressed != null;
    
    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? _getButtonHeight(),
      child: _buildButton(isEnabled),
    );
  }

  Widget _buildButton(bool isEnabled) {
    switch (type) {
      case ButtonType.primary:
        return _buildPrimaryButton(isEnabled);
      case ButtonType.secondary:
        return _buildSecondaryButton(isEnabled);
      case ButtonType.outline:
        return _buildOutlineButton(isEnabled);
      case ButtonType.text:
        return _buildTextButton(isEnabled);
      case ButtonType.gradient:
        return _buildGradientButton(isEnabled);
    }
  }

  Widget _buildPrimaryButton(bool isEnabled) {
    return ElevatedButton(
      onPressed: isEnabled ? onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? AppColors.primary,
        foregroundColor: textColor ?? Colors.white,
        disabledBackgroundColor: AppColors.grey,
        disabledForegroundColor: AppColors.textLight,
        elevation: isEnabled ? 2 : 0,
        shadowColor: AppColors.shadow,
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(AppConstants.borderRadius),
        ),
        padding: padding ?? _getButtonPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildSecondaryButton(bool isEnabled) {
    return ElevatedButton(
      onPressed: isEnabled ? onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? AppColors.greyLight,
        foregroundColor: textColor ?? AppColors.textPrimary,
        disabledBackgroundColor: AppColors.greyLight,
        disabledForegroundColor: AppColors.textLight,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(AppConstants.borderRadius),
        ),
        padding: padding ?? _getButtonPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildOutlineButton(bool isEnabled) {
    return OutlinedButton(
      onPressed: isEnabled ? onPressed : null,
      style: OutlinedButton.styleFrom(
        foregroundColor: textColor ?? AppColors.primary,
        disabledForegroundColor: AppColors.textLight,
        side: BorderSide(
          color: isEnabled 
              ? (borderColor ?? AppColors.primary)
              : AppColors.border,
          width: 1.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(AppConstants.borderRadius),
        ),
        padding: padding ?? _getButtonPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildTextButton(bool isEnabled) {
    return TextButton(
      onPressed: isEnabled ? onPressed : null,
      style: TextButton.styleFrom(
        foregroundColor: textColor ?? AppColors.primary,
        disabledForegroundColor: AppColors.textLight,
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(AppConstants.borderRadius),
        ),
        padding: padding ?? _getButtonPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildGradientButton(bool isEnabled) {
    return Container(
      decoration: BoxDecoration(
        gradient: isEnabled 
            ? (gradient ?? AppColors.primaryGradient)
            : null,
        color: isEnabled ? null : AppColors.grey,
        borderRadius: borderRadius ?? BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: isEnabled ? (boxShadow ?? [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ]) : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled ? onPressed : null,
          borderRadius: borderRadius ?? BorderRadius.circular(AppConstants.borderRadius),
          child: Container(
            padding: padding ?? _getButtonPadding(),
            child: _buildButtonContent(
              textColor: isEnabled ? Colors.white : AppColors.textLight,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildButtonContent({Color? textColor}) {
    if (isLoading) {
      return LoadingAnimationWidget.staggeredDotsWave(
        color: textColor ?? this.textColor ?? Colors.white,
        size: _getLoadingSize(),
      );
    }

    final content = <Widget>[];
    
    if (icon != null) {
      content.add(Icon(
        icon,
        size: _getIconSize(),
        color: textColor ?? this.textColor,
      ));
      content.add(const SizedBox(width: 8));
    }
    
    content.add(Text(
      text,
      style: _getTextStyle().copyWith(
        color: textColor ?? this.textColor,
      ),
    ));

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: content,
    );
  }

  double _getButtonHeight() {
    switch (size) {
      case ButtonSize.small:
        return 40;
      case ButtonSize.medium:
        return 48;
      case ButtonSize.large:
        return 56;
    }
  }

  EdgeInsetsGeometry _getButtonPadding() {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 12);
      case ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
    }
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case ButtonSize.small:
        return AppTextStyles.buttonSmall;
      case ButtonSize.medium:
        return AppTextStyles.buttonMedium;
      case ButtonSize.large:
        return AppTextStyles.buttonLarge;
    }
  }

  double _getIconSize() {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 20;
      case ButtonSize.large:
        return 24;
    }
  }

  double _getLoadingSize() {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 20;
      case ButtonSize.large:
        return 24;
    }
  }
}

class IconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double size;
  final double iconSize;
  final bool isLoading;

  const IconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size = 48,
    this.iconSize = 24,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.primary,
        borderRadius: BorderRadius.circular(size / 2),
        boxShadow: [
          BoxShadow(
            color: (backgroundColor ?? AppColors.primary).withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(size / 2),
          child: Center(
            child: isLoading
                ? LoadingAnimationWidget.staggeredDotsWave(
                    color: iconColor ?? Colors.white,
                    size: iconSize * 0.8,
                  )
                : Icon(
                    icon,
                    size: iconSize,
                    color: iconColor ?? Colors.white,
                  ),
          ),
        ),
      ),
    );
  }
}
