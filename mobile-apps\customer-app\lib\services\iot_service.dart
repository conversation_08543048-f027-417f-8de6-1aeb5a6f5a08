import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'package:wifi_iot/wifi_iot.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';

class IoTService {
  static final IoTService _instance = IoTService._internal();
  factory IoTService() => _instance;
  IoTService._internal();

  // Bluetooth
  BluetoothConnection? _bluetoothConnection;
  StreamSubscription<Uint8List>? _bluetoothSubscription;
  bool _isBluetoothConnected = false;

  // WiFi
  bool _isWiFiConnected = false;
  String? _connectedSSID;

  // IoT Device Management
  final Map<String, IoTDevice> _connectedDevices = {};
  final StreamController<IoTDeviceEvent> _deviceEventController = 
      StreamController<IoTDeviceEvent>.broadcast();

  // Getters
  bool get isBluetoothConnected => _isBluetoothConnected;
  bool get isWiFiConnected => _isWiFiConnected;
  String? get connectedSSID => _connectedSSID;
  Stream<IoTDeviceEvent> get deviceEvents => _deviceEventController.stream;
  List<IoTDevice> get connectedDevices => _connectedDevices.values.toList();

  /// Initialize IoT service
  Future<bool> initialize() async {
    try {
      // Request permissions
      await _requestPermissions();
      
      // Initialize Bluetooth
      await _initializeBluetooth();
      
      // Initialize WiFi
      await _initializeWiFi();
      
      return true;
    } catch (e) {
      print('IoT Service initialization failed: $e');
      return false;
    }
  }

  /// Request necessary permissions
  Future<void> _requestPermissions() async {
    await [
      Permission.bluetooth,
      Permission.bluetoothConnect,
      Permission.bluetoothScan,
      Permission.location,
      Permission.nearbyWifiDevices,
    ].request();
  }

  /// Initialize Bluetooth
  Future<void> _initializeBluetooth() async {
    try {
      final isEnabled = await FlutterBluetoothSerial.instance.isEnabled;
      if (!isEnabled!) {
        await FlutterBluetoothSerial.instance.requestEnable();
      }
    } catch (e) {
      print('Bluetooth initialization failed: $e');
    }
  }

  /// Initialize WiFi
  Future<void> _initializeWiFi() async {
    try {
      _isWiFiConnected = await WiFiForIoTPlugin.isConnected();
      if (_isWiFiConnected) {
        _connectedSSID = await NetworkInfo().getWifiName();
      }
    } catch (e) {
      print('WiFi initialization failed: $e');
    }
  }

  /// Scan for nearby IoT devices
  Future<List<IoTDevice>> scanForDevices({Duration timeout = const Duration(seconds: 10)}) async {
    final devices = <IoTDevice>[];
    
    try {
      // Scan Bluetooth devices
      final bluetoothDevices = await _scanBluetoothDevices(timeout);
      devices.addAll(bluetoothDevices);
      
      // Scan WiFi devices
      final wifiDevices = await _scanWiFiDevices(timeout);
      devices.addAll(wifiDevices);
      
      return devices;
    } catch (e) {
      print('Device scan failed: $e');
      return devices;
    }
  }

  /// Scan for Bluetooth devices
  Future<List<IoTDevice>> _scanBluetoothDevices(Duration timeout) async {
    final devices = <IoTDevice>[];
    
    try {
      final discoveredDevices = await FlutterBluetoothSerial.instance.getBondedDevices();
      
      for (final device in discoveredDevices) {
        if (_isBarberIoTDevice(device.name ?? '')) {
          devices.add(IoTDevice(
            id: device.address,
            name: device.name ?? 'Unknown Device',
            type: IoTDeviceType.bluetooth,
            connectionType: 'Bluetooth',
            capabilities: _getDeviceCapabilities(device.name ?? ''),
            isConnected: false,
            signalStrength: -50, // Mock signal strength
            batteryLevel: null,
            deviceInfo: {
              'address': device.address,
              'bonded': device.isBonded,
              'type': device.type.toString(),
            },
          ));
        }
      }
    } catch (e) {
      print('Bluetooth scan failed: $e');
    }
    
    return devices;
  }

  /// Scan for WiFi IoT devices
  Future<List<IoTDevice>> _scanWiFiDevices(Duration timeout) async {
    final devices = <IoTDevice>[];
    
    try {
      final wifiNetworks = await WiFiForIoTPlugin.loadWifiList();
      
      for (final network in wifiNetworks) {
        if (_isBarberIoTNetwork(network.ssid ?? '')) {
          devices.add(IoTDevice(
            id: network.bssid ?? network.ssid ?? '',
            name: network.ssid ?? 'Unknown Network',
            type: IoTDeviceType.wifi,
            connectionType: 'WiFi',
            capabilities: ['remote_control', 'status_monitoring'],
            isConnected: false,
            signalStrength: network.level ?? -70,
            batteryLevel: null,
            deviceInfo: {
              'ssid': network.ssid,
              'bssid': network.bssid,
              'frequency': network.frequency,
              'capabilities': network.capabilities,
            },
          ));
        }
      }
    } catch (e) {
      print('WiFi scan failed: $e');
    }
    
    return devices;
  }

  /// Check if device is a barber IoT device
  bool _isBarberIoTDevice(String deviceName) {
    final barberDevicePatterns = [
      'BarberChair',
      'SmartMirror',
      'ClipperPro',
      'WashStation',
      'BarberTools',
      'SalonEquip',
    ];
    
    return barberDevicePatterns.any(
      (pattern) => deviceName.toLowerCase().contains(pattern.toLowerCase())
    );
  }

  /// Check if network is a barber IoT network
  bool _isBarberIoTNetwork(String ssid) {
    final barberNetworkPatterns = [
      'BarberShop_',
      'Salon_IoT_',
      'SmartBarber_',
      'BarberEquip_',
    ];
    
    return barberNetworkPatterns.any(
      (pattern) => ssid.toLowerCase().contains(pattern.toLowerCase())
    );
  }

  /// Get device capabilities based on name
  List<String> _getDeviceCapabilities(String deviceName) {
    final name = deviceName.toLowerCase();
    
    if (name.contains('chair')) {
      return ['position_control', 'massage', 'heating', 'status_monitoring'];
    } else if (name.contains('mirror')) {
      return ['display', 'lighting_control', 'camera', 'voice_control'];
    } else if (name.contains('clipper') || name.contains('trimmer')) {
      return ['battery_monitoring', 'usage_tracking', 'maintenance_alerts'];
    } else if (name.contains('wash')) {
      return ['water_control', 'temperature_control', 'pressure_control'];
    }
    
    return ['basic_control', 'status_monitoring'];
  }

  /// Connect to IoT device
  Future<bool> connectToDevice(IoTDevice device) async {
    try {
      bool connected = false;
      
      switch (device.type) {
        case IoTDeviceType.bluetooth:
          connected = await _connectBluetooth(device);
          break;
        case IoTDeviceType.wifi:
          connected = await _connectWiFi(device);
          break;
      }
      
      if (connected) {
        device.isConnected = true;
        _connectedDevices[device.id] = device;
        
        _deviceEventController.add(IoTDeviceEvent(
          type: IoTEventType.connected,
          device: device,
          timestamp: DateTime.now(),
        ));
        
        // Start monitoring device
        _startDeviceMonitoring(device);
      }
      
      return connected;
    } catch (e) {
      print('Device connection failed: $e');
      return false;
    }
  }

  /// Connect via Bluetooth
  Future<bool> _connectBluetooth(IoTDevice device) async {
    try {
      _bluetoothConnection = await BluetoothConnection.toAddress(device.id);
      _isBluetoothConnected = true;
      
      // Listen for data
      _bluetoothSubscription = _bluetoothConnection!.input!.listen(
        (data) => _handleBluetoothData(device, data),
        onError: (error) => _handleConnectionError(device, error),
        onDone: () => _handleConnectionClosed(device),
      );
      
      return true;
    } catch (e) {
      print('Bluetooth connection failed: $e');
      return false;
    }
  }

  /// Connect via WiFi
  Future<bool> _connectWiFi(IoTDevice device) async {
    try {
      final deviceInfo = device.deviceInfo;
      final ssid = deviceInfo['ssid'] as String?;
      
      if (ssid == null) return false;
      
      // Connect to WiFi network
      final connected = await WiFiForIoTPlugin.connect(
        ssid,
        password: '', // Would need proper password handling
        security: NetworkSecurity.NONE,
      );
      
      if (connected) {
        _isWiFiConnected = true;
        _connectedSSID = ssid;
      }
      
      return connected;
    } catch (e) {
      print('WiFi connection failed: $e');
      return false;
    }
  }

  /// Disconnect from device
  Future<void> disconnectFromDevice(String deviceId) async {
    try {
      final device = _connectedDevices[deviceId];
      if (device == null) return;
      
      switch (device.type) {
        case IoTDeviceType.bluetooth:
          await _bluetoothConnection?.close();
          await _bluetoothSubscription?.cancel();
          _isBluetoothConnected = false;
          break;
        case IoTDeviceType.wifi:
          await WiFiForIoTPlugin.disconnect();
          _isWiFiConnected = false;
          _connectedSSID = null;
          break;
      }
      
      device.isConnected = false;
      _connectedDevices.remove(deviceId);
      
      _deviceEventController.add(IoTDeviceEvent(
        type: IoTEventType.disconnected,
        device: device,
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      print('Device disconnection failed: $e');
    }
  }

  /// Send command to IoT device
  Future<bool> sendCommand(String deviceId, IoTCommand command) async {
    try {
      final device = _connectedDevices[deviceId];
      if (device == null || !device.isConnected) return false;
      
      final commandData = _buildCommandData(command);
      
      switch (device.type) {
        case IoTDeviceType.bluetooth:
          return await _sendBluetoothCommand(commandData);
        case IoTDeviceType.wifi:
          return await _sendWiFiCommand(device, commandData);
      }
      
      return false;
    } catch (e) {
      print('Command send failed: $e');
      return false;
    }
  }

  /// Build command data
  Map<String, dynamic> _buildCommandData(IoTCommand command) {
    return {
      'command': command.action,
      'parameters': command.parameters,
      'timestamp': DateTime.now().toIso8601String(),
      'id': command.id,
    };
  }

  /// Send Bluetooth command
  Future<bool> _sendBluetoothCommand(Map<String, dynamic> commandData) async {
    try {
      if (_bluetoothConnection == null) return false;
      
      final data = utf8.encode(json.encode(commandData));
      _bluetoothConnection!.output.add(data);
      await _bluetoothConnection!.output.allSent;
      
      return true;
    } catch (e) {
      print('Bluetooth command send failed: $e');
      return false;
    }
  }

  /// Send WiFi command
  Future<bool> _sendWiFiCommand(IoTDevice device, Map<String, dynamic> commandData) async {
    try {
      // This would typically involve HTTP requests to the device's API
      // For now, return success
      return true;
    } catch (e) {
      print('WiFi command send failed: $e');
      return false;
    }
  }

  /// Handle Bluetooth data
  void _handleBluetoothData(IoTDevice device, Uint8List data) {
    try {
      final jsonString = utf8.decode(data);
      final responseData = json.decode(jsonString) as Map<String, dynamic>;
      
      _deviceEventController.add(IoTDeviceEvent(
        type: IoTEventType.dataReceived,
        device: device,
        data: responseData,
        timestamp: DateTime.now(),
      ));
      
      // Update device status
      _updateDeviceStatus(device, responseData);
    } catch (e) {
      print('Bluetooth data handling failed: $e');
    }
  }

  /// Handle connection error
  void _handleConnectionError(IoTDevice device, dynamic error) {
    print('Connection error for ${device.name}: $error');
    
    _deviceEventController.add(IoTDeviceEvent(
      type: IoTEventType.error,
      device: device,
      error: error.toString(),
      timestamp: DateTime.now(),
    ));
  }

  /// Handle connection closed
  void _handleConnectionClosed(IoTDevice device) {
    print('Connection closed for ${device.name}');
    
    device.isConnected = false;
    _connectedDevices.remove(device.id);
    
    _deviceEventController.add(IoTDeviceEvent(
      type: IoTEventType.disconnected,
      device: device,
      timestamp: DateTime.now(),
    ));
  }

  /// Start device monitoring
  void _startDeviceMonitoring(IoTDevice device) {
    Timer.periodic(Duration(seconds: 30), (timer) {
      if (!_connectedDevices.containsKey(device.id)) {
        timer.cancel();
        return;
      }
      
      // Send status request
      sendCommand(device.id, IoTCommand(
        id: 'status_${DateTime.now().millisecondsSinceEpoch}',
        action: 'get_status',
        parameters: {},
      ));
    });
  }

  /// Update device status
  void _updateDeviceStatus(IoTDevice device, Map<String, dynamic> statusData) {
    if (statusData.containsKey('battery_level')) {
      device.batteryLevel = statusData['battery_level'] as int?;
    }
    
    if (statusData.containsKey('signal_strength')) {
      device.signalStrength = statusData['signal_strength'] as int?;
    }
    
    // Update last seen
    device.lastSeen = DateTime.now();
    
    _deviceEventController.add(IoTDeviceEvent(
      type: IoTEventType.statusUpdated,
      device: device,
      data: statusData,
      timestamp: DateTime.now(),
    ));
  }

  /// Control barber chair
  Future<bool> controlBarberChair(String deviceId, BarberChairCommand command) async {
    final iotCommand = IoTCommand(
      id: 'chair_${DateTime.now().millisecondsSinceEpoch}',
      action: 'chair_control',
      parameters: {
        'action': command.action,
        'position': command.position,
        'speed': command.speed,
        'massage_intensity': command.massageIntensity,
        'heating_level': command.heatingLevel,
      },
    );
    
    return await sendCommand(deviceId, iotCommand);
  }

  /// Control smart mirror
  Future<bool> controlSmartMirror(String deviceId, SmartMirrorCommand command) async {
    final iotCommand = IoTCommand(
      id: 'mirror_${DateTime.now().millisecondsSinceEpoch}',
      action: 'mirror_control',
      parameters: {
        'action': command.action,
        'brightness': command.brightness,
        'color_temperature': command.colorTemperature,
        'display_content': command.displayContent,
        'voice_enabled': command.voiceEnabled,
      },
    );
    
    return await sendCommand(deviceId, iotCommand);
  }

  /// Control wash station
  Future<bool> controlWashStation(String deviceId, WashStationCommand command) async {
    final iotCommand = IoTCommand(
      id: 'wash_${DateTime.now().millisecondsSinceEpoch}',
      action: 'wash_control',
      parameters: {
        'action': command.action,
        'water_temperature': command.waterTemperature,
        'water_pressure': command.waterPressure,
        'shampoo_dispense': command.shampooDispense,
        'duration': command.duration,
      },
    );
    
    return await sendCommand(deviceId, iotCommand);
  }

  /// Get device analytics
  Future<Map<String, dynamic>> getDeviceAnalytics(String deviceId) async {
    try {
      final device = _connectedDevices[deviceId];
      if (device == null) return {};
      
      // Request analytics data
      final success = await sendCommand(deviceId, IoTCommand(
        id: 'analytics_${DateTime.now().millisecondsSinceEpoch}',
        action: 'get_analytics',
        parameters: {'period': '24h'},
      ));
      
      if (success) {
        // Return mock analytics data
        return {
          'usage_hours': 8.5,
          'energy_consumption': 12.3,
          'maintenance_score': 85,
          'efficiency_rating': 92,
          'last_maintenance': '2024-01-15',
          'next_maintenance': '2024-02-15',
        };
      }
      
      return {};
    } catch (e) {
      print('Analytics request failed: $e');
      return {};
    }
  }

  /// Dispose IoT service
  Future<void> dispose() async {
    try {
      // Disconnect all devices
      for (final deviceId in _connectedDevices.keys.toList()) {
        await disconnectFromDevice(deviceId);
      }
      
      // Close streams
      await _deviceEventController.close();
      
      // Cancel subscriptions
      await _bluetoothSubscription?.cancel();
    } catch (e) {
      print('IoT Service disposal failed: $e');
    }
  }
}

// Data Models

enum IoTDeviceType { bluetooth, wifi }

enum IoTEventType {
  connected,
  disconnected,
  dataReceived,
  statusUpdated,
  error,
}

class IoTDevice {
  final String id;
  final String name;
  final IoTDeviceType type;
  final String connectionType;
  final List<String> capabilities;
  bool isConnected;
  int? signalStrength;
  int? batteryLevel;
  DateTime? lastSeen;
  final Map<String, dynamic> deviceInfo;

  IoTDevice({
    required this.id,
    required this.name,
    required this.type,
    required this.connectionType,
    required this.capabilities,
    required this.isConnected,
    this.signalStrength,
    this.batteryLevel,
    this.lastSeen,
    required this.deviceInfo,
  });
}

class IoTDeviceEvent {
  final IoTEventType type;
  final IoTDevice device;
  final Map<String, dynamic>? data;
  final String? error;
  final DateTime timestamp;

  IoTDeviceEvent({
    required this.type,
    required this.device,
    this.data,
    this.error,
    required this.timestamp,
  });
}

class IoTCommand {
  final String id;
  final String action;
  final Map<String, dynamic> parameters;

  IoTCommand({
    required this.id,
    required this.action,
    required this.parameters,
  });
}

// Specific Device Commands

class BarberChairCommand {
  final String action; // 'recline', 'upright', 'massage_on', 'massage_off', 'heat_on', 'heat_off'
  final int? position; // 0-100
  final int? speed; // 1-10
  final int? massageIntensity; // 1-10
  final int? heatingLevel; // 1-5

  BarberChairCommand({
    required this.action,
    this.position,
    this.speed,
    this.massageIntensity,
    this.heatingLevel,
  });
}

class SmartMirrorCommand {
  final String action; // 'display_on', 'display_off', 'adjust_lighting', 'show_content'
  final int? brightness; // 0-100
  final int? colorTemperature; // 2700-6500K
  final Map<String, dynamic>? displayContent;
  final bool? voiceEnabled;

  SmartMirrorCommand({
    required this.action,
    this.brightness,
    this.colorTemperature,
    this.displayContent,
    this.voiceEnabled,
  });
}

class WashStationCommand {
  final String action; // 'start_wash', 'stop_wash', 'adjust_temperature', 'adjust_pressure'
  final int? waterTemperature; // 20-45 Celsius
  final int? waterPressure; // 1-10
  final bool? shampooDispense;
  final int? duration; // seconds

  WashStationCommand({
    required this.action,
    this.waterTemperature,
    this.waterPressure,
    this.shampooDispense,
    this.duration,
  });
}
