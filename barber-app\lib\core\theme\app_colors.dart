import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - Professional Theme
  static const Color primary = Color(0xFF2C3E50);
  static const Color primaryDark = Color(0xFF1A252F);
  static const Color primaryLight = Color(0xFF34495E);
  
  // Secondary Colors - Gold Accent
  static const Color secondary = Color(0xFFD4AF37);
  static const Color secondaryDark = Color(0xFFB8941F);
  static const Color secondaryLight = Color(0xFFE6C547);
  
  // Accent Colors
  static const Color accent = Color(0xFF3498DB);
  static const Color accentDark = Color(0xFF2980B9);
  static const Color accentLight = Color(0xFF5DADE2);
  
  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF95A5A6);
  static const Color greyLight = Color(0xFFF8F9FA);
  static const Color greyDark = Color(0xFF7F8C8D);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF2C3E50);
  static const Color textSecondary = Color(0xFF7F8C8D);
  static const Color textLight = Color(0xFFBDC3C7);
  static const Color textWhite = Color(0xFFFFFFFF);
  static const Color textGold = Color(0xFFD4AF37);
  
  // Background Colors
  static const Color background = Color(0xFFF8F9FA);
  static const Color backgroundDark = Color(0xFF1A1A1A);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF2C2C2C);
  
  // Status Colors
  static const Color success = Color(0xFF27AE60);
  static const Color successLight = Color(0xFF2ECC71);
  static const Color successDark = Color(0xFF229954);
  
  static const Color error = Color(0xFFE74C3C);
  static const Color errorLight = Color(0xFFEC7063);
  static const Color errorDark = Color(0xFFC0392B);
  
  static const Color warning = Color(0xFFF39C12);
  static const Color warningLight = Color(0xFFF4D03F);
  static const Color warningDark = Color(0xFFD68910);
  
  static const Color info = Color(0xFF3498DB);
  static const Color infoLight = Color(0xFF5DADE2);
  static const Color infoDark = Color(0xFF2980B9);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderLight = Color(0xFFF0F0F0);
  static const Color borderDark = Color(0xFFBDBDBD);
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x33000000);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient goldGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, successLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient errorGradient = LinearGradient(
    colors: [error, errorLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient warningGradient = LinearGradient(
    colors: [warning, warningLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient infoGradient = LinearGradient(
    colors: [info, infoLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Professional Theme Gradients
  static const LinearGradient professionalGradient = LinearGradient(
    colors: [Color(0xFF2C3E50), Color(0xFF34495E), Color(0xFFD4AF37)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient darkGradient = LinearGradient(
    colors: [Color(0xFF1A252F), Color(0xFF2C3E50)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // Barber Status Colors
  static const Color online = Color(0xFF27AE60);
  static const Color offline = Color(0xFF95A5A6);
  static const Color busy = Color(0xFFE74C3C);
  static const Color onBreak = Color(0xFFF39C12);
  
  // Booking Status Colors
  static const Color bookingPending = Color(0xFFF39C12);
  static const Color bookingAccepted = Color(0xFF3498DB);
  static const Color bookingInProgress = Color(0xFF9B59B6);
  static const Color bookingCompleted = Color(0xFF27AE60);
  static const Color bookingCancelled = Color(0xFFE74C3C);
  static const Color bookingRejected = Color(0xFF95A5A6);
  
  // Earnings Colors
  static const Color earnings = Color(0xFF27AE60);
  static const Color commission = Color(0xFFE74C3C);
  static const Color bonus = Color(0xFFD4AF37);
  static const Color tip = Color(0xFF9B59B6);
  
  // Rating Colors
  static const Color rating = Color(0xFFFFB300);
  static const Color ratingLight = Color(0xFFFFC947);
  static const Color ratingDark = Color(0xFFFF8F00);
  
  // Service Category Colors
  static const Color categoryHaircut = Color(0xFF3F51B5);
  static const Color categoryBeard = Color(0xFF795548);
  static const Color categorySkincare = Color(0xFFE91E63);
  static const Color categoryPackage = Color(0xFF9C27B0);
  
  // Time Slot Colors
  static const Color timeSlotAvailable = Color(0xFF27AE60);
  static const Color timeSlotBooked = Color(0xFFE74C3C);
  static const Color timeSlotBreak = Color(0xFFF39C12);
  static const Color timeSlotUnavailable = Color(0xFF95A5A6);
  
  // Chart Colors
  static const List<Color> chartColors = [
    Color(0xFF3498DB),
    Color(0xFF27AE60),
    Color(0xFFF39C12),
    Color(0xFFE74C3C),
    Color(0xFF9B59B6),
    Color(0xFFD4AF37),
    Color(0xFF1ABC9C),
    Color(0xFFE67E22),
  ];
  
  // Social Media Colors
  static const Color facebook = Color(0xFF1877F2);
  static const Color instagram = Color(0xFFE4405F);
  static const Color whatsapp = Color(0xFF25D366);
  
  // Utility Methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }
  
  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }
  
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'online':
        return online;
      case 'offline':
        return offline;
      case 'busy':
        return busy;
      case 'break':
        return onBreak;
      default:
        return offline;
    }
  }
  
  static Color getBookingStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return bookingPending;
      case 'accepted':
        return bookingAccepted;
      case 'in_progress':
        return bookingInProgress;
      case 'completed':
        return bookingCompleted;
      case 'cancelled':
        return bookingCancelled;
      case 'rejected':
        return bookingRejected;
      default:
        return bookingPending;
    }
  }
  
  static Color getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'haircut':
        return categoryHaircut;
      case 'beard':
        return categoryBeard;
      case 'skincare':
        return categorySkincare;
      case 'package':
        return categoryPackage;
      default:
        return primary;
    }
  }
  
  static MaterialColor createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map<int, Color> swatch = {};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    
    return MaterialColor(color.value, swatch);
  }
  
  // Color Schemes
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primary,
    onPrimary: white,
    secondary: secondary,
    onSecondary: textPrimary,
    error: error,
    onError: white,
    background: background,
    onBackground: textPrimary,
    surface: surface,
    onSurface: textPrimary,
  );
  
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: primaryLight,
    onPrimary: white,
    secondary: secondaryLight,
    onSecondary: textPrimary,
    error: errorLight,
    onError: white,
    background: backgroundDark,
    onBackground: white,
    surface: surfaceDark,
    onSurface: white,
  );
}
