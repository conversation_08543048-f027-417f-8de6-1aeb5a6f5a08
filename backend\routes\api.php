<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\SettingsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes (no authentication required)
Route::prefix('v1')->group(function () {
    
    // Settings routes for mobile apps
    Route::prefix('settings')->group(function () {
        Route::get('/public', [SettingsController::class, 'getPublicSettings']);
        Route::get('/app-config', [SettingsController::class, 'getAppConfig']);
        Route::get('/features', [SettingsController::class, 'getFeatureFlags']);
        Route::get('/booking-config', [SettingsController::class, 'getBookingConfig']);
        Route::get('/vip-config', [SettingsController::class, 'getVipConfig']);
        Route::get('/payment-config', [SettingsController::class, 'getPaymentConfig']);
        Route::get('/maintenance', [SettingsController::class, 'checkMaintenance']);
    });

    // Authentication routes
    Route::prefix('auth')->group(function () {
        // User authentication
        Route::prefix('user')->group(function () {
            Route::post('/register', [UserAuthController::class, 'register']);
            Route::post('/login', [UserAuthController::class, 'login']);
            Route::post('/verify-phone', [UserAuthController::class, 'verifyPhone']);
            Route::post('/resend-otp', [UserAuthController::class, 'resendOtp']);
            Route::post('/forgot-password', [UserAuthController::class, 'forgotPassword']);
            Route::post('/reset-password', [UserAuthController::class, 'resetPassword']);
        });

        // Barber authentication
        Route::prefix('barber')->group(function () {
            Route::post('/register', [BarberAuthController::class, 'register']);
            Route::post('/login', [BarberAuthController::class, 'login']);
            Route::post('/verify-phone', [BarberAuthController::class, 'verifyPhone']);
            Route::post('/resend-otp', [BarberAuthController::class, 'resendOtp']);
            Route::post('/forgot-password', [BarberAuthController::class, 'forgotPassword']);
            Route::post('/reset-password', [BarberAuthController::class, 'resetPassword']);
        });

        // Admin authentication
        Route::prefix('admin')->group(function () {
            Route::post('/login', [AdminAuthController::class, 'login']);
            Route::post('/forgot-password', [AdminAuthController::class, 'forgotPassword']);
            Route::post('/reset-password', [AdminAuthController::class, 'resetPassword']);
        });
    });

    // Cities and locations
    Route::prefix('locations')->group(function () {
        Route::get('/cities', [LocationController::class, 'getCities']);
        Route::get('/cities/{id}/areas', [LocationController::class, 'getCityAreas']);
        Route::get('/governorates', [LocationController::class, 'getGovernorates']);
    });

    // Services
    Route::prefix('services')->group(function () {
        Route::get('/', [ServiceController::class, 'index']);
        Route::get('/categories', [ServiceController::class, 'getCategories']);
        Route::get('/{id}', [ServiceController::class, 'show']);
    });

    // Store (public products)
    Route::prefix('store')->group(function () {
        Route::get('/products', [StoreController::class, 'getProducts']);
        Route::get('/categories', [StoreController::class, 'getCategories']);
        Route::get('/products/{id}', [StoreController::class, 'getProduct']);
        Route::get('/featured', [StoreController::class, 'getFeaturedProducts']);
    });
});

// Protected routes (authentication required)
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    
    // User routes
    Route::prefix('user')->middleware('user')->group(function () {
        Route::get('/profile', [UserController::class, 'getProfile']);
        Route::put('/profile', [UserController::class, 'updateProfile']);
        Route::post('/logout', [UserAuthController::class, 'logout']);
        Route::delete('/account', [UserController::class, 'deleteAccount']);
        
        // Bookings
        Route::prefix('bookings')->group(function () {
            Route::get('/', [BookingController::class, 'getUserBookings']);
            Route::post('/', [BookingController::class, 'createBooking']);
            Route::get('/{id}', [BookingController::class, 'getBooking']);
            Route::put('/{id}/cancel', [BookingController::class, 'cancelBooking']);
            Route::post('/{id}/rate', [BookingController::class, 'rateBooking']);
        });

        // VIP subscription
        Route::prefix('vip')->group(function () {
            Route::get('/subscription', [VipController::class, 'getSubscription']);
            Route::post('/subscribe', [VipController::class, 'subscribe']);
            Route::put('/cancel', [VipController::class, 'cancelSubscription']);
        });

        // Wallet
        Route::prefix('wallet')->group(function () {
            Route::get('/balance', [WalletController::class, 'getBalance']);
            Route::get('/transactions', [WalletController::class, 'getTransactions']);
            Route::post('/add-funds', [WalletController::class, 'addFunds']);
        });

        // Loyalty points
        Route::prefix('loyalty')->group(function () {
            Route::get('/points', [LoyaltyController::class, 'getPoints']);
            Route::get('/history', [LoyaltyController::class, 'getHistory']);
            Route::post('/redeem', [LoyaltyController::class, 'redeemPoints']);
        });
    });

    // Barber routes
    Route::prefix('barber')->middleware('barber')->group(function () {
        Route::get('/profile', [BarberController::class, 'getProfile']);
        Route::put('/profile', [BarberController::class, 'updateProfile']);
        Route::post('/logout', [BarberAuthController::class, 'logout']);
        
        // Availability
        Route::prefix('availability')->group(function () {
            Route::get('/', [BarberController::class, 'getAvailability']);
            Route::put('/', [BarberController::class, 'updateAvailability']);
            Route::put('/toggle', [BarberController::class, 'toggleAvailability']);
        });

        // Bookings
        Route::prefix('bookings')->group(function () {
            Route::get('/', [BarberBookingController::class, 'getBookings']);
            Route::get('/{id}', [BarberBookingController::class, 'getBooking']);
            Route::put('/{id}/accept', [BarberBookingController::class, 'acceptBooking']);
            Route::put('/{id}/reject', [BarberBookingController::class, 'rejectBooking']);
            Route::put('/{id}/start', [BarberBookingController::class, 'startService']);
            Route::put('/{id}/complete', [BarberBookingController::class, 'completeService']);
        });

        // Wallet
        Route::prefix('wallet')->group(function () {
            Route::get('/balance', [BarberWalletController::class, 'getBalance']);
            Route::get('/transactions', [BarberWalletController::class, 'getTransactions']);
            Route::post('/withdraw', [BarberWalletController::class, 'requestWithdrawal']);
        });

        // Statistics
        Route::get('/stats', [BarberController::class, 'getStatistics']);
    });

    // Admin routes
    Route::prefix('admin')->middleware('admin')->group(function () {
        Route::post('/logout', [AdminAuthController::class, 'logout']);
        
        // Settings management
        Route::prefix('settings')->group(function () {
            Route::get('/', [SettingsController::class, 'getAllSettings']);
            Route::put('/', [SettingsController::class, 'updateSettings']);
            Route::post('/toggle/{feature}', [SettingsController::class, 'toggleFeature']);
            Route::get('/export', [SettingsController::class, 'exportSettings']);
            Route::post('/clear-cache', [SettingsController::class, 'clearCache']);
        });

        // User management
        Route::prefix('users')->group(function () {
            Route::get('/', [AdminUserController::class, 'index']);
            Route::get('/{id}', [AdminUserController::class, 'show']);
            Route::put('/{id}', [AdminUserController::class, 'update']);
            Route::put('/{id}/ban', [AdminUserController::class, 'ban']);
            Route::put('/{id}/unban', [AdminUserController::class, 'unban']);
            Route::delete('/{id}', [AdminUserController::class, 'destroy']);
        });

        // Barber management
        Route::prefix('barbers')->group(function () {
            Route::get('/', [AdminBarberController::class, 'index']);
            Route::get('/{id}', [AdminBarberController::class, 'show']);
            Route::put('/{id}', [AdminBarberController::class, 'update']);
            Route::put('/{id}/verify', [AdminBarberController::class, 'verify']);
            Route::put('/{id}/reject', [AdminBarberController::class, 'reject']);
            Route::put('/{id}/ban', [AdminBarberController::class, 'ban']);
            Route::put('/{id}/unban', [AdminBarberController::class, 'unban']);
        });

        // Booking management
        Route::prefix('bookings')->group(function () {
            Route::get('/', [AdminBookingController::class, 'index']);
            Route::get('/{id}', [AdminBookingController::class, 'show']);
            Route::put('/{id}/cancel', [AdminBookingController::class, 'cancel']);
            Route::get('/stats/overview', [AdminBookingController::class, 'getOverviewStats']);
        });

        // Financial management
        Route::prefix('finance')->group(function () {
            Route::get('/overview', [AdminFinanceController::class, 'getOverview']);
            Route::get('/withdrawals', [AdminFinanceController::class, 'getWithdrawals']);
            Route::put('/withdrawals/{id}/approve', [AdminFinanceController::class, 'approveWithdrawal']);
            Route::put('/withdrawals/{id}/reject', [AdminFinanceController::class, 'rejectWithdrawal']);
            Route::get('/reports', [AdminFinanceController::class, 'getReports']);
        });

        // Store management
        Route::prefix('store')->group(function () {
            Route::get('/products', [AdminStoreController::class, 'getProducts']);
            Route::post('/products', [AdminStoreController::class, 'createProduct']);
            Route::put('/products/{id}', [AdminStoreController::class, 'updateProduct']);
            Route::delete('/products/{id}', [AdminStoreController::class, 'deleteProduct']);
            Route::get('/categories', [AdminStoreController::class, 'getCategories']);
            Route::post('/categories', [AdminStoreController::class, 'createCategory']);
        });

        // Analytics and reports
        Route::prefix('analytics')->group(function () {
            Route::get('/dashboard', [AnalyticsController::class, 'getDashboardData']);
            Route::get('/users', [AnalyticsController::class, 'getUserAnalytics']);
            Route::get('/barbers', [AnalyticsController::class, 'getBarberAnalytics']);
            Route::get('/bookings', [AnalyticsController::class, 'getBookingAnalytics']);
            Route::get('/revenue', [AnalyticsController::class, 'getRevenueAnalytics']);
        });
    });
});

// Webhook routes (for payment gateways)
Route::prefix('webhooks')->group(function () {
    Route::post('/paymob', [PaymentWebhookController::class, 'paymob']);
    Route::post('/stripe', [PaymentWebhookController::class, 'stripe']);
});

// Health check
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now(),
        'version' => '1.0.0'
    ]);
});
