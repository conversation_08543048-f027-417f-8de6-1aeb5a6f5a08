# Security Hardening Configuration for <PERSON> App
# This file contains comprehensive security configurations

apiVersion: v1
kind: Namespace
metadata:
  name: security
  labels:
    name: security
    security.policy: "strict"
---
# Network Policies
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: barber-app
spec:
  podSelector: {}
  policyTypes:
    - Ingress
    - Egress

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-backend-to-db
  namespace: barber-app
spec:
  podSelector:
    matchLabels:
      app: barber-app-backend
  policyTypes:
    - Egress
  egress:
    - to:
        - podSelector:
            matchLabels:
              app: mysql
      ports:
        - protocol: TCP
          port: 3306

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-backend-to-redis
  namespace: barber-app
spec:
  podSelector:
    matchLabels:
      app: barber-app-backend
  policyTypes:
    - Egress
  egress:
    - to:
        - podSelector:
            matchLabels:
              app: redis
      ports:
        - protocol: TCP
          port: 6379

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-ingress-to-backend
  namespace: barber-app
spec:
  podSelector:
    matchLabels:
      app: barber-app-backend
  policyTypes:
    - Ingress
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      ports:
        - protocol: TCP
          port: 8080

---
# Pod Security Policy
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: barber-app-psp
  namespace: barber-app
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'MustRunAsNonRoot'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  readOnlyRootFilesystem: false
  seLinux:
    rule: 'RunAsAny'

---
# Security Context Constraints
apiVersion: security.openshift.io/v1
kind: SecurityContextConstraints
metadata:
  name: barber-app-scc
allowHostDirVolumePlugin: false
allowHostIPC: false
allowHostNetwork: false
allowHostPID: false
allowHostPorts: false
allowPrivilegedContainer: false
allowedCapabilities: null
defaultAddCapabilities: null
requiredDropCapabilities:
  - KILL
  - MKNOD
  - SETUID
  - SETGID
fsGroup:
  type: MustRunAs
  ranges:
    - min: 1
      max: 65535
readOnlyRootFilesystem: false
runAsUser:
  type: MustRunAsNonRoot
seLinuxContext:
  type: MustRunAs
supplementalGroups:
  type: MustRunAs
  ranges:
    - min: 1
      max: 65535
volumes:
  - configMap
  - downwardAPI
  - emptyDir
  - persistentVolumeClaim
  - projected
  - secret

---
# RBAC Configuration
apiVersion: v1
kind: ServiceAccount
metadata:
  name: barber-app-backend
  namespace: barber-app

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: barber-app-backend-role
  namespace: barber-app
rules:
  - apiGroups: [""]
    resources: ["configmaps", "secrets"]
    verbs: ["get", "list"]
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: barber-app-backend-binding
  namespace: barber-app
subjects:
  - kind: ServiceAccount
    name: barber-app-backend
    namespace: barber-app
roleRef:
  kind: Role
  name: barber-app-backend-role
  apiGroup: rbac.authorization.k8s.io

---
# Secrets Management
apiVersion: v1
kind: Secret
metadata:
  name: barber-app-secrets
  namespace: barber-app
  annotations:
    kubernetes.io/description: "Encrypted secrets for Barber App"
type: Opaque
data:
  # All values are base64 encoded
  DB_PASSWORD: YmFyYmVyLWRiLXBhc3N3b3JkLTIwMjQ=
  REDIS_PASSWORD: cmVkaXMtcGFzc3dvcmQtMjAyNA==
  JWT_SECRET: and0LXNlY3JldC1rZXktMjAyNC1iYXJiZXItYXBw
  STRIPE_SECRET: c2tfdGVzdF8xMjM0NTY3ODkwYWJjZGVmZ2hpams=
  PAYMOB_SECRET: cGF5bW9iLXNlY3JldC1rZXktMjAyNA==
  AGORA_APP_ID: YWdvcmEtYXBwLWlkLTIwMjQ=
  AGORA_APP_CERTIFICATE: YWdvcmEtYXBwLWNlcnRpZmljYXRlLTIwMjQ=
  FIREBASE_PRIVATE_KEY: ZmlyZWJhc2UtcHJpdmF0ZS1rZXktMjAyNA==
  ENCRYPTION_KEY: ZW5jcnlwdGlvbi1rZXktMjAyNC1iYXJiZXI=

---
# Sealed Secrets Controller (for GitOps)
apiVersion: v1
kind: ServiceAccount
metadata:
  name: sealed-secrets-controller
  namespace: kube-system

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: secrets-unsealer
rules:
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "list", "create", "update", "delete"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "patch"]
  - apiGroups: ["bitnami.com"]
    resources: ["sealedsecrets"]
    verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: sealed-secrets-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: secrets-unsealer
subjects:
  - kind: ServiceAccount
    name: sealed-secrets-controller
    namespace: kube-system

---
# Security Scanning with Falco
apiVersion: v1
kind: ConfigMap
metadata:
  name: falco-config
  namespace: security
data:
  falco.yaml: |
    rules_file:
      - /etc/falco/falco_rules.yaml
      - /etc/falco/falco_rules.local.yaml
      - /etc/falco/k8s_audit_rules.yaml
      - /etc/falco/rules.d

    time_format_iso_8601: true
    json_output: true
    json_include_output_property: true

    log_stderr: true
    log_syslog: true
    log_level: info

    priority: debug

    buffered_outputs: false

    syscall_event_drops:
      actions:
        - log
        - alert
      rate: 0.03333
      max_burst: 1000

    outputs:
      rate: 1
      max_burst: 1000

    syslog_output:
      enabled: true

    file_output:
      enabled: false
      keep_alive: false
      filename: ./events.txt

    stdout_output:
      enabled: true

    webserver:
      enabled: true
      listen_port: 8765
      k8s_healthz_endpoint: /healthz
      ssl_enabled: false
      ssl_certificate: /etc/ssl/falco/falco.pem

    grpc:
      enabled: false
      bind_address: "0.0.0.0:5060"
      threadiness: 8

    grpc_output:
      enabled: false

  custom_rules.yaml: |
    - rule: Barber App Suspicious Network Activity
      desc: Detect suspicious network activity in Barber App
      condition: >
        (k8s_audit and ka.verb in (create, update, patch) and
         ka.target.resource=networkpolicies and
         ka.target.namespace=barber-app)
      output: >
        Suspicious network policy change in Barber App
        (user=%ka.user.name verb=%ka.verb resource=%ka.target.resource
         namespace=%ka.target.namespace)
      priority: WARNING
      tags: [network, k8s_audit, barber-app]

    - rule: Barber App Secret Access
      desc: Detect access to Barber App secrets
      condition: >
        (k8s_audit and ka.verb in (get, list) and
         ka.target.resource=secrets and
         ka.target.namespace=barber-app and
         ka.target.name contains "barber-app")
      output: >
        Secret access in Barber App
        (user=%ka.user.name verb=%ka.verb secret=%ka.target.name
         namespace=%ka.target.namespace)
      priority: INFO
      tags: [secrets, k8s_audit, barber-app]

    - rule: Barber App Privilege Escalation
      desc: Detect privilege escalation attempts in Barber App
      condition: >
        (spawned_process and container and
         container.image.repository contains "barber-app" and
         proc.name in (su, sudo, doas) and
         not proc.pname in (systemd, systemd-user))
      output: >
        Privilege escalation attempt in Barber App container
        (user=%user.name command=%proc.cmdline container=%container.name
         image=%container.image.repository)
      priority: CRITICAL
      tags: [privilege_escalation, container, barber-app]

---
# Falco DaemonSet
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: falco
  namespace: security
  labels:
    app: falco
spec:
  selector:
    matchLabels:
      app: falco
  template:
    metadata:
      labels:
        app: falco
    spec:
      serviceAccount: falco
      hostNetwork: true
      hostPID: true
      containers:
        - name: falco
          image: falcosecurity/falco:0.35.1
          args:
            - /usr/bin/falco
            - --cri=/run/containerd/containerd.sock
            - --k8s-api=https://kubernetes.default:443
            - --k8s-api-cert=/var/run/secrets/kubernetes.io/serviceaccount/ca.crt
            - --k8s-api-token=/var/run/secrets/kubernetes.io/serviceaccount/token
          env:
            - name: FALCO_K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          securityContext:
            privileged: true
          resources:
            requests:
              cpu: 100m
              memory: 512Mi
            limits:
              cpu: 1000m
              memory: 1Gi
          volumeMounts:
            - mountPath: /host/var/run/docker.sock
              name: docker-socket
            - mountPath: /host/run/containerd/containerd.sock
              name: containerd-socket
            - mountPath: /host/dev
              name: dev-fs
            - mountPath: /host/proc
              name: proc-fs
              readOnly: true
            - mountPath: /host/boot
              name: boot-fs
              readOnly: true
            - mountPath: /host/lib/modules
              name: lib-modules
              readOnly: true
            - mountPath: /host/usr
              name: usr-fs
              readOnly: true
            - mountPath: /host/etc
              name: etc-fs
              readOnly: true
            - mountPath: /etc/falco
              name: falco-config
      volumes:
        - name: docker-socket
          hostPath:
            path: /var/run/docker.sock
        - name: containerd-socket
          hostPath:
            path: /run/containerd/containerd.sock
        - name: dev-fs
          hostPath:
            path: /dev
        - name: proc-fs
          hostPath:
            path: /proc
        - name: boot-fs
          hostPath:
            path: /boot
        - name: lib-modules
          hostPath:
            path: /lib/modules
        - name: usr-fs
          hostPath:
            path: /usr
        - name: etc-fs
          hostPath:
            path: /etc
        - name: falco-config
          configMap:
            name: falco-config

---
# Falco ServiceAccount
apiVersion: v1
kind: ServiceAccount
metadata:
  name: falco
  namespace: security

---
# Falco ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: falco
rules:
  - apiGroups: [""]
    resources: ["nodes", "namespaces", "pods", "replicationcontrollers", "services"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["apps"]
    resources: ["daemonsets", "deployments", "replicasets", "statefulsets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["extensions"]
    resources: ["daemonsets", "deployments", "replicasets"]
    verbs: ["get", "list", "watch"]

---
# Falco ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: falco
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: falco
subjects:
  - kind: ServiceAccount
    name: falco
    namespace: security

---
# OPA Gatekeeper Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: gatekeeper-config
  namespace: gatekeeper-system
data:
  config.yaml: |
    match:
      - excludedNamespaces: ["kube-system", "gatekeeper-system"]
        processes: ["*"]
    validation:
      traces:
        - user:
            kind:
              group: "*"
              version: "*"
              kind: "*"
    readiness:
      statsEnabled: true

---
# Constraint Template for Required Labels
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: k8srequiredlabels
spec:
  crd:
    spec:
      names:
        kind: K8sRequiredLabels
      validation:
        openAPIV3Schema:
          type: object
          properties:
            labels:
              type: array
              items:
                type: string
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package k8srequiredlabels

        violation[{"msg": msg}] {
          required := input.parameters.labels
          provided := input.review.object.metadata.labels
          missing := required[_]
          not provided[missing]
          msg := sprintf("Missing required label: %v", [missing])
        }

---
# Required Labels Constraint
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sRequiredLabels
metadata:
  name: must-have-app-label
spec:
  match:
    kinds:
      - apiGroups: ["apps"]
        kinds: ["Deployment", "StatefulSet", "DaemonSet"]
    namespaces: ["barber-app"]
  parameters:
    labels: ["app", "version", "environment"]

---
# Image Security Policy
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: k8sallowedrepos
spec:
  crd:
    spec:
      names:
        kind: K8sAllowedRepos
      validation:
        openAPIV3Schema:
          type: object
          properties:
            repos:
              type: array
              items:
                type: string
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package k8sallowedrepos

        violation[{"msg": msg}] {
          container := input.review.object.spec.containers[_]
          satisfied := [good | repo = input.parameters.repos[_] ; good = startswith(container.image, repo)]
          not any(satisfied)
          msg := sprintf("Container <%v> has an invalid image repo <%v>, allowed repos are %v", [container.name, container.image, input.parameters.repos])
        }

        violation[{"msg": msg}] {
          container := input.review.object.spec.initContainers[_]
          satisfied := [good | repo = input.parameters.repos[_] ; good = startswith(container.image, repo)]
          not any(satisfied)
          msg := sprintf("InitContainer <%v> has an invalid image repo <%v>, allowed repos are %v", [container.name, container.image, input.parameters.repos])
        }

---
# Allowed Repositories Constraint
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sAllowedRepos
metadata:
  name: repo-must-be-trusted
spec:
  match:
    kinds:
      - apiGroups: ["apps"]
        kinds: ["Deployment", "StatefulSet", "DaemonSet"]
      - apiGroups: [""]
        kinds: ["Pod"]
    namespaces: ["barber-app"]
  parameters:
    repos:
      - "gcr.io/barber-app/"
      - "docker.io/library/"
      - "quay.io/barber-app/"
      - "registry.barber-app.com/"

---
# Security Monitoring Dashboard
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-dashboard
  namespace: monitoring
data:
  security-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Barber App - Security Dashboard",
        "tags": ["security", "barber-app"],
        "timezone": "Africa/Cairo",
        "panels": [
          {
            "id": 1,
            "title": "Security Events",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(falco_events_total[5m])",
                "legendFormat": "{{priority}} events"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Failed Authentication Attempts",
            "type": "stat",
            "targets": [
              {
                "expr": "increase(auth_failed_attempts_total[1h])",
                "legendFormat": "Failed Attempts"
              }
            ],
            "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Suspicious Network Activity",
            "type": "table",
            "targets": [
              {
                "expr": "falco_events_total{rule_name=~\".*network.*\"}",
                "legendFormat": "Network Events"
              }
            ],
            "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}
          },
          {
            "id": 4,
            "title": "Container Security Violations",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(gatekeeper_violations_total[5m])",
                "legendFormat": "{{violation_kind}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 5,
            "title": "SSL Certificate Status",
            "type": "stat",
            "targets": [
              {
                "expr": "probe_ssl_earliest_cert_expiry",
                "legendFormat": "Days to Expiry"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "30s"
      }
    }
