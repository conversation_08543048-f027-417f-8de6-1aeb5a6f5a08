# 🚀 دليل التثبيت الشامل - مشروع "حلاق على بابك"

## 📋 متطلبات النظام

### 🖥️ متطلبات الخادم
- **نظام التشغيل**: Ubuntu 20.04 LTS أو أحدث / CentOS 8 أو أحدث
- **المعالج**: 4 cores minimum (8 cores recommended)
- **الذاكرة**: 8GB RAM minimum (16GB recommended)
- **التخزين**: 100GB SSD minimum (500GB recommended)
- **الشبكة**: اتصال إنترنت مستقر

### 🛠️ البرامج المطلوبة
- Docker 20.10+
- Docker Compose 2.0+
- Git 2.30+
- Node.js 18+ (للتطوير)
- Flutter 3.16+ (للتطبيقات المحمولة)

---

## 🔧 التثبيت السريع

### 1️⃣ تحضير الخادم

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت المتطلبات الأساسية
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# تثبيت Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# تثبيت Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# إعادة تسجيل الدخول لتفعيل مجموعة Docker
newgrp docker
```

### 2️⃣ استنساخ المشروع

```bash
# إنشاء مجلد المشروع
sudo mkdir -p /opt/barber-app
cd /opt/barber-app

# استنساخ المشروع (استبدل بالرابط الفعلي)
git clone https://github.com/your-repo/barber-app.git .

# إعداد الصلاحيات
sudo chown -R $USER:$USER /opt/barber-app
chmod +x deployment/scripts/*.sh
```

### 3️⃣ إعداد متغيرات البيئة

```bash
# نسخ ملفات البيئة
cp backend/.env.example backend/.env
cp admin-dashboard/.env.example admin-dashboard/.env

# تحرير متغيرات البيئة
nano backend/.env
```

#### متغيرات البيئة الأساسية:
```env
# Application
APP_NAME="حلاق على بابك"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://api.barber-app.com
APP_KEY=base64:your-32-character-secret-key

# Database
DB_CONNECTION=mysql
DB_HOST=db
DB_PORT=3306
DB_DATABASE=barber_db
DB_USERNAME=barber_user
DB_PASSWORD=your-secure-database-password

# Redis
REDIS_HOST=redis
REDIS_PASSWORD=your-secure-redis-password
REDIS_PORT=6379

# JWT
JWT_SECRET=your-jwt-secret-key
JWT_TTL=60

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="حلاق على بابك"

# Payment Gateways
STRIPE_KEY=pk_live_your-stripe-public-key
STRIPE_SECRET=sk_live_your-stripe-secret-key
PAYMOB_API_KEY=your-paymob-api-key
PAYMOB_INTEGRATION_ID=your-integration-id

# Firebase
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id

# Google Maps
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Agora (Live Streaming)
AGORA_APP_ID=your-agora-app-id
AGORA_APP_CERTIFICATE=your-agora-app-certificate

# AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=barber-app-storage

# Elasticsearch
ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200

# Monitoring
GRAFANA_PASSWORD=your-secure-grafana-password
PROMETHEUS_PASSWORD=your-secure-prometheus-password
```

### 4️⃣ إعداد SSL Certificates

```bash
# تثبيت Certbot
sudo apt install -y certbot python3-certbot-nginx

# الحصول على شهادات SSL
sudo certbot certonly --standalone -d api.barber-app.com
sudo certbot certonly --standalone -d admin.barber-app.com
sudo certbot certonly --standalone -d barber-app.com

# نسخ الشهادات
sudo mkdir -p deployment/ssl
sudo cp /etc/letsencrypt/live/api.barber-app.com/fullchain.pem deployment/ssl/
sudo cp /etc/letsencrypt/live/api.barber-app.com/privkey.pem deployment/ssl/
sudo chown -R $USER:$USER deployment/ssl
```

### 5️⃣ بناء ونشر التطبيق

```bash
# تشغيل سكريبت النشر
./deployment/scripts/deploy.sh production latest

# أو النشر اليدوي
docker-compose -f deployment/docker-compose.production.yml up -d
```

---

## 🔧 التثبيت المفصل

### Backend (Laravel API)

#### 1. إعداد قاعدة البيانات
```bash
# انتظار تشغيل قاعدة البيانات
docker-compose -f deployment/docker-compose.production.yml up -d db redis

# تشغيل الترحيلات
docker-compose -f deployment/docker-compose.production.yml exec app php artisan migrate --force

# إدراج البيانات الأولية
docker-compose -f deployment/docker-compose.production.yml exec app php artisan db:seed --force

# إنشاء مفتاح التطبيق
docker-compose -f deployment/docker-compose.production.yml exec app php artisan key:generate

# إنشاء مفاتيح JWT
docker-compose -f deployment/docker-compose.production.yml exec app php artisan jwt:secret

# تحسين التطبيق
docker-compose -f deployment/docker-compose.production.yml exec app php artisan config:cache
docker-compose -f deployment/docker-compose.production.yml exec app php artisan route:cache
docker-compose -f deployment/docker-compose.production.yml exec app php artisan view:cache
```

#### 2. إعداد التخزين
```bash
# إنشاء رابط التخزين
docker-compose -f deployment/docker-compose.production.yml exec app php artisan storage:link

# إعداد صلاحيات التخزين
docker-compose -f deployment/docker-compose.production.yml exec app chmod -R 775 storage bootstrap/cache
```

#### 3. إعداد المهام المجدولة
```bash
# تشغيل Laravel Horizon
docker-compose -f deployment/docker-compose.production.yml up -d horizon

# تشغيل المجدول
docker-compose -f deployment/docker-compose.production.yml up -d scheduler
```

### Admin Dashboard (Vue.js)

#### 1. بناء التطبيق
```bash
cd admin-dashboard

# تثبيت التبعيات
npm install

# بناء للإنتاج
npm run build

# نسخ الملفات المبنية
cp -r dist/* ../backend/public/admin/
```

#### 2. إعداد Nginx
```bash
# نسخ إعدادات Nginx
sudo cp deployment/nginx/production.conf /etc/nginx/sites-available/barber-app
sudo ln -s /etc/nginx/sites-available/barber-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Mobile Apps (Flutter)

#### 1. إعداد بيئة Flutter
```bash
# تثبيت Flutter
git clone https://github.com/flutter/flutter.git -b stable
export PATH="$PATH:`pwd`/flutter/bin"

# فحص التثبيت
flutter doctor
```

#### 2. بناء تطبيق العملاء
```bash
cd customer-app

# تثبيت التبعيات
flutter pub get

# بناء للأندرويد
flutter build apk --release

# بناء للـ iOS (على macOS فقط)
flutter build ios --release
```

#### 3. بناء تطبيق الحلاقين
```bash
cd barber-app

# تثبيت التبعيات
flutter pub get

# بناء للأندرويد
flutter build apk --release

# بناء للـ iOS (على macOS فقط)
flutter build ios --release
```

---

## 🔍 التحقق من التثبيت

### 1. فحص الخدمات
```bash
# فحص حالة الحاويات
docker-compose -f deployment/docker-compose.production.yml ps

# فحص السجلات
docker-compose -f deployment/docker-compose.production.yml logs -f app

# فحص قاعدة البيانات
docker-compose -f deployment/docker-compose.production.yml exec db mysql -u barber_user -p -e "SHOW DATABASES;"
```

### 2. اختبار API
```bash
# فحص صحة API
curl -X GET https://api.barber-app.com/api/health

# فحص تسجيل الدخول
curl -X POST https://api.barber-app.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123456"}'
```

### 3. فحص لوحة التحكم
```bash
# الوصول للوحة التحكم
curl -I https://admin.barber-app.com

# فحص Grafana
curl -I http://your-server-ip:3000
```

---

## 🛠️ التثبيت للتطوير

### 1. إعداد بيئة التطوير
```bash
# نسخ ملف البيئة للتطوير
cp backend/.env.example backend/.env.local

# تحرير المتغيرات للتطوير
nano backend/.env.local
```

### 2. تشغيل بيئة التطوير
```bash
# تشغيل جميع الخدمات
docker-compose -f deployment/docker-compose.development.yml up -d

# تشغيل الترحيلات
docker-compose -f deployment/docker-compose.development.yml exec app php artisan migrate

# إدراج البيانات التجريبية
docker-compose -f deployment/docker-compose.development.yml exec app php artisan db:seed
```

### 3. الوصول لأدوات التطوير
- **التطبيق**: http://localhost:8000
- **Adminer**: http://localhost:8080/adminer
- **MailHog**: http://localhost:8025
- **Redis Commander**: http://localhost:8081
- **Grafana**: http://localhost:3000
- **Prometheus**: http://localhost:9090

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ في الاتصال بقاعدة البيانات
```bash
# فحص حالة قاعدة البيانات
docker-compose exec db mysql -u root -p -e "SELECT 1;"

# إعادة تشغيل قاعدة البيانات
docker-compose restart db

# فحص السجلات
docker-compose logs db
```

#### 2. مشاكل الصلاحيات
```bash
# إصلاح صلاحيات Laravel
docker-compose exec app chmod -R 775 storage bootstrap/cache
docker-compose exec app chown -R www-data:www-data storage bootstrap/cache
```

#### 3. مشاكل SSL
```bash
# تجديد شهادات SSL
sudo certbot renew

# فحص صحة الشهادات
openssl x509 -in deployment/ssl/fullchain.pem -text -noout
```

#### 4. مشاكل الذاكرة
```bash
# فحص استخدام الذاكرة
docker stats

# زيادة حد الذاكرة لـ PHP
echo "memory_limit = 512M" >> deployment/php/local.ini
docker-compose restart app
```

#### 5. مشاكل الشبكة
```bash
# فحص الشبكات
docker network ls

# إعادة إنشاء الشبكة
docker-compose down
docker network prune
docker-compose up -d
```

---

## 📊 المراقبة والصيانة

### 1. إعداد المراقبة
```bash
# الوصول لـ Grafana
# URL: http://your-server-ip:3000
# Username: admin
# Password: your-grafana-password

# إعداد التنبيهات
docker-compose exec prometheus promtool check rules /etc/prometheus/alert_rules.yml
```

### 2. النسخ الاحتياطية
```bash
# إنشاء نسخة احتياطية يدوية
./deployment/scripts/backup.sh

# إعداد النسخ الاحتياطية التلقائية
crontab -e
# إضافة: 0 2 * * * /opt/barber-app/deployment/scripts/backup.sh
```

### 3. التحديثات
```bash
# تحديث التطبيق
git pull origin main
./deployment/scripts/deploy.sh production latest

# تحديث قاعدة البيانات
docker-compose exec app php artisan migrate --force
```

---

## 📞 الدعم والمساعدة

### 🆘 في حالة المشاكل:
1. **فحص السجلات**: `docker-compose logs -f app`
2. **فحص الحالة**: `docker-compose ps`
3. **إعادة التشغيل**: `docker-compose restart`
4. **التواصل**: <EMAIL>

### 📚 موارد إضافية:
- **التوثيق الفني**: `/docs`
- **API Documentation**: `/docs/API_DOCUMENTATION.md`
- **دليل الأمان**: `/docs/SECURITY_GUIDE.md`
- **دليل النشر**: `/docs/FINAL_DEPLOYMENT_GUIDE.md`

---

**✅ تم إكمال التثبيت بنجاح!**

*مشروع "حلاق على بابك" جاهز للعمل والانطلاق! 🚀*
