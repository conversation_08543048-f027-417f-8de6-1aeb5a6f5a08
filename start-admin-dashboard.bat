@echo off
chcp 65001 >nul
title تشغيل لوحة الإدارة - حلاق على بابك

echo.
echo ========================================
echo 🚀 تشغيل لوحة الإدارة المتقدمة
echo ========================================
echo.

echo 📍 المسار الحالي: %CD%
echo.

:: التحقق من وجود Node.js
echo 🔍 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت أو غير متاح
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js متاح
node --version
npm --version
echo.

:: التحقق من مجلد admin-dashboard
if not exist "admin-dashboard" (
    echo ❌ مجلد admin-dashboard غير موجود في: %CD%
    echo 📂 المجلدات المتاحة:
    dir /b
    echo.
    echo 💡 تأكد من تشغيل هذا الملف من المجلد الصحيح
    pause
    exit /b 1
)

echo ✅ مجلد admin-dashboard موجود
echo.

:: الانتقال إلى مجلد admin-dashboard
echo 📂 الانتقال إلى مجلد admin-dashboard...
cd admin-dashboard

:: التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود
    echo 💡 هذا ليس مشروع Node.js صحيح
    pause
    exit /b 1
)

echo ✅ ملف package.json موجود
echo.

:: التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت تبعيات المشروع...
    echo ⏳ هذا قد يستغرق بضع دقائق...
    echo.
    
    npm install
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        echo 💡 تحقق من:
        echo    - اتصال الإنترنت
        echo    - صحة ملف package.json
        echo    - صلاحيات الكتابة في المجلد
        pause
        exit /b 1
    )
    
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
) else (
    echo ✅ التبعيات مثبتة مسبقاً
    echo.
)

:: التحقق من وجود ملف .env
if not exist ".env" (
    echo 📝 إنشاء ملف .env...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo ✅ تم نسخ .env.example إلى .env
    ) else (
        echo # Admin Dashboard Environment > .env
        echo VITE_API_BASE_URL=http://localhost:8000/api/v1 >> .env
        echo VITE_APP_NAME="حلاق على بابك - لوحة التحكم" >> .env
        echo ✅ تم إنشاء ملف .env أساسي
    )
    echo.
)

echo 🚀 تشغيل خادم التطوير...
echo 🌐 لوحة الإدارة ستعمل على: http://localhost:3000
echo.
echo 💡 نصائح:
echo    - اتركي هذه النافذة مفتوحة
echo    - لإيقاف الخادم اضغط Ctrl+C
echo    - لوحة الإدارة ستفتح تلقائياً في المتصفح
echo.

:: تشغيل خادم التطوير
echo ⏳ جاري بدء الخادم...
timeout /t 2 /nobreak >nul

npm run dev

:: إذا فشل npm run dev، جرب البدائل
if %errorlevel% neq 0 (
    echo.
    echo ⚠️ فشل في تشغيل npm run dev، جاري المحاولة بطرق بديلة...
    echo.
    
    echo 🔄 محاولة 1: npm start
    npm start
    
    if %errorlevel% neq 0 (
        echo.
        echo 🔄 محاولة 2: npx vite
        npx vite
        
        if %errorlevel% neq 0 (
            echo.
            echo 🔄 محاولة 3: npx vite --port 3000
            npx vite --port 3000
            
            if %errorlevel% neq 0 (
                echo.
                echo ❌ فشل في تشغيل خادم التطوير
                echo.
                echo 🛠️ حلول مقترحة:
                echo 1. تحقق من ملف package.json
                echo 2. احذف مجلد node_modules وأعد تثبيت التبعيات
                echo 3. تحقق من وجود ملف vite.config.js
                echo 4. استخدم لوحة الإدارة البديلة: admin.php
                echo.
                echo 🌐 لوحة الإدارة البديلة متاحة على:
                echo http://localhost/flutter_module_2/backend/public/admin.php
                echo.
                pause
                exit /b 1
            )
        )
    )
)

echo.
echo 🎉 تم تشغيل لوحة الإدارة بنجاح!
echo 🌐 اذهب إلى: http://localhost:3000
echo.
pause
