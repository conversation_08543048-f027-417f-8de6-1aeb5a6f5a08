<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Barber>
 */
class BarberFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $specialties = [
            'قص شعر كلاسيكي',
            'قص شعر عصري',
            'حلاقة ذقن',
            'تشكيل لحية',
            'قص شعر أطفال',
            'تصفيف شعر',
            'حلاقة بالموس',
            'تهذيب شارب',
            'قص شعر مدرج',
            'عناية بالشعر'
        ];

        $bios = [
            'حلاق محترف مع خبرة واسعة في جميع أنواع قصات الشعر الكلاسيكية والعصرية',
            'متخصص في الحلاقة التقليدية والعناية بالذقن واللحية',
            'خبير في قصات الشعر العصرية وتصفيف الشعر للمناسبات الخاصة',
            'حلاق ماهر يجمع بين الأساليب التقليدية والحديثة في الحلاقة',
            'متخصص في قص شعر الأطفال والكبار مع اهتمام خاص بالتفاصيل',
            'خبرة طويلة في مجال الحلاقة والعناية بالشعر والذقن',
            'حلاق محترف يقدم خدمات متميزة في الحلاقة والتجميل',
            'متخصص في الحلاقة بالموس والعناية التقليدية بالذقن'
        ];

        $workingHours = [
            'saturday' => ['start' => '09:00', 'end' => '22:00'],
            'sunday' => ['start' => '09:00', 'end' => '22:00'],
            'monday' => ['start' => '09:00', 'end' => '22:00'],
            'tuesday' => ['start' => '09:00', 'end' => '22:00'],
            'wednesday' => ['start' => '09:00', 'end' => '22:00'],
            'thursday' => ['start' => '09:00', 'end' => '22:00'],
            'friday' => ['start' => '14:00', 'end' => '22:00'],
        ];

        return [
            'user_id' => User::factory()->barber(),
            'experience_years' => $this->faker->numberBetween(2, 20),
            'specialties' => $this->faker->randomElements($specialties, $this->faker->numberBetween(3, 6)),
            'bio' => $this->faker->randomElement($bios),
            'rating' => $this->faker->randomFloat(2, 3.5, 5.0),
            'total_reviews' => $this->faker->numberBetween(10, 500),
            'total_bookings' => $this->faker->numberBetween(50, 1000),
            'is_verified' => $this->faker->boolean(80), // 80% verified
            'is_available' => $this->faker->boolean(90), // 90% available
            'verification_documents' => [
                'id_card' => 'documents/id_' . $this->faker->uuid() . '.jpg',
                'certificate' => 'documents/cert_' . $this->faker->uuid() . '.jpg',
            ],
            'working_hours' => $workingHours,
            'service_radius' => $this->faker->numberBetween(5, 20),
            'commission_rate' => $this->faker->randomFloat(2, 10.00, 20.00),
        ];
    }

    /**
     * Indicate that the barber is verified.
     */
    public function verified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => true,
        ]);
    }

    /**
     * Indicate that the barber is not verified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => false,
        ]);
    }

    /**
     * Indicate that the barber is available.
     */
    public function available(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_available' => true,
        ]);
    }

    /**
     * Indicate that the barber is unavailable.
     */
    public function unavailable(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_available' => false,
        ]);
    }

    /**
     * Indicate that the barber is highly rated.
     */
    public function highlyRated(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->randomFloat(2, 4.5, 5.0),
            'total_reviews' => $this->faker->numberBetween(100, 500),
            'total_bookings' => $this->faker->numberBetween(300, 1000),
        ]);
    }

    /**
     * Indicate that the barber is new.
     */
    public function newBarber(): static
    {
        return $this->state(fn (array $attributes) => [
            'experience_years' => $this->faker->numberBetween(1, 3),
            'rating' => $this->faker->randomFloat(2, 3.5, 4.2),
            'total_reviews' => $this->faker->numberBetween(5, 50),
            'total_bookings' => $this->faker->numberBetween(10, 100),
        ]);
    }

    /**
     * Indicate that the barber is experienced.
     */
    public function experienced(): static
    {
        return $this->state(fn (array $attributes) => [
            'experience_years' => $this->faker->numberBetween(10, 25),
            'rating' => $this->faker->randomFloat(2, 4.2, 5.0),
            'total_reviews' => $this->faker->numberBetween(200, 800),
            'total_bookings' => $this->faker->numberBetween(500, 2000),
        ]);
    }

    /**
     * Indicate that the barber specializes in classic cuts.
     */
    public function classicSpecialist(): static
    {
        return $this->state(fn (array $attributes) => [
            'specialties' => [
                'قص شعر كلاسيكي',
                'حلاقة ذقن',
                'حلاقة بالموس',
                'تهذيب شارب'
            ],
            'bio' => 'متخصص في الحلاقة التقليدية الكلاسيكية مع خبرة واسعة في استخدام الموس والأدوات التقليدية',
        ]);
    }

    /**
     * Indicate that the barber specializes in modern cuts.
     */
    public function modernSpecialist(): static
    {
        return $this->state(fn (array $attributes) => [
            'specialties' => [
                'قص شعر عصري',
                'قص شعر مدرج',
                'تصفيف شعر',
                'تشكيل لحية'
            ],
            'bio' => 'خبير في القصات العصرية والأساليب الحديثة في الحلاقة وتصفيف الشعر',
        ]);
    }

    /**
     * Indicate that the barber works with children.
     */
    public function childrenSpecialist(): static
    {
        return $this->state(fn (array $attributes) => [
            'specialties' => [
                'قص شعر أطفال',
                'قص شعر كلاسيكي',
                'تصفيف شعر',
                'قص شعر عصري'
            ],
            'bio' => 'متخصص في قص شعر الأطفال مع صبر وخبرة في التعامل مع الصغار',
        ]);
    }

    /**
     * Indicate that the barber has flexible hours.
     */
    public function flexibleHours(): static
    {
        return $this->state(fn (array $attributes) => [
            'working_hours' => [
                'saturday' => ['start' => '08:00', 'end' => '23:00'],
                'sunday' => ['start' => '08:00', 'end' => '23:00'],
                'monday' => ['start' => '08:00', 'end' => '23:00'],
                'tuesday' => ['start' => '08:00', 'end' => '23:00'],
                'wednesday' => ['start' => '08:00', 'end' => '23:00'],
                'thursday' => ['start' => '08:00', 'end' => '23:00'],
                'friday' => ['start' => '12:00', 'end' => '23:00'],
            ],
        ]);
    }

    /**
     * Indicate that the barber has limited hours.
     */
    public function limitedHours(): static
    {
        return $this->state(fn (array $attributes) => [
            'working_hours' => [
                'saturday' => ['start' => '10:00', 'end' => '18:00'],
                'sunday' => ['start' => '10:00', 'end' => '18:00'],
                'monday' => ['start' => '10:00', 'end' => '18:00'],
                'tuesday' => ['start' => '10:00', 'end' => '18:00'],
                'wednesday' => ['start' => '10:00', 'end' => '18:00'],
                'thursday' => ['start' => '10:00', 'end' => '18:00'],
                'friday' => ['start' => '15:00', 'end' => '20:00'],
            ],
        ]);
    }

    /**
     * Indicate that the barber has a wide service radius.
     */
    public function wideRadius(): static
    {
        return $this->state(fn (array $attributes) => [
            'service_radius' => $this->faker->numberBetween(15, 30),
        ]);
    }

    /**
     * Indicate that the barber has a narrow service radius.
     */
    public function narrowRadius(): static
    {
        return $this->state(fn (array $attributes) => [
            'service_radius' => $this->faker->numberBetween(3, 8),
        ]);
    }

    /**
     * Indicate that the barber has a low commission rate.
     */
    public function lowCommission(): static
    {
        return $this->state(fn (array $attributes) => [
            'commission_rate' => $this->faker->randomFloat(2, 8.00, 12.00),
        ]);
    }

    /**
     * Indicate that the barber has a high commission rate.
     */
    public function highCommission(): static
    {
        return $this->state(fn (array $attributes) => [
            'commission_rate' => $this->faker->randomFloat(2, 18.00, 25.00),
        ]);
    }

    /**
     * Indicate that the barber is premium.
     */
    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => true,
            'is_available' => true,
            'rating' => $this->faker->randomFloat(2, 4.7, 5.0),
            'total_reviews' => $this->faker->numberBetween(200, 1000),
            'total_bookings' => $this->faker->numberBetween(500, 2000),
            'experience_years' => $this->faker->numberBetween(8, 20),
            'specialties' => [
                'قص شعر كلاسيكي',
                'قص شعر عصري',
                'حلاقة ذقن',
                'تشكيل لحية',
                'تصفيف شعر',
                'عناية بالشعر'
            ],
            'bio' => 'حلاق محترف ومتميز مع خبرة واسعة في جميع أنواع الحلاقة والعناية بالشعر. يقدم خدمات عالية الجودة ويحرص على رضا العملاء.',
        ]);
    }
}
