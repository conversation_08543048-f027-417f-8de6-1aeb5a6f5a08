class AppConstants {
  // App Info
  static const String appName = 'حلاق على بابك - المحترفين';
  static const String appNameEn = 'Barber at Your Door - Professionals';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق الحلاقين المحترفين لإدارة الطلبات والحجوزات';

  // API Configuration
  static const String baseUrl = 'http://localhost:8000/api/v1';
  static const String apiTimeout = '30'; // seconds
  
  // Storage Keys
  static const String tokenKey = 'barber_auth_token';
  static const String userKey = 'barber_data';
  static const String settingsKey = 'barber_settings';
  static const String languageKey = 'barber_language';
  static const String themeKey = 'barber_theme';
  static const String locationKey = 'barber_location';
  static const String onboardingKey = 'barber_onboarding_completed';
  static const String notificationKey = 'barber_notification_settings';
  static const String availabilityKey = 'barber_availability';
  static const String servicesKey = 'barber_services';

  // Hive Boxes
  static const String barberBox = 'barber_box';
  static const String bookingsBox = 'bookings_box';
  static const String earningsBox = 'earnings_box';
  static const String scheduleBox = 'schedule_box';
  static const String settingsBox = 'settings_box';
  static const String cacheBox = 'cache_box';

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 20.0;

  // Image Sizes
  static const double avatarSize = 80.0;
  static const double smallAvatarSize = 40.0;
  static const double largeAvatarSize = 120.0;
  static const double iconSize = 24.0;
  static const double smallIconSize = 16.0;
  static const double largeIconSize = 32.0;

  // Map Configuration
  static const double defaultZoom = 15.0;
  static const double maxZoom = 20.0;
  static const double minZoom = 10.0;
  static const double serviceRadius = 10000.0; // meters

  // Booking Configuration
  static const int maxDailyBookings = 12;
  static const int minServiceDuration = 15; // minutes
  static const int maxServiceDuration = 120; // minutes
  static const int defaultServiceDuration = 30; // minutes
  static const int bookingBufferTime = 15; // minutes between bookings

  // Earnings Configuration
  static const double minWithdrawalAmount = 100.0;
  static const double maxWithdrawalAmount = 5000.0;
  static const double withdrawalFee = 5.0;
  static const int withdrawalProcessingDays = 3;

  // Working Hours
  static const int earliestWorkingHour = 8; // 8 AM
  static const int latestWorkingHour = 22; // 10 PM
  static const int maxWorkingHours = 12; // per day

  // Rating System
  static const double minRating = 1.0;
  static const double maxRating = 5.0;
  static const double minAcceptableRating = 3.0;
  static const int minRatingsForAverage = 5;

  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];

  // Notification Types
  static const String newBookingNotification = 'new_booking';
  static const String bookingCancelledNotification = 'booking_cancelled';
  static const String paymentReceivedNotification = 'payment_received';
  static const String ratingReceivedNotification = 'rating_received';
  static const String systemNotification = 'system';

  // Error Messages
  static const String networkError = 'خطأ في الاتصال بالإنترنت';
  static const String serverError = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
  static const String unknownError = 'حدث خطأ غير متوقع';
  static const String validationError = 'يرجى التحقق من البيانات المدخلة';
  static const String authError = 'خطأ في المصادقة، يرجى تسجيل الدخول مرة أخرى';
  static const String permissionError = 'ليس لديك صلاحية للوصول لهذه الخدمة';
  static const String locationError = 'خطأ في تحديد الموقع';
  static const String cameraError = 'خطأ في الوصول للكاميرا';

  // Success Messages
  static const String bookingAcceptedSuccess = 'تم قبول الحجز بنجاح';
  static const String bookingCompletedSuccess = 'تم إنهاء الخدمة بنجاح';
  static const String profileUpdateSuccess = 'تم تحديث الملف الشخصي بنجاح';
  static const String availabilityUpdateSuccess = 'تم تحديث حالة التوفر بنجاح';
  static const String withdrawalRequestSuccess = 'تم إرسال طلب السحب بنجاح';

  // Validation Rules
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int phoneLength = 11;
  static const int minExperienceYears = 1;
  static const int maxExperienceYears = 50;

  // Regular Expressions
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phoneRegex = r'^01[0-2,5]{1}[0-9]{8}$';
  static const String nameRegex = r'^[\u0600-\u06FFa-zA-Z\s]+$';
  static const String priceRegex = r'^\d+(\.\d{1,2})?$';

  // Social Media
  static const String facebookUrl = 'https://facebook.com/barberapp';
  static const String instagramUrl = 'https://instagram.com/barberapp';
  static const String whatsappNumber = '+201234567890';

  // Support
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+201234567890';
  static const String privacyPolicyUrl = 'https://barberapp.com/barber-privacy';
  static const String termsOfServiceUrl = 'https://barberapp.com/barber-terms';

  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const Duration shortCacheExpiration = Duration(hours: 1);
  static const Duration longCacheExpiration = Duration(days: 7);

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Currency
  static const String defaultCurrency = 'EGP';
  static const String currencySymbol = 'ج.م';

  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayTimeFormat = 'hh:mm a';
  static const String displayDateTimeFormat = 'dd/MM/yyyy hh:mm a';

  // Booking Status
  static const String bookingPending = 'pending';
  static const String bookingAccepted = 'accepted';
  static const String bookingRejected = 'rejected';
  static const String bookingInProgress = 'in_progress';
  static const String bookingCompleted = 'completed';
  static const String bookingCancelled = 'cancelled';

  // Barber Status
  static const String barberOnline = 'online';
  static const String barberOffline = 'offline';
  static const String barberBusy = 'busy';
  static const String barberBreak = 'break';

  // Service Categories
  static const String categoryHaircut = 'haircut';
  static const String categoryBeard = 'beard';
  static const String categorySkincare = 'skincare';
  static const String categoryPackage = 'package';

  // Payment Status
  static const String paymentPending = 'pending';
  static const String paymentPaid = 'paid';
  static const String paymentRefunded = 'refunded';

  // Withdrawal Status
  static const String withdrawalPending = 'pending';
  static const String withdrawalApproved = 'approved';
  static const String withdrawalRejected = 'rejected';
  static const String withdrawalCompleted = 'completed';

  // Background Task Names
  static const String syncBookingsTask = 'syncBookings';
  static const String checkNewBookingsTask = 'checkNewBookings';
  static const String updateLocationTask = 'updateLocation';
  static const String syncEarningsTask = 'syncEarnings';

  // Location Update Intervals
  static const Duration locationUpdateInterval = Duration(minutes: 5);
  static const Duration backgroundLocationUpdateInterval = Duration(minutes: 15);

  // Audio Files
  static const String newBookingSound = 'assets/sounds/new_booking.mp3';
  static const String bookingCancelledSound = 'assets/sounds/booking_cancelled.mp3';
  static const String paymentReceivedSound = 'assets/sounds/payment_received.mp3';

  // Default Values
  static const double defaultServicePrice = 50.0;
  static const int defaultServiceDurationMinutes = 30;
  static const double defaultCommissionRate = 15.0; // percentage

  // Verification Requirements
  static const List<String> requiredDocuments = [
    'national_id',
    'barber_certificate',
    'health_certificate',
  ];

  // Working Days
  static const List<String> workingDays = [
    'saturday',
    'sunday',
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
  ];

  // Time Slots (in minutes from midnight)
  static const List<int> timeSlots = [
    480,  // 8:00 AM
    510,  // 8:30 AM
    540,  // 9:00 AM
    570,  // 9:30 AM
    600,  // 10:00 AM
    630,  // 10:30 AM
    660,  // 11:00 AM
    690,  // 11:30 AM
    720,  // 12:00 PM
    750,  // 12:30 PM
    780,  // 1:00 PM
    810,  // 1:30 PM
    840,  // 2:00 PM
    870,  // 2:30 PM
    900,  // 3:00 PM
    930,  // 3:30 PM
    960,  // 4:00 PM
    990,  // 4:30 PM
    1020, // 5:00 PM
    1050, // 5:30 PM
    1080, // 6:00 PM
    1110, // 6:30 PM
    1140, // 7:00 PM
    1170, // 7:30 PM
    1200, // 8:00 PM
    1230, // 8:30 PM
    1260, // 9:00 PM
    1290, // 9:30 PM
    1320, // 10:00 PM
  ];
}
