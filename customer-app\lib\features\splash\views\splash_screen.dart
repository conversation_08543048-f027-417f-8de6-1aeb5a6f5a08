import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:animate_do/animate_do.dart';
import 'package:lottie/lottie.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/app_router.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/services/settings_service.dart';
import '../../../shared/services/storage_service.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late Animation<double> _logoAnimation;
  late Animation<double> _textAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeApp();
  }

  void _initializeAnimations() {
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _textAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _logoController.forward();
    Future.delayed(const Duration(milliseconds: 500), () {
      _textController.forward();
    });
  }

  Future<void> _initializeApp() async {
    try {
      // Wait for minimum splash duration
      await Future.delayed(const Duration(seconds: 3));

      // Check if app is in maintenance mode
      final settingsService = Get.find<SettingsService>();
      final isMaintenanceMode = await settingsService.isMaintenanceMode();
      
      if (isMaintenanceMode) {
        _showMaintenanceDialog();
        return;
      }

      // Check if user has completed onboarding
      final storageService = Get.find<StorageService>();
      final hasCompletedOnboarding = await storageService.getBool(
        AppConstants.onboardingKey,
      ) ?? false;

      if (!hasCompletedOnboarding) {
        AppRouter.toOnboarding();
        return;
      }

      // Check if user is logged in
      final authService = Get.find<AuthService>();
      final isLoggedIn = await authService.isLoggedIn();

      if (isLoggedIn) {
        // Verify token and navigate to home
        final isValid = await authService.verifyToken();
        if (isValid) {
          AppRouter.toHome();
        } else {
          AppRouter.toLogin();
        }
      } else {
        AppRouter.toLogin();
      }
    } catch (e) {
      // Handle initialization error
      _showErrorDialog(e.toString());
    }
  }

  void _showMaintenanceDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('التطبيق تحت الصيانة'),
        content: const Text(
          'نعتذر، التطبيق تحت الصيانة حالياً. سنعود قريباً بخدمة أفضل.',
        ),
        actions: [
          TextButton(
            onPressed: () => _initializeApp(),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  void _showErrorDialog(String error) {
    Get.dialog(
      AlertDialog(
        title: const Text('خطأ في التحميل'),
        content: Text('حدث خطأ أثناء تحميل التطبيق:\n$error'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              _initializeApp();
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradient,
        ),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(flex: 2),
              
              // Logo Animation
              AnimatedBuilder(
                animation: _logoAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _logoAnimation.value,
                    child: FadeIn(
                      duration: const Duration(milliseconds: 1500),
                      child: Container(
                        width: 150,
                        height: 150,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.content_cut,
                          size: 80,
                          color: AppColors.primary,
                        ),
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 40),
              
              // App Name Animation
              AnimatedBuilder(
                animation: _textAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: _textAnimation.value,
                    child: Transform.translate(
                      offset: Offset(0, 50 * (1 - _textAnimation.value)),
                      child: Column(
                        children: [
                          Text(
                            AppConstants.appName,
                            style: AppTextStyles.h2.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          
                          const SizedBox(height: 8),
                          
                          Text(
                            AppConstants.appDescription,
                            style: AppTextStyles.bodyLarge.copyWith(
                              color: Colors.white.withOpacity(0.9),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              
              const Spacer(flex: 2),
              
              // Loading Indicator
              FadeInUp(
                delay: const Duration(milliseconds: 2000),
                duration: const Duration(milliseconds: 800),
                child: Column(
                  children: [
                    const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 3,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    Text(
                      'جاري تحميل التطبيق...',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 60),
              
              // Version Info
              FadeInUp(
                delay: const Duration(milliseconds: 2500),
                duration: const Duration(milliseconds: 800),
                child: Text(
                  'الإصدار ${AppConstants.appVersion}',
                  style: AppTextStyles.caption.copyWith(
                    color: Colors.white.withOpacity(0.6),
                  ),
                ),
              ),
              
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}
