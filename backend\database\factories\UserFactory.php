<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $arabicNames = [
            'أحمد محمد', 'محمد علي', 'علي أحمد', 'حسام محمود', 'كريم سامي',
            'عمر حسن', 'يوسف إبراهيم', 'مصطفى عبدالله', 'خالد محمود', 'طارق سعيد',
            'أمير فتحي', 'محمود رضا', 'إسلام طه', 'أحمد سالم', 'محمد حسين',
            'عبدالرحمن أحمد', 'يحيى محمد', 'حازم علي', 'شريف محمود', 'وائل حسن'
        ];

        return [
            'name' => $this->faker->randomElement($arabicNames),
            'email' => fake()->unique()->safeEmail(),
            'phone' => '+2010' . $this->faker->numberBetween(10000000, 99999999),
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'avatar' => null,
            'date_of_birth' => $this->faker->dateTimeBetween('-60 years', '-18 years'),
            'gender' => $this->faker->randomElement(['male', 'female']),
            'is_active' => true,
            'last_login_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'fcm_token' => 'fcm_' . Str::random(152),
            'two_factor_secret' => null,
            'two_factor_enabled' => false,
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
            'phone_verified_at' => null,
        ]);
    }

    /**
     * Indicate that the user is a customer.
     */
    public function customer(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->randomElement([
                'أحمد محمد', 'محمد علي', 'علي أحمد', 'حسام محمود', 'كريم سامي',
                'عمر حسن', 'يوسف إبراهيم', 'مصطفى عبدالله', 'خالد محمود', 'طارق سعيد'
            ]),
        ]);
    }

    /**
     * Indicate that the user is a barber.
     */
    public function barber(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->randomElement([
                'أستاذ حسن', 'كابتن أحمد', 'أستاذ محمد', 'كابتن علي', 'أستاذ سامي',
                'حلاق محمود', 'أستاذ عمر', 'كابتن يوسف', 'حلاق مصطفى', 'أستاذ خالد'
            ]),
        ]);
    }

    /**
     * Indicate that the user has two-factor authentication enabled.
     */
    public function withTwoFactor(): static
    {
        return $this->state(fn (array $attributes) => [
            'two_factor_secret' => encrypt(Str::random(32)),
            'two_factor_enabled' => true,
        ]);
    }

    /**
     * Indicate that the user is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the user is an admin.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'phone' => '+201000000000',
        ]);
    }
}
