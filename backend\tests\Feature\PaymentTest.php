<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Barber;
use App\Models\Booking;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Services\PaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Mockery;

class PaymentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $customer;
    protected $booking;
    protected $paymentMethod;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->customer = User::factory()->create();
        $barberUser = User::factory()->create();
        $barber = Barber::factory()->create(['user_id' => $barberUser->id]);
        
        $this->booking = Booking::factory()->create([
            'customer_id' => $this->customer->id,
            'barber_id' => $barber->id,
            'total_amount' => 100.00,
            'status' => 'confirmed'
        ]);

        $this->paymentMethod = PaymentMethod::factory()->create([
            'name' => 'Credit Card',
            'type' => 'card',
            'provider' => 'stripe'
        ]);
    }

    /** @test */
    public function customer_can_process_payment_for_booking()
    {
        Sanctum::actingAs($this->customer);

        // Mock payment service
        $paymentService = Mockery::mock(PaymentService::class);
        $paymentService->shouldReceive('processPayment')
            ->once()
            ->andReturn([
                'success' => true,
                'transaction_id' => 'txn_123456',
                'amount' => 100.00
            ]);

        $this->app->instance(PaymentService::class, $paymentService);

        $paymentData = [
            'booking_id' => $this->booking->id,
            'payment_method_id' => $this->paymentMethod->id,
            'amount' => 100.00,
            'payment_details' => [
                'card_number' => '****************',
                'exp_month' => '12',
                'exp_year' => '2025',
                'cvc' => '123'
            ]
        ];

        $response = $this->postJson('/api/payments', $paymentData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'transaction_id',
                        'amount',
                        'status'
                    ]
                ]);

        $this->assertDatabaseHas('payments', [
            'booking_id' => $this->booking->id,
            'amount' => 100.00,
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function payment_fails_with_invalid_card()
    {
        Sanctum::actingAs($this->customer);

        // Mock payment service to return failure
        $paymentService = Mockery::mock(PaymentService::class);
        $paymentService->shouldReceive('processPayment')
            ->once()
            ->andReturn([
                'success' => false,
                'error' => 'Invalid card number'
            ]);

        $this->app->instance(PaymentService::class, $paymentService);

        $paymentData = [
            'booking_id' => $this->booking->id,
            'payment_method_id' => $this->paymentMethod->id,
            'amount' => 100.00,
            'payment_details' => [
                'card_number' => '****************', // Declined card
                'exp_month' => '12',
                'exp_year' => '2025',
                'cvc' => '123'
            ]
        ];

        $response = $this->postJson('/api/payments', $paymentData);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Invalid card number'
                ]);

        $this->assertDatabaseHas('payments', [
            'booking_id' => $this->booking->id,
            'status' => 'failed'
        ]);
    }

    /** @test */
    public function customer_can_view_payment_history()
    {
        Sanctum::actingAs($this->customer);

        // Create test payments
        Payment::factory()->count(3)->create([
            'booking_id' => $this->booking->id,
            'payment_method_id' => $this->paymentMethod->id
        ]);

        $response = $this->getJson('/api/payments');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'amount',
                            'status',
                            'booking' => ['booking_number'],
                            'payment_method' => ['name']
                        ]
                    ]
                ]);

        $this->assertCount(3, $response->json('data'));
    }

    /** @test */
    public function customer_can_request_refund()
    {
        Sanctum::actingAs($this->customer);

        $payment = Payment::factory()->create([
            'booking_id' => $this->booking->id,
            'payment_method_id' => $this->paymentMethod->id,
            'status' => 'completed',
            'transaction_id' => 'txn_123456'
        ]);

        // Mock payment service for refund
        $paymentService = Mockery::mock(PaymentService::class);
        $paymentService->shouldReceive('refundPayment')
            ->once()
            ->with('txn_123456', 100.00)
            ->andReturn([
                'success' => true,
                'refund_id' => 'ref_123456'
            ]);

        $this->app->instance(PaymentService::class, $paymentService);

        $response = $this->postJson("/api/payments/{$payment->id}/refund", [
            'reason' => 'Service cancelled'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'تم استرداد المبلغ بنجاح'
                ]);

        $this->assertDatabaseHas('payments', [
            'id' => $payment->id,
            'status' => 'refunded'
        ]);
    }

    /** @test */
    public function payment_amount_must_match_booking_total()
    {
        Sanctum::actingAs($this->customer);

        $paymentData = [
            'booking_id' => $this->booking->id,
            'payment_method_id' => $this->paymentMethod->id,
            'amount' => 50.00, // Less than booking total
            'payment_details' => [
                'card_number' => '****************',
                'exp_month' => '12',
                'exp_year' => '2025',
                'cvc' => '123'
            ]
        ];

        $response = $this->postJson('/api/payments', $paymentData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['amount']);
    }

    /** @test */
    public function cannot_pay_for_already_paid_booking()
    {
        Sanctum::actingAs($this->customer);

        // Create existing successful payment
        Payment::factory()->create([
            'booking_id' => $this->booking->id,
            'payment_method_id' => $this->paymentMethod->id,
            'status' => 'completed'
        ]);

        $paymentData = [
            'booking_id' => $this->booking->id,
            'payment_method_id' => $this->paymentMethod->id,
            'amount' => 100.00,
            'payment_details' => [
                'card_number' => '****************',
                'exp_month' => '12',
                'exp_year' => '2025',
                'cvc' => '123'
            ]
        ];

        $response = $this->postJson('/api/payments', $paymentData);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'تم دفع هذا الحجز مسبقاً'
                ]);
    }

    /** @test */
    public function customer_can_pay_with_wallet()
    {
        Sanctum::actingAs($this->customer);

        $walletPaymentMethod = PaymentMethod::factory()->create([
            'name' => 'فوري',
            'type' => 'wallet',
            'provider' => 'paymob'
        ]);

        // Mock payment service for wallet payment
        $paymentService = Mockery::mock(PaymentService::class);
        $paymentService->shouldReceive('processPayment')
            ->once()
            ->andReturn([
                'success' => true,
                'transaction_id' => 'wallet_123456',
                'amount' => 100.00
            ]);

        $this->app->instance(PaymentService::class, $paymentService);

        $paymentData = [
            'booking_id' => $this->booking->id,
            'payment_method_id' => $walletPaymentMethod->id,
            'amount' => 100.00,
            'payment_details' => [
                'phone_number' => '+201234567890'
            ]
        ];

        $response = $this->postJson('/api/payments', $paymentData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('payments', [
            'booking_id' => $this->booking->id,
            'payment_method_id' => $walletPaymentMethod->id,
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function customer_can_pay_cash_on_delivery()
    {
        Sanctum::actingAs($this->customer);

        $cashPaymentMethod = PaymentMethod::factory()->create([
            'name' => 'كاش',
            'type' => 'cash'
        ]);

        $paymentData = [
            'booking_id' => $this->booking->id,
            'payment_method_id' => $cashPaymentMethod->id,
            'amount' => 100.00
        ];

        $response = $this->postJson('/api/payments', $paymentData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('payments', [
            'booking_id' => $this->booking->id,
            'payment_method_id' => $cashPaymentMethod->id,
            'status' => 'pending' // Cash payments are pending until confirmed
        ]);
    }

    /** @test */
    public function barber_can_confirm_cash_payment()
    {
        $barberUser = User::factory()->create();
        $barber = Barber::factory()->create(['user_id' => $barberUser->id]);
        
        $booking = Booking::factory()->create([
            'customer_id' => $this->customer->id,
            'barber_id' => $barber->id
        ]);

        $cashPaymentMethod = PaymentMethod::factory()->create([
            'type' => 'cash'
        ]);

        $payment = Payment::factory()->create([
            'booking_id' => $booking->id,
            'payment_method_id' => $cashPaymentMethod->id,
            'status' => 'pending'
        ]);

        Sanctum::actingAs($barberUser);

        $response = $this->putJson("/api/barber/payments/{$payment->id}/confirm");

        $response->assertStatus(200);

        $this->assertDatabaseHas('payments', [
            'id' => $payment->id,
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function payment_webhook_updates_payment_status()
    {
        $payment = Payment::factory()->create([
            'booking_id' => $this->booking->id,
            'payment_method_id' => $this->paymentMethod->id,
            'status' => 'processing',
            'transaction_id' => 'txn_123456'
        ]);

        $webhookData = [
            'event_type' => 'payment.succeeded',
            'transaction_id' => 'txn_123456',
            'amount' => 100.00,
            'currency' => 'EGP'
        ];

        $response = $this->postJson('/api/webhooks/payment', $webhookData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('payments', [
            'id' => $payment->id,
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function payment_includes_commission_calculation()
    {
        Sanctum::actingAs($this->customer);

        // Mock payment service
        $paymentService = Mockery::mock(PaymentService::class);
        $paymentService->shouldReceive('processPayment')
            ->once()
            ->andReturn([
                'success' => true,
                'transaction_id' => 'txn_123456',
                'amount' => 100.00
            ]);

        $this->app->instance(PaymentService::class, $paymentService);

        $paymentData = [
            'booking_id' => $this->booking->id,
            'payment_method_id' => $this->paymentMethod->id,
            'amount' => 100.00,
            'payment_details' => [
                'card_number' => '****************',
                'exp_month' => '12',
                'exp_year' => '2025',
                'cvc' => '123'
            ]
        ];

        $response = $this->postJson('/api/payments', $paymentData);

        $response->assertStatus(201);

        // Check that booking commission was calculated
        $this->booking->refresh();
        $this->assertEquals(15.00, $this->booking->commission_amount); // 15% of 100
        $this->assertEquals(85.00, $this->booking->barber_amount); // 100 - 15
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
