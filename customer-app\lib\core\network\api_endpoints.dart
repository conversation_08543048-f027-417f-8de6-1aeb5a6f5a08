class ApiEndpoints {
  // Base paths
  static const String auth = '/auth/user';
  static const String settings = '/settings';
  static const String locations = '/locations';
  static const String services = '/services';
  static const String store = '/store';
  static const String user = '/user';
  static const String barber = '/barber';
  static const String admin = '/admin';

  // Authentication Endpoints
  static const String login = '$auth/login';
  static const String register = '$auth/register';
  static const String logout = '$auth/logout';
  static const String refreshToken = '$auth/refresh';
  static const String verifyPhone = '$auth/verify-phone';
  static const String resendOtp = '$auth/resend-otp';
  static const String forgotPassword = '$auth/forgot-password';
  static const String resetPassword = '$auth/reset-password';
  static const String changePassword = '$auth/change-password';
  static const String deleteAccount = '$auth/delete-account';

  // Settings Endpoints
  static const String publicSettings = '$settings/public';
  static const String appConfig = '$settings/app-config';
  static const String featureFlags = '$settings/features';
  static const String bookingConfig = '$settings/booking-config';
  static const String vipConfig = '$settings/vip-config';
  static const String paymentConfig = '$settings/payment-config';
  static const String maintenanceCheck = '$settings/maintenance';

  // Location Endpoints
  static const String cities = '$locations/cities';
  static const String cityAreas = '$locations/cities/{id}/areas';
  static const String governorates = '$locations/governorates';

  // Service Endpoints
  static const String servicesList = '$services';
  static const String serviceCategories = '$services/categories';
  static const String serviceDetails = '$services/{id}';

  // Store Endpoints
  static const String storeProducts = '$store/products';
  static const String storeCategories = '$store/categories';
  static const String productDetails = '$store/products/{id}';
  static const String featuredProducts = '$store/featured';

  // User Profile Endpoints
  static const String userProfile = '$user/profile';
  static const String updateProfile = '$user/profile';
  static const String uploadAvatar = '$user/profile/avatar';

  // User Booking Endpoints
  static const String userBookings = '$user/bookings';
  static const String createBooking = '$user/bookings';
  static const String bookingDetails = '$user/bookings/{id}';
  static const String cancelBooking = '$user/bookings/{id}/cancel';
  static const String rateBooking = '$user/bookings/{id}/rate';

  // User VIP Endpoints
  static const String vipSubscription = '$user/vip/subscription';
  static const String subscribeVip = '$user/vip/subscribe';
  static const String cancelVipSubscription = '$user/vip/cancel';

  // User Wallet Endpoints
  static const String walletBalance = '$user/wallet/balance';
  static const String walletTransactions = '$user/wallet/transactions';
  static const String addFunds = '$user/wallet/add-funds';

  // User Loyalty Endpoints
  static const String loyaltyPoints = '$user/loyalty/points';
  static const String loyaltyHistory = '$user/loyalty/history';
  static const String redeemPoints = '$user/loyalty/redeem';

  // User Addresses
  static const String userAddresses = '$user/addresses';
  static const String createAddress = '$user/addresses';
  static const String updateAddress = '$user/addresses/{id}';
  static const String deleteAddress = '$user/addresses/{id}';
  static const String setDefaultAddress = '$user/addresses/{id}/default';

  // User Notifications
  static const String userNotifications = '$user/notifications';
  static const String markNotificationRead = '$user/notifications/{id}/read';
  static const String markAllNotificationsRead = '$user/notifications/read-all';
  static const String notificationSettings = '$user/notifications/settings';
  static const String updateNotificationSettings = '$user/notifications/settings';

  // Barber Endpoints (for customer app to view barber info)
  static const String availableBarbers = '/barbers/available';
  static const String barberProfile = '/barbers/{id}';
  static const String barberServices = '/barbers/{id}/services';
  static const String barberAvailability = '/barbers/{id}/availability';
  static const String barberReviews = '/barbers/{id}/reviews';

  // Booking Flow Endpoints
  static const String searchBarbers = '/booking/search-barbers';
  static const String checkAvailability = '/booking/check-availability';
  static const String calculatePrice = '/booking/calculate-price';
  static const String applyCoupon = '/booking/apply-coupon';
  static const String createBookingRequest = '/booking/create';
  static const String confirmBooking = '/booking/{id}/confirm';

  // Payment Endpoints
  static const String paymentMethods = '/payment/methods';
  static const String createPaymentIntent = '/payment/create-intent';
  static const String confirmPayment = '/payment/confirm';
  static const String paymentHistory = '/payment/history';
  static const String refundPayment = '/payment/{id}/refund';

  // Support Endpoints
  static const String supportTickets = '/support/tickets';
  static const String createSupportTicket = '/support/tickets';
  static const String supportTicketDetails = '/support/tickets/{id}';
  static const String supportTicketMessages = '/support/tickets/{id}/messages';
  static const String sendSupportMessage = '/support/tickets/{id}/messages';

  // Rating & Review Endpoints
  static const String submitReview = '/reviews';
  static const String userReviews = '/reviews/user';
  static const String barberReviews = '/reviews/barber/{id}';

  // Coupon Endpoints
  static const String validateCoupon = '/coupons/validate';
  static const String userCoupons = '/coupons/user';

  // Analytics Endpoints (for user behavior tracking)
  static const String trackEvent = '/analytics/track';
  static const String userAnalytics = '/analytics/user';

  // File Upload Endpoints
  static const String uploadImage = '/upload/image';
  static const String uploadDocument = '/upload/document';

  // Utility Methods
  static String replacePathParameter(String endpoint, String parameter, String value) {
    return endpoint.replaceAll('{$parameter}', value);
  }

  static String getCityAreas(String cityId) {
    return replacePathParameter(cityAreas, 'id', cityId);
  }

  static String getServiceDetails(String serviceId) {
    return replacePathParameter(serviceDetails, 'id', serviceId);
  }

  static String getProductDetails(String productId) {
    return replacePathParameter(productDetails, 'id', productId);
  }

  static String getBookingDetails(String bookingId) {
    return replacePathParameter(bookingDetails, 'id', bookingId);
  }

  static String getCancelBooking(String bookingId) {
    return replacePathParameter(cancelBooking, 'id', bookingId);
  }

  static String getRateBooking(String bookingId) {
    return replacePathParameter(rateBooking, 'id', bookingId);
  }

  static String getBarberProfile(String barberId) {
    return replacePathParameter(barberProfile, 'id', barberId);
  }

  static String getBarberServices(String barberId) {
    return replacePathParameter(barberServices, 'id', barberId);
  }

  static String getBarberAvailability(String barberId) {
    return replacePathParameter(barberAvailability, 'id', barberId);
  }

  static String getBarberReviews(String barberId) {
    return replacePathParameter(barberReviews, 'id', barberId);
  }

  static String getUpdateAddress(String addressId) {
    return replacePathParameter(updateAddress, 'id', addressId);
  }

  static String getDeleteAddress(String addressId) {
    return replacePathParameter(deleteAddress, 'id', addressId);
  }

  static String getSetDefaultAddress(String addressId) {
    return replacePathParameter(setDefaultAddress, 'id', addressId);
  }

  static String getMarkNotificationRead(String notificationId) {
    return replacePathParameter(markNotificationRead, 'id', notificationId);
  }

  static String getConfirmBooking(String bookingId) {
    return replacePathParameter(confirmBooking, 'id', bookingId);
  }

  static String getRefundPayment(String paymentId) {
    return replacePathParameter(refundPayment, 'id', paymentId);
  }

  static String getSupportTicketDetails(String ticketId) {
    return replacePathParameter(supportTicketDetails, 'id', ticketId);
  }

  static String getSupportTicketMessages(String ticketId) {
    return replacePathParameter(supportTicketMessages, 'id', ticketId);
  }

  static String getSendSupportMessage(String ticketId) {
    return replacePathParameter(sendSupportMessage, 'id', ticketId);
  }

  static String getBarberReviewsEndpoint(String barberId) {
    return replacePathParameter(barberReviews, 'id', barberId);
  }
}
