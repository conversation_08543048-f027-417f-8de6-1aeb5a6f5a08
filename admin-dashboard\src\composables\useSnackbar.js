import { ref } from 'vue'

const snackbar = ref({
  show: false,
  message: '',
  color: 'info',
  timeout: 4000,
  multiLine: false,
  vertical: false
})

export function useSnackbar() {
  const showSnackbar = (message, color = 'info', timeout = 4000) => {
    snackbar.value = {
      show: true,
      message,
      color,
      timeout,
      multiLine: message.length > 50,
      vertical: false
    }
  }

  const hideSnackbar = () => {
    snackbar.value.show = false
  }

  const showSuccess = (message, timeout = 4000) => {
    showSnackbar(message, 'success', timeout)
  }

  const showError = (message, timeout = 6000) => {
    showSnackbar(message, 'error', timeout)
  }

  const showWarning = (message, timeout = 5000) => {
    showSnackbar(message, 'warning', timeout)
  }

  const showInfo = (message, timeout = 4000) => {
    showSnackbar(message, 'info', timeout)
  }

  return {
    snackbar,
    showSnackbar,
    hideSnackbar,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
}
