import io from 'socket.io-client'
import { EventEmitter } from 'events'

class RealTimeAnalyticsService extends EventEmitter {
  constructor() {
    super()
    this.socket = null
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
    this.metrics = new Map()
    this.dashboards = new Map()
    this.alerts = []
    this.subscribers = new Map()
  }

  // Initialize real-time analytics connection
  async initialize(config = {}) {
    try {
      const socketConfig = {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
        ...config
      }

      this.socket = io(process.env.VUE_APP_ANALYTICS_WS_URL || 'ws://localhost:3001', socketConfig)

      this.setupEventListeners()
      await this.waitForConnection()
      
      console.log('Real-time analytics service initialized successfully')
      return true
    } catch (error) {
      console.error('Failed to initialize real-time analytics:', error)
      return false
    }
  }

  // Setup socket event listeners
  setupEventListeners() {
    this.socket.on('connect', () => {
      this.isConnected = true
      this.reconnectAttempts = 0
      console.log('Connected to analytics server')
      this.emit('connected')
      this.subscribeToDefaultChannels()
    })

    this.socket.on('disconnect', (reason) => {
      this.isConnected = false
      console.log('Disconnected from analytics server:', reason)
      this.emit('disconnected', reason)
    })

    this.socket.on('reconnect', (attemptNumber) => {
      console.log('Reconnected to analytics server, attempt:', attemptNumber)
      this.emit('reconnected', attemptNumber)
    })

    this.socket.on('reconnect_error', (error) => {
      this.reconnectAttempts++
      console.error('Reconnection failed:', error)
      this.emit('reconnect_error', error)
    })

    // Analytics-specific events
    this.socket.on('metrics_update', (data) => {
      this.handleMetricsUpdate(data)
    })

    this.socket.on('alert_triggered', (alert) => {
      this.handleAlert(alert)
    })

    this.socket.on('dashboard_update', (dashboard) => {
      this.handleDashboardUpdate(dashboard)
    })

    this.socket.on('user_activity', (activity) => {
      this.handleUserActivity(activity)
    })

    this.socket.on('system_health', (health) => {
      this.handleSystemHealth(health)
    })

    this.socket.on('performance_metrics', (metrics) => {
      this.handlePerformanceMetrics(metrics)
    })

    this.socket.on('business_metrics', (metrics) => {
      this.handleBusinessMetrics(metrics)
    })

    this.socket.on('security_events', (events) => {
      this.handleSecurityEvents(events)
    })
  }

  // Wait for socket connection
  waitForConnection(timeout = 10000) {
    return new Promise((resolve, reject) => {
      if (this.isConnected) {
        resolve()
        return
      }

      const timer = setTimeout(() => {
        reject(new Error('Connection timeout'))
      }, timeout)

      this.socket.once('connect', () => {
        clearTimeout(timer)
        resolve()
      })

      this.socket.once('connect_error', (error) => {
        clearTimeout(timer)
        reject(error)
      })
    })
  }

  // Subscribe to default analytics channels
  subscribeToDefaultChannels() {
    const defaultChannels = [
      'system_metrics',
      'user_analytics',
      'business_metrics',
      'performance_data',
      'security_events',
      'alerts'
    ]

    defaultChannels.forEach(channel => {
      this.subscribe(channel)
    })
  }

  // Subscribe to specific analytics channel
  subscribe(channel, callback = null) {
    if (!this.isConnected) {
      console.warn('Cannot subscribe: not connected to analytics server')
      return false
    }

    this.socket.emit('subscribe', { channel })
    
    if (callback) {
      this.subscribers.set(channel, callback)
    }

    console.log(`Subscribed to analytics channel: ${channel}`)
    return true
  }

  // Unsubscribe from analytics channel
  unsubscribe(channel) {
    if (!this.isConnected) {
      return false
    }

    this.socket.emit('unsubscribe', { channel })
    this.subscribers.delete(channel)
    
    console.log(`Unsubscribed from analytics channel: ${channel}`)
    return true
  }

  // Handle metrics updates
  handleMetricsUpdate(data) {
    const { metric, value, timestamp, metadata } = data
    
    this.metrics.set(metric, {
      value,
      timestamp: new Date(timestamp),
      metadata,
      history: this.metrics.get(metric)?.history || []
    })

    // Add to history (keep last 100 points)
    const currentMetric = this.metrics.get(metric)
    currentMetric.history.push({ value, timestamp: new Date(timestamp) })
    if (currentMetric.history.length > 100) {
      currentMetric.history.shift()
    }

    this.emit('metric_updated', { metric, data: currentMetric })

    // Notify specific subscribers
    const callback = this.subscribers.get('metrics')
    if (callback) {
      callback({ metric, data: currentMetric })
    }
  }

  // Handle alerts
  handleAlert(alert) {
    const alertData = {
      id: alert.id || Date.now(),
      type: alert.type,
      severity: alert.severity,
      title: alert.title,
      message: alert.message,
      timestamp: new Date(alert.timestamp),
      metadata: alert.metadata || {},
      acknowledged: false
    }

    this.alerts.unshift(alertData)
    
    // Keep only last 1000 alerts
    if (this.alerts.length > 1000) {
      this.alerts = this.alerts.slice(0, 1000)
    }

    this.emit('alert_received', alertData)

    // Show browser notification for critical alerts
    if (alert.severity === 'critical' && 'Notification' in window) {
      new Notification(alert.title, {
        body: alert.message,
        icon: '/icons/alert.png',
        tag: alert.id
      })
    }
  }

  // Handle dashboard updates
  handleDashboardUpdate(dashboard) {
    this.dashboards.set(dashboard.id, {
      ...dashboard,
      lastUpdated: new Date()
    })

    this.emit('dashboard_updated', dashboard)
  }

  // Handle user activity
  handleUserActivity(activity) {
    this.emit('user_activity', {
      ...activity,
      timestamp: new Date(activity.timestamp)
    })
  }

  // Handle system health updates
  handleSystemHealth(health) {
    this.emit('system_health', {
      ...health,
      timestamp: new Date(health.timestamp)
    })
  }

  // Handle performance metrics
  handlePerformanceMetrics(metrics) {
    this.emit('performance_metrics', {
      ...metrics,
      timestamp: new Date(metrics.timestamp)
    })
  }

  // Handle business metrics
  handleBusinessMetrics(metrics) {
    this.emit('business_metrics', {
      ...metrics,
      timestamp: new Date(metrics.timestamp)
    })
  }

  // Handle security events
  handleSecurityEvents(events) {
    this.emit('security_events', {
      ...events,
      timestamp: new Date(events.timestamp)
    })
  }

  // Get current metric value
  getMetric(metricName) {
    return this.metrics.get(metricName)
  }

  // Get all metrics
  getAllMetrics() {
    return Object.fromEntries(this.metrics)
  }

  // Get metric history
  getMetricHistory(metricName, limit = 50) {
    const metric = this.metrics.get(metricName)
    if (!metric || !metric.history) {
      return []
    }

    return metric.history.slice(-limit)
  }

  // Get recent alerts
  getRecentAlerts(limit = 20) {
    return this.alerts.slice(0, limit)
  }

  // Acknowledge alert
  acknowledgeAlert(alertId) {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.acknowledged = true
      alert.acknowledgedAt = new Date()
      
      if (this.isConnected) {
        this.socket.emit('acknowledge_alert', { alertId })
      }
    }
  }

  // Get dashboard data
  getDashboard(dashboardId) {
    return this.dashboards.get(dashboardId)
  }

  // Request specific analytics data
  requestAnalytics(query) {
    if (!this.isConnected) {
      return Promise.reject(new Error('Not connected to analytics server'))
    }

    return new Promise((resolve, reject) => {
      const requestId = Date.now().toString()
      
      const timeout = setTimeout(() => {
        this.socket.off(`analytics_response_${requestId}`)
        reject(new Error('Analytics request timeout'))
      }, 30000)

      this.socket.once(`analytics_response_${requestId}`, (response) => {
        clearTimeout(timeout)
        if (response.error) {
          reject(new Error(response.error))
        } else {
          resolve(response.data)
        }
      })

      this.socket.emit('analytics_request', {
        requestId,
        query
      })
    })
  }

  // Create custom dashboard
  async createDashboard(dashboardConfig) {
    try {
      const response = await this.requestAnalytics({
        type: 'create_dashboard',
        config: dashboardConfig
      })

      this.dashboards.set(response.id, response)
      return response
    } catch (error) {
      console.error('Failed to create dashboard:', error)
      throw error
    }
  }

  // Update dashboard
  async updateDashboard(dashboardId, updates) {
    try {
      const response = await this.requestAnalytics({
        type: 'update_dashboard',
        dashboardId,
        updates
      })

      this.dashboards.set(dashboardId, response)
      return response
    } catch (error) {
      console.error('Failed to update dashboard:', error)
      throw error
    }
  }

  // Get real-time user statistics
  async getUserStatistics(timeRange = '1h') {
    return this.requestAnalytics({
      type: 'user_statistics',
      timeRange
    })
  }

  // Get real-time booking statistics
  async getBookingStatistics(timeRange = '1h') {
    return this.requestAnalytics({
      type: 'booking_statistics',
      timeRange
    })
  }

  // Get real-time revenue statistics
  async getRevenueStatistics(timeRange = '1h') {
    return this.requestAnalytics({
      type: 'revenue_statistics',
      timeRange
    })
  }

  // Get system performance metrics
  async getSystemPerformance() {
    return this.requestAnalytics({
      type: 'system_performance'
    })
  }

  // Get geographic analytics
  async getGeographicAnalytics(timeRange = '24h') {
    return this.requestAnalytics({
      type: 'geographic_analytics',
      timeRange
    })
  }

  // Get user behavior analytics
  async getUserBehaviorAnalytics(timeRange = '7d') {
    return this.requestAnalytics({
      type: 'user_behavior',
      timeRange
    })
  }

  // Get conversion funnel analytics
  async getConversionFunnel(timeRange = '30d') {
    return this.requestAnalytics({
      type: 'conversion_funnel',
      timeRange
    })
  }

  // Get A/B test results
  async getABTestResults(testId) {
    return this.requestAnalytics({
      type: 'ab_test_results',
      testId
    })
  }

  // Send custom event
  sendEvent(eventName, eventData) {
    if (!this.isConnected) {
      console.warn('Cannot send event: not connected to analytics server')
      return false
    }

    this.socket.emit('custom_event', {
      event: eventName,
      data: eventData,
      timestamp: new Date().toISOString()
    })

    return true
  }

  // Set up alert rules
  async createAlertRule(rule) {
    return this.requestAnalytics({
      type: 'create_alert_rule',
      rule
    })
  }

  // Update alert rule
  async updateAlertRule(ruleId, updates) {
    return this.requestAnalytics({
      type: 'update_alert_rule',
      ruleId,
      updates
    })
  }

  // Delete alert rule
  async deleteAlertRule(ruleId) {
    return this.requestAnalytics({
      type: 'delete_alert_rule',
      ruleId
    })
  }

  // Export analytics data
  async exportData(exportConfig) {
    return this.requestAnalytics({
      type: 'export_data',
      config: exportConfig
    })
  }

  // Get connection status
  getConnectionStatus() {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      lastConnected: this.lastConnected,
      serverUrl: this.socket?.io?.uri
    }
  }

  // Disconnect from analytics server
  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    
    this.isConnected = false
    this.metrics.clear()
    this.dashboards.clear()
    this.alerts = []
    this.subscribers.clear()
    
    console.log('Disconnected from analytics server')
  }

  // Cleanup resources
  destroy() {
    this.disconnect()
    this.removeAllListeners()
  }
}

// Create singleton instance
const realTimeAnalytics = new RealTimeAnalyticsService()

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
  // Request notification permission
  if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission()
  }

  // Initialize on page load
  document.addEventListener('DOMContentLoaded', () => {
    realTimeAnalytics.initialize().catch(console.error)
  })

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    realTimeAnalytics.disconnect()
  })
}

export default realTimeAnalytics
