<?php
session_start();

// Simple authentication
$admin_username = '<EMAIL>';
$admin_password = 'admin123';

// Initialize data arrays if not exist
if (!isset($_SESSION['bookings'])) {
    $_SESSION['bookings'] = [
        ['id' => 1, 'customer' => 'أحمد محمد', 'barber' => 'محمد الحلاق', 'service' => 'قص شعر', 'date' => '2024-01-15', 'time' => '10:00', 'status' => 'مؤكد', 'price' => 50],
        ['id' => 2, 'customer' => 'سارة أحمد', 'barber' => 'فاطمة الحلاقة', 'service' => 'تصفيف شعر', 'date' => '2024-01-15', 'time' => '11:30', 'status' => 'في الانتظار', 'price' => 80]
    ];
}

// نظام الدول والمحافظات
if (!isset($_SESSION['countries_cities'])) {
    $_SESSION['countries_cities'] = [
        'مصر' => [
            'القاهرة', 'الجيزة', 'الإسكندرية', 'الشرقية', 'المنوفية', 'القليوبية', 'البحيرة', 'الغربية',
            'كفر الشيخ', 'الدقهلية', 'دمياط', 'بورسعيد', 'الإسماعيلية', 'السويس', 'شمال سيناء', 'جنوب سيناء',
            'الفيوم', 'بني سويف', 'المنيا', 'أسيوط', 'سوهاج', 'قنا', 'الأقصر', 'أسوان', 'البحر الأحمر',
            'الوادي الجديد', 'مطروح'
        ],
        'السعودية' => [
            'الرياض', 'جدة', 'مكة المكرمة', 'المدينة المنورة', 'الدمام', 'الخبر', 'الظهران', 'الطائف',
            'بريدة', 'تبوك', 'خميس مشيط', 'حائل', 'الجبيل', 'ينبع', 'الأحساء', 'نجران', 'جازان',
            'عرعر', 'سكاكا', 'أبها'
        ],
        'الإمارات' => [
            'دبي', 'أبوظبي', 'الشارقة', 'عجمان', 'أم القيوين', 'رأس الخيمة', 'الفجيرة'
        ],
        'الكويت' => [
            'العاصمة', 'حولي', 'الفروانية', 'مبارك الكبير', 'الأحمدي', 'الجهراء'
        ],
        'قطر' => [
            'الدوحة', 'الريان', 'الوكرة', 'أم صلال', 'الخور', 'الشمال', 'الضعاين', 'الشحانية'
        ],
        'البحرين' => [
            'المنامة', 'المحرق', 'الشمالية', 'الجنوبية'
        ],
        'عمان' => [
            'مسقط', 'صلالة', 'صحار', 'نزوى', 'صور', 'الرستاق', 'البريمي', 'مطرح'
        ],
        'الأردن' => [
            'عمان', 'إربد', 'الزرقاء', 'المفرق', 'الكرك', 'معان', 'الطفيلة', 'مادبا', 'جرش', 'عجلون', 'البلقاء', 'العقبة'
        ],
        'لبنان' => [
            'بيروت', 'جبل لبنان', 'الشمال', 'الجنوب', 'البقاع', 'بعلبك الهرمل', 'النبطية', 'عكار'
        ],
        'سوريا' => [
            'دمشق', 'حلب', 'حمص', 'حماة', 'اللاذقية', 'دير الزور', 'الحسكة', 'إدلب', 'درعا', 'السويداء', 'القنيطرة', 'الرقة', 'طرطوس', 'ريف دمشق'
        ],
        'العراق' => [
            'بغداد', 'البصرة', 'الموصل', 'أربيل', 'النجف', 'كربلاء', 'السليمانية', 'الأنبار', 'دهوك', 'كركوك', 'بابل', 'ديالى', 'ذي قار', 'المثنى', 'القادسية', 'واسط', 'ميسان', 'صلاح الدين'
        ],
        'المغرب' => [
            'الدار البيضاء', 'الرباط', 'فاس', 'مراكش', 'أغادير', 'مكناس', 'وجدة', 'القنيطرة', 'تطوان', 'تمارة', 'سلا', 'المحمدية', 'خريبكة', 'بني ملال', 'الجديدة'
        ],
        'الجزائر' => [
            'الجزائر', 'وهران', 'قسنطينة', 'عنابة', 'باتنة', 'سطيف', 'سيدي بلعباس', 'بسكرة', 'تلمسان', 'بجاية', 'تيزي وزو', 'الشلف', 'تبسة', 'بشار', 'تيارت'
        ],
        'تونس' => [
            'تونس', 'صفاقس', 'سوسة', 'القيروان', 'بنزرت', 'قابس', 'أريانة', 'قفصة', 'المنستير', 'مدنين', 'تطاوين', 'الكاف', 'قبلي', 'سليانة'
        ],
        'ليبيا' => [
            'طرابلس', 'بنغازي', 'مصراتة', 'الزاوية', 'البيضاء', 'طبرق', 'الخمس', 'زليتن', 'أجدابيا', 'درنة', 'سبها', 'غريان'
        ],
        'السودان' => [
            'الخرطوم', 'أم درمان', 'بحري', 'بورتسودان', 'كسلا', 'القضارف', 'الأبيض', 'نيالا', 'الفاشر', 'كادوقلي', 'الدمازين', 'ربك'
        ],
        'فلسطين' => [
            'القدس', 'غزة', 'الخليل', 'نابلس', 'رام الله', 'بيت لحم', 'جنين', 'طولكرم', 'قلقيلية', 'سلفيت', 'أريحا', 'طوباس'
        ]
    ];
}

if (!isset($_SESSION['barbers'])) {
    $_SESSION['barbers'] = [
        ['id' => 1, 'name' => 'أحمد محمد', 'phone' => '01012345678', 'experience' => 5, 'specialty' => 'حلاقة رجالي', 'status' => 'متاح', 'country' => 'مصر', 'city' => 'القاهرة', 'address' => 'مدينة نصر، شارع مصطفى النحاس', 'rating' => 4.8, 'completed_services' => 156],
        ['id' => 2, 'name' => 'فاطمة علي', 'phone' => '01098765432', 'experience' => 3, 'specialty' => 'تصفيف نسائي', 'status' => 'مشغولة', 'country' => 'مصر', 'city' => 'الإسكندرية', 'address' => 'سيدي جابر، شارع الحرية', 'rating' => 4.6, 'completed_services' => 89],
        ['id' => 3, 'name' => 'محمد الحلاق', 'phone' => '0501234567', 'experience' => 7, 'specialty' => 'حلاقة رجالي', 'status' => 'متاح', 'country' => 'السعودية', 'city' => 'الرياض', 'address' => 'حي النخيل، شارع الملك فهد', 'rating' => 4.9, 'completed_services' => 234]
    ];
}

if (!isset($_SESSION['customers'])) {
    $_SESSION['customers'] = [
        ['id' => 1, 'name' => 'أحمد محمد', 'email' => '<EMAIL>', 'phone' => '0501234567', 'bookings_count' => 12, 'join_date' => '2024-01-01'],
        ['id' => 2, 'name' => 'سارة أحمد', 'email' => '<EMAIL>', 'phone' => '0507654321', 'bookings_count' => 8, 'join_date' => '2024-01-05']
    ];
}

if (!isset($_SESSION['services'])) {
    $_SESSION['services'] = [
        ['id' => 1, 'name' => 'قص شعر رجالي', 'description' => 'قص شعر احترافي للرجال', 'price' => 50, 'duration' => 30, 'category' => 'رجالي'],
        ['id' => 2, 'name' => 'تصفيف شعر نسائي', 'description' => 'تصفيف وتسريح شعر للنساء', 'price' => 80, 'duration' => 45, 'category' => 'نسائي'],
        ['id' => 3, 'name' => 'حلاقة كاملة', 'description' => 'حلاقة شعر ولحية كاملة', 'price' => 70, 'duration' => 60, 'category' => 'رجالي'],
        ['id' => 4, 'name' => 'صبغة شعر', 'description' => 'صبغة شعر بألوان متنوعة', 'price' => 120, 'duration' => 90, 'category' => 'مشترك'],
        ['id' => 5, 'name' => 'تسريحة عروس', 'description' => 'تسريحة خاصة للعرائس', 'price' => 200, 'duration' => 120, 'category' => 'نسائي'],
        ['id' => 6, 'name' => 'تشذيب لحية', 'description' => 'تشذيب وتهذيب اللحية', 'price' => 30, 'duration' => 20, 'category' => 'رجالي']
    ];
}

if (!isset($_SESSION['payments'])) {
    $_SESSION['payments'] = [
        ['id' => 1, 'booking_id' => 1, 'amount' => 50, 'method' => 'نقدي', 'status' => 'مدفوع', 'date' => '2024-01-15'],
        ['id' => 2, 'booking_id' => 2, 'amount' => 80, 'method' => 'بطاقة ائتمان', 'status' => 'معلق', 'date' => '2024-01-15']
    ];
}

if (!isset($_SESSION['complaints'])) {
    $_SESSION['complaints'] = [
        ['id' => 1, 'customer_name' => 'أحمد محمد', 'customer_phone' => '0501234567', 'barber_name' => 'محمد الحلاق', 'complaint' => 'تأخر في الموعد لمدة 30 دقيقة', 'status' => 'جديد', 'priority' => 'متوسط', 'date' => '2024-01-15 10:30:00', 'admin_reply' => ''],
        ['id' => 2, 'customer_name' => 'سارة أحمد', 'customer_phone' => '0507654321', 'barber_name' => 'فاطمة الحلاقة', 'complaint' => 'عدم رضا عن جودة الخدمة المقدمة', 'status' => 'قيد المراجعة', 'priority' => 'عالي', 'date' => '2024-01-15 14:15:00', 'admin_reply' => 'جاري التحقق من الشكوى'],
        ['id' => 3, 'customer_name' => 'محمد علي', 'customer_phone' => '0509876543', 'barber_name' => 'محمد الحلاق', 'complaint' => 'عدم التزام بالسعر المتفق عليه', 'status' => 'مغلق', 'priority' => 'منخفض', 'date' => '2024-01-14 16:45:00', 'admin_reply' => 'تم حل المشكلة وإعادة الفرق في السعر']
    ];
}

if (!isset($_SESSION['messages'])) {
    $_SESSION['messages'] = [
        ['id' => 1, 'sender_name' => 'أحمد محمد', 'sender_phone' => '0501234567', 'sender_type' => 'عميل', 'subject' => 'استفسار عن الخدمات', 'message' => 'أريد معرفة أسعار خدمات تصفيف الشعر للمناسبات', 'status' => 'جديد', 'date' => '2024-01-15 09:20:00', 'admin_reply' => ''],
        ['id' => 2, 'sender_name' => 'فاطمة الحلاقة', 'sender_phone' => '0507654321', 'sender_type' => 'حلاق', 'subject' => 'طلب إجازة', 'message' => 'أريد أخذ إجازة لمدة 3 أيام الأسبوع القادم', 'status' => 'مقروء', 'date' => '2024-01-15 11:45:00', 'admin_reply' => 'تم الموافقة على الإجازة'],
        ['id' => 3, 'sender_name' => 'سارة أحمد', 'sender_phone' => '0509876543', 'sender_type' => 'عميل', 'subject' => 'اقتراح تحسين', 'message' => 'اقترح إضافة خدمة الحجز المسبق لأكثر من أسبوع', 'status' => 'مغلق', 'date' => '2024-01-14 13:30:00', 'admin_reply' => 'شكراً لاقتراحك، سيتم دراسته']
    ];
}

if (!isset($_SESSION['barber_bans'])) {
    $_SESSION['barber_bans'] = [
        ['id' => 1, 'barber_id' => 2, 'barber_name' => 'فاطمة الحلاقة', 'reason' => 'شكاوى متكررة من العملاء', 'ban_type' => 'مؤقت', 'start_date' => '2024-01-15', 'end_date' => '2024-01-22', 'status' => 'نشط', 'admin_notes' => 'حظر لمدة أسبوع للمراجعة']
    ];
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    switch ($_POST['action']) {
        case 'save_booking':
            $new_booking = [
                'id' => count($_SESSION['bookings']) + 1,
                'customer' => $_POST['customer'],
                'barber' => $_POST['barber'],
                'service' => $_POST['service'],
                'date' => $_POST['date'],
                'time' => $_POST['time'],
                'status' => 'مؤكد',
                'price' => (int)$_POST['price']
            ];
            $_SESSION['bookings'][] = $new_booking;
            echo json_encode(['success' => true, 'message' => 'تم حفظ الحجز بنجاح']);
            exit;

        case 'save_barber':
            $new_barber = [
                'id' => count($_SESSION['barbers']) + 1,
                'name' => $_POST['name'],
                'phone' => $_POST['phone'],
                'experience' => (int)$_POST['experience'],
                'specialty' => $_POST['specialty'],
                'country' => $_POST['country'],
                'city' => $_POST['city'],
                'address' => $_POST['address'],
                'status' => 'متاح',
                'rating' => 0,
                'completed_services' => 0
            ];
            $_SESSION['barbers'][] = $new_barber;
            echo json_encode(['success' => true, 'message' => 'تم إضافة الحلاق بنجاح']);
            exit;

        case 'update_booking':
            $booking_id = (int)$_POST['id'];
            foreach ($_SESSION['bookings'] as &$booking) {
                if ($booking['id'] === $booking_id) {
                    $booking['customer'] = $_POST['customer'];
                    $booking['barber'] = $_POST['barber'];
                    $booking['service'] = $_POST['service'];
                    $booking['date'] = $_POST['date'];
                    $booking['time'] = $_POST['time'];
                    $booking['price'] = (int)$_POST['price'];
                    break;
                }
            }
            echo json_encode(['success' => true, 'message' => 'تم تحديث الحجز بنجاح']);
            exit;

        case 'update_barber':
            $barber_id = (int)$_POST['id'];
            foreach ($_SESSION['barbers'] as &$barber) {
                if ($barber['id'] === $barber_id) {
                    $barber['name'] = $_POST['name'];
                    $barber['phone'] = $_POST['phone'];
                    $barber['experience'] = (int)$_POST['experience'];
                    $barber['specialty'] = $_POST['specialty'];
                    $barber['country'] = $_POST['country'];
                    $barber['city'] = $_POST['city'];
                    $barber['address'] = $_POST['address'];
                    break;
                }
            }
            echo json_encode(['success' => true, 'message' => 'تم تحديث بيانات الحلاق بنجاح']);
            exit;

        case 'update_customer':
            $customer_id = (int)$_POST['id'];
            foreach ($_SESSION['customers'] as &$customer) {
                if ($customer['id'] === $customer_id) {
                    $customer['name'] = $_POST['name'];
                    $customer['email'] = $_POST['email'];
                    $customer['phone'] = $_POST['phone'];
                    break;
                }
            }
            echo json_encode(['success' => true, 'message' => 'تم تحديث بيانات العميل بنجاح']);
            exit;

        case 'save_service':
            $new_service = [
                'id' => count($_SESSION['services']) + 1,
                'name' => $_POST['name'],
                'description' => $_POST['description'],
                'price' => (float)$_POST['price'],
                'duration' => (int)$_POST['duration'],
                'category' => $_POST['category']
            ];
            $_SESSION['services'][] = $new_service;
            echo json_encode(['success' => true, 'message' => 'تم إضافة الخدمة بنجاح']);
            exit;

        case 'update_service':
            $service_id = (int)$_POST['id'];
            foreach ($_SESSION['services'] as &$service) {
                if ($service['id'] === $service_id) {
                    $service['name'] = $_POST['name'];
                    $service['description'] = $_POST['description'];
                    $service['price'] = (float)$_POST['price'];
                    $service['duration'] = (int)$_POST['duration'];
                    $service['category'] = $_POST['category'];
                    break;
                }
            }
            echo json_encode(['success' => true, 'message' => 'تم تحديث الخدمة بنجاح']);
            exit;

        case 'delete_service':
            $service_id = (int)$_POST['id'];
            $_SESSION['services'] = array_filter($_SESSION['services'], function($service) use ($service_id) {
                return $service['id'] !== $service_id;
            });
            echo json_encode(['success' => true, 'message' => 'تم حذف الخدمة بنجاح']);
            exit;

        case 'delete_booking':
            $booking_id = (int)$_POST['id'];
            $_SESSION['bookings'] = array_filter($_SESSION['bookings'], function($booking) use ($booking_id) {
                return $booking['id'] !== $booking_id;
            });
            echo json_encode(['success' => true, 'message' => 'تم حذف الحجز بنجاح']);
            exit;

        case 'delete_barber':
            $barber_id = (int)$_POST['id'];
            $_SESSION['barbers'] = array_filter($_SESSION['barbers'], function($barber) use ($barber_id) {
                return $barber['id'] !== $barber_id;
            });
            echo json_encode(['success' => true, 'message' => 'تم حذف الحلاق بنجاح']);
            exit;

        case 'save_payment':
            $new_payment = [
                'id' => count($_SESSION['payments']) + 1,
                'booking_id' => (int)$_POST['booking_id'],
                'amount' => (float)$_POST['amount'],
                'method' => $_POST['method'],
                'status' => 'مدفوع',
                'date' => date('Y-m-d')
            ];
            $_SESSION['payments'][] = $new_payment;
            echo json_encode(['success' => true, 'message' => 'تم تسجيل الدفع بنجاح']);
            exit;

        case 'reply_complaint':
            $complaint_id = (int)$_POST['id'];
            $reply = $_POST['reply'];
            $status = $_POST['status'];

            foreach ($_SESSION['complaints'] as &$complaint) {
                if ($complaint['id'] === $complaint_id) {
                    $complaint['admin_reply'] = $reply;
                    $complaint['status'] = $status;
                    break;
                }
            }
            echo json_encode(['success' => true, 'message' => 'تم حفظ الرد بنجاح']);
            exit;

        case 'delete_complaint':
            $complaint_id = (int)$_POST['id'];
            $_SESSION['complaints'] = array_filter($_SESSION['complaints'], function($complaint) use ($complaint_id) {
                return $complaint['id'] !== $complaint_id;
            });
            echo json_encode(['success' => true, 'message' => 'تم حذف الشكوى بنجاح']);
            exit;

        case 'reply_message':
            $message_id = (int)$_POST['id'];
            $reply = $_POST['reply'];
            $status = $_POST['status'];

            foreach ($_SESSION['messages'] as &$message) {
                if ($message['id'] === $message_id) {
                    $message['admin_reply'] = $reply;
                    $message['status'] = $status;
                    break;
                }
            }
            echo json_encode(['success' => true, 'message' => 'تم حفظ الرد بنجاح']);
            exit;

        case 'delete_message':
            $message_id = (int)$_POST['id'];
            $_SESSION['messages'] = array_filter($_SESSION['messages'], function($message) use ($message_id) {
                return $message['id'] !== $message_id;
            });
            echo json_encode(['success' => true, 'message' => 'تم حذف الرسالة بنجاح']);
            exit;

        case 'ban_barber':
            $barber_id = (int)$_POST['barber_id'];
            $reason = $_POST['reason'];
            $ban_type = $_POST['ban_type'];
            $days = (int)$_POST['days'];

            // العثور على اسم الحلاق
            $barber_name = '';
            foreach ($_SESSION['barbers'] as $barber) {
                if ($barber['id'] === $barber_id) {
                    $barber_name = $barber['name'];
                    break;
                }
            }

            $new_ban = [
                'id' => count($_SESSION['barber_bans']) + 1,
                'barber_id' => $barber_id,
                'barber_name' => $barber_name,
                'reason' => $reason,
                'ban_type' => $ban_type,
                'start_date' => date('Y-m-d'),
                'end_date' => $ban_type === 'مؤقت' ? date('Y-m-d', strtotime("+$days days")) : null,
                'status' => 'نشط',
                'admin_notes' => $_POST['notes'] ?? ''
            ];
            $_SESSION['barber_bans'][] = $new_ban;

            // تحديث حالة الحلاق
            foreach ($_SESSION['barbers'] as &$barber) {
                if ($barber['id'] === $barber_id) {
                    $barber['status'] = 'محظور';
                    break;
                }
            }

            echo json_encode(['success' => true, 'message' => 'تم حظر الحلاق بنجاح']);
            exit;

        case 'unban_barber':
            $ban_id = (int)$_POST['id'];
            $barber_id = 0;

            // العثور على معرف الحلاق وإلغاء الحظر
            foreach ($_SESSION['barber_bans'] as &$ban) {
                if ($ban['id'] === $ban_id) {
                    $ban['status'] = 'ملغي';
                    $barber_id = $ban['barber_id'];
                    break;
                }
            }

            // تحديث حالة الحلاق
            foreach ($_SESSION['barbers'] as &$barber) {
                if ($barber['id'] === $barber_id) {
                    $barber['status'] = 'متاح';
                    break;
                }
            }

            echo json_encode(['success' => true, 'message' => 'تم إلغاء حظر الحلاق بنجاح']);
            exit;

        case 'get_cities':
            $country = $_POST['country'];
            $cities = $_SESSION['countries_cities'][$country] ?? [];
            echo json_encode(['success' => true, 'cities' => $cities]);
            exit;
    }
}

// Handle login
if (isset($_POST['login'])) {
    if ($_POST['email'] === $admin_username && $_POST['password'] === $admin_password) {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_email'] = $admin_username;
    } else {
        $error = 'بيانات تسجيل الدخول غير صحيحة';
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin.php');
    exit;
}

// Check if logged in
$is_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حلاق على بابك - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.3.67/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .admin-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1200px;
        }
        .login-container {
            max-width: 400px;
            margin: 100px auto;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 600px;
            border-radius: 20px 0 0 20px;
        }
        .content-area {
            padding: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e0e0e0;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .bg-pink {
            background-color: #e91e63 !important;
        }

        .barber-info {
            font-size: 0.9rem;
        }

        .barber-info .d-flex {
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 5px;
        }

        .barber-info .d-flex:last-child {
            border-bottom: none;
        }

        .complaint-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .input-group-text {
            background-color: #f8f9fa;
            border-color: #ced4da;
        }

        .badge {
            font-size: 0.75rem;
        }

        .modal-lg {
            max-width: 800px;
        }

        .progress {
            height: 8px;
        }
    </style>
</head>
<body>

<?php if (!$is_logged_in): ?>
    <!-- Login Form -->
    <div class="login-container">
        <div class="admin-container p-4">
            <div class="text-center mb-4">
                <h1 class="h3">🚀 حلاق على بابك</h1>
                <p class="text-muted">لوحة التحكم الإدارية</p>
            </div>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="mdi mdi-alert-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="email" name="email" required 
                           placeholder="<EMAIL>">
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <input type="password" class="form-control" id="password" name="password" required 
                           placeholder="admin123">
                </div>
                <button type="submit" name="login" class="btn btn-primary w-100">
                    <i class="mdi mdi-login"></i> تسجيل الدخول
                </button>
            </form>
            
            <div class="mt-4 p-3 bg-light rounded">
                <small class="text-muted">
                    <strong>بيانات تجريبية:</strong><br>
                    البريد: <EMAIL><br>
                    كلمة المرور: admin123
                </small>
            </div>
        </div>
    </div>

<?php else: ?>
    <!-- Admin Dashboard -->
    <div class="admin-container">
        <div class="row g-0">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar p-3">
                    <div class="text-center mb-4">
                        <h4>🚀 حلاق على بابك</h4>
                        <small>مرحباً، <?php echo $_SESSION['admin_email']; ?></small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#" onclick="showPage('dashboard'); return false;">
                            <i class="mdi mdi-view-dashboard"></i> لوحة التحكم
                        </a>
                        <a class="nav-link" href="#" onclick="showPage('bookings'); return false;">
                            <i class="mdi mdi-calendar-check"></i> الحجوزات
                        </a>
                        <a class="nav-link" href="#" onclick="showPage('barbers'); return false;">
                            <i class="mdi mdi-account-group"></i> الحلاقين
                        </a>
                        <a class="nav-link" href="#" onclick="showPage('customers'); return false;">
                            <i class="mdi mdi-account-multiple"></i> العملاء
                        </a>
                        <a class="nav-link" href="#" onclick="showPage('services'); return false;">
                            <i class="mdi mdi-scissors-cutting"></i> الخدمات
                        </a>
                        <a class="nav-link" href="#" onclick="showPage('payments'); return false;">
                            <i class="mdi mdi-credit-card"></i> المدفوعات
                        </a>
                        <a class="nav-link" href="#" onclick="showPage('complaints'); return false;">
                            <i class="mdi mdi-alert-circle"></i> الشكاوى
                        </a>
                        <a class="nav-link" href="#" onclick="showPage('messages'); return false;">
                            <i class="mdi mdi-message-text"></i> الرسائل
                        </a>
                        <a class="nav-link" href="#" onclick="showPage('bans'); return false;">
                            <i class="mdi mdi-account-cancel"></i> حظر الحلاقين
                        </a>
                        <a class="nav-link" href="#" onclick="showPage('reports'); return false;">
                            <i class="mdi mdi-chart-line"></i> التقارير
                        </a>
                        <a class="nav-link" href="#" onclick="showPage('settings'); return false;">
                            <i class="mdi mdi-cog"></i> الإعدادات
                        </a>
                        <hr class="my-3">
                        <a class="nav-link" href="?logout=1">
                            <i class="mdi mdi-logout"></i> تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Content Area -->
            <div class="col-md-9">
                <div class="content-area">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 id="page-title">لوحة التحكم الرئيسية</h2>
                        <span class="badge bg-success">متصل</span>
                    </div>

                    <!-- Dynamic Content Container -->
                    <div id="dynamic-content"></div>

                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// صفحات المحتوى
const pages = {
    dashboard: `
        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-calendar-check display-4"></i>
                    <h3>156</h3>
                    <p>إجمالي الحجوزات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-account-group display-4"></i>
                    <h3>23</h3>
                    <p>الحلاقين النشطين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-earth display-4"></i>
                    <h3>17</h3>
                    <p>الدول المدعومة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-cash-multiple display-4"></i>
                    <h3>12,450</h3>
                    <p>الإيرادات (ريال)</p>
                </div>
            </div>
        </div>

        <!-- إحصائيات الدول -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="mdi mdi-earth"></i> توزيع الحلاقين حسب الدول</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>🇪🇬 مصر</span>
                                <strong>2 حلاق</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: 67%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>🇸🇦 السعودية</span>
                                <strong>1 حلاق</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-primary" style="width: 33%"></div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <small class="text-muted">إجمالي: 3 حلاقين في دولتين</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="mdi mdi-map-marker-multiple"></i> أهم المحافظات</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>القاهرة</span>
                                <strong>1 حلاق</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: 33%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>الإسكندرية</span>
                                <strong>1 حلاق</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-warning" style="width: 33%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>الرياض</span>
                                <strong>1 حلاق</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-danger" style="width: 33%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="mdi mdi-clock-outline"></i> آخر الحجوزات</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex justify-content-between">
                                <span>أحمد محمد - قص شعر</span>
                                <small class="text-muted">منذ 5 دقائق</small>
                            </div>
                            <div class="list-group-item d-flex justify-content-between">
                                <span>سارة أحمد - تصفيف شعر</span>
                                <small class="text-muted">منذ 15 دقيقة</small>
                            </div>
                            <div class="list-group-item d-flex justify-content-between">
                                <span>محمد علي - حلاقة كاملة</span>
                                <small class="text-muted">منذ 30 دقيقة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="mdi mdi-chart-line"></i> إحصائيات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>معدل الحجوزات اليومية</span>
                                <strong>85%</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>رضا العملاء</span>
                                <strong>92%</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: 92%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>الحلاقين المتاحين</span>
                                <strong>78%</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: 78%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="mdi mdi-lightning-bolt"></i> إجراءات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button class="btn btn-primary w-100 mb-2" onclick="showNewBookingModal(event)">
                                    <i class="mdi mdi-plus"></i> حجز جديد
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-success w-100 mb-2" onclick="showAddBarberModal(event)">
                                    <i class="mdi mdi-account-plus"></i> إضافة حلاق
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-info w-100 mb-2" onclick="generateReport(event)">
                                    <i class="mdi mdi-file-chart"></i> تقرير يومي
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-warning w-100 mb-2" onclick="showNotifications(event)">
                                    <i class="mdi mdi-bell"></i> إشعارات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,

    bookings: `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="mdi mdi-calendar-check"></i> إدارة الحجوزات</h4>
            <button class="btn btn-primary" onclick="showNewBookingModal(event)">
                <i class="mdi mdi-plus"></i> حجز جديد
            </button>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                            <input type="text" class="form-control" id="bookingsSearch" placeholder="البحث في الحجوزات..." onkeyup="searchBookings()">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="bookingsStatusFilter" onchange="filterBookings()">
                            <option value="">كل الحالات</option>
                            <option value="مؤكد">مؤكد</option>
                            <option value="في الانتظار">في الانتظار</option>
                            <option value="مكتمل">مكتمل</option>
                            <option value="ملغي">ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="bookingsDateFilter" onchange="filterBookings()">
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="bookingsBarberFilter" onchange="filterBookings()">
                            <option value="">كل الحلاقين</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-secondary w-100" onclick="clearBookingsFilters()">
                            <i class="mdi mdi-filter-remove"></i> مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="mdi mdi-format-list-bulleted"></i> قائمة الحجوزات</h5>
                <span class="badge bg-primary" id="bookingsCount">0 حجز</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="bookingsTable">
                        <thead>
                            <tr>
                                <th>رقم الحجز</th>
                                <th>العميل</th>
                                <th>الحلاق</th>
                                <th>الخدمة</th>
                                <th>التاريخ</th>
                                <th>الوقت</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="bookingsTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>
                <div id="bookingsNoResults" class="text-center py-4" style="display: none;">
                    <i class="mdi mdi-magnify display-1 text-muted"></i>
                    <h5 class="text-muted">لا توجد نتائج للبحث</h5>
                    <p class="text-muted">جرب تغيير كلمات البحث أو الفلاتر</p>
                </div>
            </div>
        </div>
    `,

    barbers: `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="mdi mdi-account-group"></i> إدارة الحلاقين</h4>
            <button class="btn btn-primary" onclick="showAddBarberModal(event)">
                <i class="mdi mdi-plus"></i> إضافة حلاق
            </button>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                            <input type="text" class="form-control" id="barbersSearch" placeholder="البحث في الحلاقين..." onkeyup="searchBarbers()">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="barbersStatusFilter" onchange="filterBarbers()">
                            <option value="">كل الحالات</option>
                            <option value="متاح">متاح</option>
                            <option value="مشغول">مشغول</option>
                            <option value="محظور">محظور</option>
                            <option value="إجازة">إجازة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="barbersSpecialtyFilter" onchange="filterBarbers()">
                            <option value="">كل التخصصات</option>
                            <option value="حلاقة رجالي">حلاقة رجالي</option>
                            <option value="تصفيف نسائي">تصفيف نسائي</option>
                            <option value="كلاهما">كلاهما</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="barbersCountryFilter" onchange="filterBarbers()">
                            <option value="">كل الدول</option>
                            <option value="مصر">🇪🇬 مصر</option>
                            <option value="السعودية">🇸🇦 السعودية</option>
                            <option value="الإمارات">🇦🇪 الإمارات</option>
                            <option value="الكويت">🇰🇼 الكويت</option>
                            <option value="قطر">🇶🇦 قطر</option>
                            <option value="البحرين">🇧🇭 البحرين</option>
                            <option value="عمان">🇴🇲 عمان</option>
                            <option value="الأردن">🇯🇴 الأردن</option>
                            <option value="لبنان">🇱🇧 لبنان</option>
                            <option value="سوريا">🇸🇾 سوريا</option>
                            <option value="العراق">🇮🇶 العراق</option>
                            <option value="المغرب">🇲🇦 المغرب</option>
                            <option value="الجزائر">🇩🇿 الجزائر</option>
                            <option value="تونس">🇹🇳 تونس</option>
                            <option value="ليبيا">🇱🇾 ليبيا</option>
                            <option value="السودان">🇸🇩 السودان</option>
                            <option value="فلسطين">🇵🇸 فلسطين</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <select class="form-control" id="barbersExperienceFilter" onchange="filterBarbers()">
                            <option value="">الخبرة</option>
                            <option value="1-2">1-2 سنوات</option>
                            <option value="3-5">3-5 سنوات</option>
                            <option value="6+">أكثر من 6</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <button class="btn btn-secondary w-100" onclick="clearBarbersFilters()">
                            <i class="mdi mdi-filter-remove"></i> مسح
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5><i class="mdi mdi-account-group"></i> قائمة الحلاقين</h5>
            <span class="badge bg-primary" id="barbersCount">0 حلاق</span>
        </div>

        <div class="row" id="barbersContainer">
            <!-- سيتم تحميل البيانات ديناميكياً -->
        </div>

        <div id="barbersNoResults" class="text-center py-5" style="display: none;">
            <i class="mdi mdi-account-search display-1 text-muted"></i>
            <h5 class="text-muted">لا توجد نتائج للبحث</h5>
            <p class="text-muted">جرب تغيير كلمات البحث أو الفلاتر</p>
        </div>
    `,

    customers: `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="mdi mdi-account-multiple"></i> إدارة العملاء</h4>
            <button class="btn btn-primary" onclick="exportCustomers()">
                <i class="mdi mdi-download"></i> تصدير البيانات
            </button>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                            <input type="text" class="form-control" id="customersSearch" placeholder="البحث في العملاء..." onkeyup="searchCustomers()">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="customersBookingsFilter" onchange="filterCustomers()">
                            <option value="">كل العملاء</option>
                            <option value="active">عملاء نشطين (5+ حجوزات)</option>
                            <option value="new">عملاء جدد (أقل من 5)</option>
                            <option value="vip">عملاء مميزين (15+ حجوزات)</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="customersDateFilter" placeholder="تاريخ التسجيل من" onchange="filterCustomers()">
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="customersDateToFilter" placeholder="تاريخ التسجيل إلى" onchange="filterCustomers()">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-secondary w-100" onclick="clearCustomersFilters()">
                            <i class="mdi mdi-filter-remove"></i> مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="mdi mdi-format-list-bulleted"></i> قائمة العملاء</h5>
                <span class="badge bg-primary" id="customersCount">0 عميل</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="customersTable">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>عدد الحجوزات</th>
                                <th>تاريخ التسجيل</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="customersTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>
                <div id="customersNoResults" class="text-center py-4" style="display: none;">
                    <i class="mdi mdi-account-search display-1 text-muted"></i>
                    <h5 class="text-muted">لا توجد نتائج للبحث</h5>
                    <p class="text-muted">جرب تغيير كلمات البحث أو الفلاتر</p>
                </div>
            </div>
        </div>
    `,

    services: `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="mdi mdi-scissors-cutting"></i> إدارة الخدمات</h4>
            <button class="btn btn-primary" onclick="showAddServiceModal(event)">
                <i class="mdi mdi-plus"></i> إضافة خدمة
            </button>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                            <input type="text" class="form-control" id="servicesSearch" placeholder="البحث في الخدمات..." onkeyup="searchServices()">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="servicesPriceFilter" onchange="filterServices()">
                            <option value="">كل الأسعار</option>
                            <option value="0-50">أقل من 50 ريال</option>
                            <option value="50-100">50-100 ريال</option>
                            <option value="100+">أكثر من 100 ريال</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="servicesDurationFilter" onchange="filterServices()">
                            <option value="">كل المدد</option>
                            <option value="0-30">أقل من 30 دقيقة</option>
                            <option value="30-60">30-60 دقيقة</option>
                            <option value="60+">أكثر من ساعة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="servicesTypeFilter" onchange="filterServices()">
                            <option value="">كل الأنواع</option>
                            <option value="رجالي">خدمات رجالية</option>
                            <option value="نسائي">خدمات نسائية</option>
                            <option value="مشترك">خدمات مشتركة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-secondary w-100" onclick="clearServicesFilters()">
                            <i class="mdi mdi-filter-remove"></i> مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5><i class="mdi mdi-scissors-cutting"></i> قائمة الخدمات</h5>
            <span class="badge bg-primary" id="servicesCount">0 خدمة</span>
        </div>

        <div class="row" id="servicesContainer">
            <!-- سيتم تحميل البيانات ديناميكياً -->
        </div>

        <div id="servicesNoResults" class="text-center py-5" style="display: none;">
            <i class="mdi mdi-scissors-cutting display-1 text-muted"></i>
            <h5 class="text-muted">لا توجد نتائج للبحث</h5>
            <p class="text-muted">جرب تغيير كلمات البحث أو الفلاتر</p>
        </div>
    `,

    payments: `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="mdi mdi-credit-card"></i> إدارة المدفوعات</h4>
            <button class="btn btn-primary" onclick="showNewPaymentModal(event)">
                <i class="mdi mdi-plus"></i> تسجيل دفع جديد
            </button>
        </div>

        <!-- إحصائيات المدفوعات -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-cash-multiple display-4"></i>
                    <h3 id="totalRevenue">0</h3>
                    <p>إجمالي الإيرادات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-credit-card-check display-4"></i>
                    <h3 id="paidPayments">0</h3>
                    <p>المدفوعات المكتملة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-credit-card-clock display-4"></i>
                    <h3 id="pendingPayments">0</h3>
                    <p>المدفوعات المعلقة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-calendar-today display-4"></i>
                    <h3 id="todayRevenue">0</h3>
                    <p>إيرادات اليوم</p>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة للمدفوعات -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                            <input type="text" class="form-control" id="paymentsSearch" placeholder="البحث في المدفوعات..." onkeyup="searchPayments()">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="paymentsMethodFilter" onchange="filterPayments()">
                            <option value="">كل طرق الدفع</option>
                            <option value="نقدي">نقدي</option>
                            <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                            <option value="تحويل بنكي">تحويل بنكي</option>
                            <option value="محفظة إلكترونية">محفظة إلكترونية</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="paymentsStatusFilter" onchange="filterPayments()">
                            <option value="">كل الحالات</option>
                            <option value="مدفوع">مدفوع</option>
                            <option value="معلق">معلق</option>
                            <option value="مرفوض">مرفوض</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="paymentsDateFilter" onchange="filterPayments()">
                    </div>
                    <div class="col-md-1">
                        <input type="number" class="form-control" id="paymentsAmountFilter" placeholder="المبلغ" onchange="filterPayments()">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-secondary w-100" onclick="clearPaymentsFilters()">
                            <i class="mdi mdi-filter-remove"></i> مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول المدفوعات -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="mdi mdi-format-list-bulleted"></i> سجل المدفوعات</h5>
                <span class="badge bg-primary" id="paymentsCount">0 دفعة</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="paymentsTable">
                        <thead>
                            <tr>
                                <th>رقم الدفع</th>
                                <th>رقم الحجز</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="paymentsTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>
                <div id="paymentsNoResults" class="text-center py-4" style="display: none;">
                    <i class="mdi mdi-credit-card-search display-1 text-muted"></i>
                    <h5 class="text-muted">لا توجد نتائج للبحث</h5>
                    <p class="text-muted">جرب تغيير كلمات البحث أو الفلاتر</p>
                </div>
            </div>
        </div>

        <!-- طرق الدفع المتاحة -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="mdi mdi-credit-card-multiple"></i> طرق الدفع المتاحة</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="mdi mdi-cash text-success"></i> نقدي</span>
                                <span class="badge bg-success">متاح</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="mdi mdi-credit-card text-primary"></i> بطاقة ائتمان</span>
                                <span class="badge bg-success">متاح</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="mdi mdi-bank text-info"></i> تحويل بنكي</span>
                                <span class="badge bg-success">متاح</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="mdi mdi-wallet text-warning"></i> محفظة إلكترونية</span>
                                <span class="badge bg-success">متاح</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="mdi mdi-chart-pie"></i> توزيع طرق الدفع</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>نقدي</span>
                                <strong>60%</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: 60%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>بطاقة ائتمان</span>
                                <strong>25%</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-primary" style="width: 25%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>تحويل بنكي</span>
                                <strong>10%</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: 10%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>محفظة إلكترونية</span>
                                <strong>5%</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-warning" style="width: 5%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,

    complaints: `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="mdi mdi-alert-circle"></i> إدارة الشكاوى</h4>
            <div>
                <span class="badge bg-danger me-2" id="newComplaintsCount">0</span>
                <button class="btn btn-warning" onclick="markAllComplaintsRead()">
                    <i class="mdi mdi-check-all"></i> تحديد الكل كمقروء
                </button>
            </div>
        </div>

        <!-- إحصائيات الشكاوى -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-alert-circle display-4"></i>
                    <h3 id="totalComplaints">0</h3>
                    <p>إجمالي الشكاوى</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-clock-alert display-4"></i>
                    <h3 id="newComplaints">0</h3>
                    <p>شكاوى جديدة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-progress-clock display-4"></i>
                    <h3 id="pendingComplaints">0</h3>
                    <p>قيد المراجعة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="mdi mdi-check-circle display-4"></i>
                    <h3 id="closedComplaints">0</h3>
                    <p>مغلقة</p>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة للشكاوى -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                            <input type="text" class="form-control" id="complaintsSearch" placeholder="البحث في الشكاوى..." onkeyup="searchComplaints()">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="complaintsStatusFilter" onchange="filterComplaints()">
                            <option value="">كل الحالات</option>
                            <option value="جديد">جديد</option>
                            <option value="قيد المراجعة">قيد المراجعة</option>
                            <option value="مغلق">مغلق</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="complaintsPriorityFilter" onchange="filterComplaints()">
                            <option value="">كل الأولويات</option>
                            <option value="عالي">عالي</option>
                            <option value="متوسط">متوسط</option>
                            <option value="منخفض">منخفض</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="complaintsDateFilter" onchange="filterComplaints()">
                    </div>
                    <div class="col-md-1">
                        <select class="form-control" id="complaintsBarberFilter" onchange="filterComplaints()">
                            <option value="">كل الحلاقين</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-secondary w-100" onclick="clearComplaintsFilters()">
                            <i class="mdi mdi-filter-remove"></i> مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الشكاوى -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="mdi mdi-format-list-bulleted"></i> قائمة الشكاوى</h5>
                <span class="badge bg-primary" id="complaintsCount">0 شكوى</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="complaintsTable">
                        <thead>
                            <tr>
                                <th>رقم الشكوى</th>
                                <th>العميل</th>
                                <th>الحلاق</th>
                                <th>الشكوى</th>
                                <th>الأولوية</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="complaintsTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>
                <div id="complaintsNoResults" class="text-center py-4" style="display: none;">
                    <i class="mdi mdi-alert-circle-outline display-1 text-muted"></i>
                    <h5 class="text-muted">لا توجد نتائج للبحث</h5>
                    <p class="text-muted">جرب تغيير كلمات البحث أو الفلاتر</p>
                </div>
            </div>
        </div>
    `,

    messages: `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="mdi mdi-message-text"></i> إدارة الرسائل</h4>
            <div>
                <span class="badge bg-primary me-2" id="newMessagesCount">0</span>
                <button class="btn btn-info" onclick="markAllMessagesRead()">
                    <i class="mdi mdi-check-all"></i> تحديد الكل كمقروء
                </button>
            </div>
        </div>

        <!-- إحصائيات الرسائل -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="mdi mdi-message-text display-4"></i>
                    <h3 id="totalMessages">0</h3>
                    <p>إجمالي الرسائل</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="mdi mdi-message-alert display-4"></i>
                    <h3 id="newMessages">0</h3>
                    <p>رسائل جديدة</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="mdi mdi-message-reply display-4"></i>
                    <h3 id="repliedMessages">0</h3>
                    <p>تم الرد عليها</p>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة للرسائل -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                            <input type="text" class="form-control" id="messagesSearch" placeholder="البحث في الرسائل..." onkeyup="searchMessages()">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="messagesStatusFilter" onchange="filterMessages()">
                            <option value="">كل الحالات</option>
                            <option value="جديد">جديد</option>
                            <option value="مقروء">مقروء</option>
                            <option value="مغلق">مغلق</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="messagesTypeFilter" onchange="filterMessages()">
                            <option value="">كل الأنواع</option>
                            <option value="عميل">عميل</option>
                            <option value="حلاق">حلاق</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="messagesDateFilter" onchange="filterMessages()">
                    </div>
                    <div class="col-md-1">
                        <select class="form-control" id="messagesSubjectFilter" onchange="filterMessages()">
                            <option value="">كل المواضيع</option>
                            <option value="استفسار">استفسار</option>
                            <option value="شكوى">شكوى</option>
                            <option value="اقتراح">اقتراح</option>
                            <option value="طلب">طلب</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-secondary w-100" onclick="clearMessagesFilters()">
                            <i class="mdi mdi-filter-remove"></i> مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الرسائل -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="mdi mdi-format-list-bulleted"></i> قائمة الرسائل</h5>
                <span class="badge bg-primary" id="messagesCount">0 رسالة</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="messagesTable">
                        <thead>
                            <tr>
                                <th>رقم الرسالة</th>
                                <th>المرسل</th>
                                <th>النوع</th>
                                <th>الموضوع</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="messagesTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>
                <div id="messagesNoResults" class="text-center py-4" style="display: none;">
                    <i class="mdi mdi-message-text-outline display-1 text-muted"></i>
                    <h5 class="text-muted">لا توجد نتائج للبحث</h5>
                    <p class="text-muted">جرب تغيير كلمات البحث أو الفلاتر</p>
                </div>
            </div>
        </div>
    `,

    bans: `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="mdi mdi-account-cancel"></i> إدارة حظر الحلاقين</h4>
            <button class="btn btn-danger" onclick="showBanBarberModal(event)">
                <i class="mdi mdi-account-cancel"></i> حظر حلاق
            </button>
        </div>

        <!-- إحصائيات الحظر -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="mdi mdi-account-cancel display-4"></i>
                    <h3 id="totalBans">0</h3>
                    <p>إجمالي الحظر</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="mdi mdi-clock-alert display-4"></i>
                    <h3 id="activeBans">0</h3>
                    <p>حظر نشط</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="mdi mdi-calendar-clock display-4"></i>
                    <h3 id="temporaryBans">0</h3>
                    <p>حظر مؤقت</p>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة للحظر -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                            <input type="text" class="form-control" id="bansSearch" placeholder="البحث في الحظر..." onkeyup="searchBans()">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="bansStatusFilter" onchange="filterBans()">
                            <option value="">كل الحالات</option>
                            <option value="نشط">نشط</option>
                            <option value="ملغي">ملغي</option>
                            <option value="منتهي">منتهي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="bansTypeFilter" onchange="filterBans()">
                            <option value="">كل الأنواع</option>
                            <option value="مؤقت">مؤقت</option>
                            <option value="دائم">دائم</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="bansDateFilter" onchange="filterBans()">
                    </div>
                    <div class="col-md-1">
                        <select class="form-control" id="bansReasonFilter" onchange="filterBans()">
                            <option value="">كل الأسباب</option>
                            <option value="شكاوى">شكاوى</option>
                            <option value="تأخير">تأخير</option>
                            <option value="مخالفة">مخالفة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-secondary w-100" onclick="clearBansFilters()">
                            <i class="mdi mdi-filter-remove"></i> مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الحظر -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="mdi mdi-format-list-bulleted"></i> قائمة الحلاقين المحظورين</h5>
                <span class="badge bg-primary" id="bansCount">0 حظر</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="bansTable">
                        <thead>
                            <tr>
                                <th>رقم الحظر</th>
                                <th>الحلاق</th>
                                <th>السبب</th>
                                <th>نوع الحظر</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="bansTableBody">
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>
                <div id="bansNoResults" class="text-center py-4" style="display: none;">
                    <i class="mdi mdi-account-cancel-outline display-1 text-muted"></i>
                    <h5 class="text-muted">لا توجد نتائج للبحث</h5>
                    <p class="text-muted">جرب تغيير كلمات البحث أو الفلاتر</p>
                </div>
            </div>
        </div>
    `,

    reports: `
        <div class="mb-4">
            <h4><i class="mdi mdi-chart-line"></i> التقارير والإحصائيات</h4>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>إيرادات الشهر</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="revenueChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>أكثر الخدمات طلباً</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between">
                                <span>قص شعر رجالي</span>
                                <span class="badge bg-primary">45%</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between">
                                <span>تصفيف شعر نسائي</span>
                                <span class="badge bg-success">30%</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between">
                                <span>حلاقة كاملة</span>
                                <span class="badge bg-info">25%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,

    settings: `
        <div class="mb-4">
            <h4><i class="mdi mdi-cog"></i> إعدادات النظام</h4>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>إعدادات عامة</h5>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="mb-3">
                                <label class="form-label">اسم التطبيق</label>
                                <input type="text" class="form-control" value="حلاق على بابك">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" value="<EMAIL>">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" value="0501234567">
                            </div>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>إعدادات الحجز</h5>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="mb-3">
                                <label class="form-label">مدة الحجز الافتراضية (دقيقة)</label>
                                <input type="number" class="form-control" value="30">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">ساعات العمل من</label>
                                <input type="time" class="form-control" value="09:00">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">ساعات العمل إلى</label>
                                <input type="time" class="form-control" value="22:00">
                            </div>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `
};

// عرض الصفحة
function showPage(pageName, event) {
    // منع السلوك الافتراضي للرابط
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    // تحديث العنوان
    const titles = {
        dashboard: 'لوحة التحكم الرئيسية',
        bookings: 'إدارة الحجوزات',
        barbers: 'إدارة الحلاقين',
        customers: 'إدارة العملاء',
        services: 'إدارة الخدمات',
        payments: 'إدارة المدفوعات',
        complaints: 'إدارة الشكاوى',
        messages: 'إدارة الرسائل',
        bans: 'إدارة حظر الحلاقين',
        reports: 'التقارير والإحصائيات',
        settings: 'إعدادات النظام'
    };

    // تحديث العنوان والمحتوى
    document.getElementById('page-title').textContent = titles[pageName];
    document.getElementById('dynamic-content').innerHTML = pages[pageName];

    // تحديث الروابط النشطة
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // البحث عن الرابط الصحيح وتفعيله
    const activeLink = document.querySelector(`[onclick*="showPage('${pageName}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }

    // منع الانتقال الافتراضي
    return false;
}

// وظائف الأزرار
function showNewBookingModal(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    // إنشاء نافذة منبثقة جميلة
    const modalHtml = `
        <div class="modal fade" id="newBookingModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">🎉 حجز جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="mb-3">
                                <label class="form-label">اسم العميل</label>
                                <input type="text" class="form-control" name="customer" placeholder="أدخل اسم العميل" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الحلاق</label>
                                <select class="form-control" name="barber" required>
                                    <option value="">اختر الحلاق</option>
                                    <option value="محمد الحلاق">محمد الحلاق</option>
                                    <option value="فاطمة الحلاقة">فاطمة الحلاقة</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الخدمة</label>
                                <select class="form-control" name="service" required onchange="updatePrice(this)">
                                    <option value="">اختر الخدمة</option>
                                    <option value="قص شعر رجالي" data-price="50">قص شعر رجالي - 50 ريال</option>
                                    <option value="تصفيف شعر نسائي" data-price="80">تصفيف شعر نسائي - 80 ريال</option>
                                    <option value="حلاقة كاملة" data-price="70">حلاقة كاملة - 70 ريال</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">السعر</label>
                                <input type="number" class="form-control" name="price" id="bookingPrice" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">التاريخ</label>
                                <input type="date" class="form-control" name="date" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الوقت</label>
                                <input type="time" class="form-control" name="time" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveBooking()">حفظ الحجز</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة النافذة للصفحة وعرضها
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('newBookingModal'));
    modal.show();

    // حذف النافذة عند الإغلاق
    document.getElementById('newBookingModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });

    return false;
}

function showAddBarberModal(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    const modalHtml = `
        <div class="modal fade" id="addBarberModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">👨‍💼 إضافة حلاق جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control" name="name" placeholder="أدخل اسم الحلاق" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone" placeholder="05xxxxxxxx" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">سنوات الخبرة</label>
                                        <input type="number" class="form-control" name="experience" placeholder="عدد سنوات الخبرة" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">التخصص</label>
                                        <select class="form-control" name="specialty" required>
                                            <option value="">اختر التخصص</option>
                                            <option value="حلاقة رجالي">حلاقة رجالي</option>
                                            <option value="تصفيف نسائي">تصفيف نسائي</option>
                                            <option value="كلاهما">كلاهما</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الدولة</label>
                                <select class="form-control" name="country" required onchange="loadCities(this.value, 'addBarberCitySelect')">
                                    <option value="">اختر الدولة</option>
                                    <option value="مصر">🇪🇬 مصر</option>
                                    <option value="السعودية">🇸🇦 السعودية</option>
                                    <option value="الإمارات">🇦🇪 الإمارات العربية المتحدة</option>
                                    <option value="الكويت">🇰🇼 الكويت</option>
                                    <option value="قطر">🇶🇦 قطر</option>
                                    <option value="البحرين">🇧🇭 البحرين</option>
                                    <option value="عمان">🇴🇲 عمان</option>
                                    <option value="الأردن">🇯🇴 الأردن</option>
                                    <option value="لبنان">🇱🇧 لبنان</option>
                                    <option value="سوريا">🇸🇾 سوريا</option>
                                    <option value="العراق">🇮🇶 العراق</option>
                                    <option value="المغرب">🇲🇦 المغرب</option>
                                    <option value="الجزائر">🇩🇿 الجزائر</option>
                                    <option value="تونس">🇹🇳 تونس</option>
                                    <option value="ليبيا">🇱🇾 ليبيا</option>
                                    <option value="السودان">🇸🇩 السودان</option>
                                    <option value="فلسطين">🇵🇸 فلسطين</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المحافظة/المدينة</label>
                                <select class="form-control" name="city" id="addBarberCitySelect" required disabled>
                                    <option value="">اختر الدولة أولاً</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">العنوان التفصيلي</label>
                                <textarea class="form-control" name="address" rows="2" placeholder="أدخل العنوان التفصيلي (الحي، الشارع)" required></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="saveBarber()">إضافة الحلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('addBarberModal'));
    modal.show();

    document.getElementById('addBarberModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });

    return false;
}

function generateReport(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    // عرض رسالة تحميل
    const loadingHtml = `
        <div class="modal fade" id="reportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">📊 إنشاء التقرير اليومي</h5>
                    </div>
                    <div class="modal-body text-center">
                        <div class="spinner-border text-primary mb-3" role="status"></div>
                        <p>جاري إنشاء التقرير اليومي...</p>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 75%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', loadingHtml);
    const modal = new bootstrap.Modal(document.getElementById('reportModal'));
    modal.show();

    // محاكاة إنشاء التقرير
    setTimeout(() => {
        modal.hide();
        document.getElementById('reportModal').remove();

        // عرض رسالة نجاح
        const successHtml = `
            <div class="alert alert-success alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                <strong>✅ تم بنجاح!</strong> تم إنشاء التقرير اليومي وحفظه.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', successHtml);

        // إزالة الرسالة بعد 3 ثوان
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) alert.remove();
        }, 3000);
    }, 2000);

    return false;
}

function showNotifications(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    const modalHtml = `
        <div class="modal fade" id="notificationsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">🔔 الإشعارات</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="list-group">
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">حجز جديد</h6>
                                    <small>منذ 3 دقائق</small>
                                </div>
                                <p class="mb-1">أحمد محمد حجز موعد قص شعر اليوم الساعة 2:00 م</p>
                                <small class="text-muted">حجز رقم #156</small>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">تأكيد حجز</h6>
                                    <small>منذ 15 دقيقة</small>
                                </div>
                                <p class="mb-1">سارة أحمد أكدت حجزها لتصفيف الشعر</p>
                                <small class="text-muted">حجز رقم #155</small>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">حلاق جديد</h6>
                                    <small>منذ ساعة</small>
                                </div>
                                <p class="mb-1">انضم محمد الحلاق إلى فريق العمل</p>
                                <small class="text-muted">مرحباً بالعضو الجديد!</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">تم</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('notificationsModal'));
    modal.show();

    document.getElementById('notificationsModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });

    return false;
}

// وظائف إضافية للنوافذ المنبثقة
function saveBooking() {
    const form = document.querySelector('#newBookingModal form');
    const formData = new FormData(form);
    formData.append('action', 'save_booking');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('newBookingModal')).hide();
            if (document.getElementById('bookingsTableBody')) {
                loadBookings();
            }
        } else {
            showErrorMessage('حدث خطأ في حفظ البيانات');
        }
    })
    .catch(error => {
        showErrorMessage('حدث خطأ في الاتصال');
    });
}

function saveBarber() {
    const form = document.querySelector('#addBarberModal form');
    const formData = new FormData(form);
    formData.append('action', 'save_barber');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('addBarberModal')).hide();
            if (document.getElementById('barbersContainer')) {
                loadBarbers();
            }
        } else {
            showErrorMessage('حدث خطأ في حفظ البيانات');
        }
    })
    .catch(error => {
        showErrorMessage('حدث خطأ في الاتصال');
    });
}

function deleteBooking(id) {
    if (confirm('هل أنت متأكد من حذف هذا الحجز؟')) {
        const formData = new FormData();
        formData.append('action', 'delete_booking');
        formData.append('id', id);

        fetch('admin.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessMessage(data.message);
                loadBookings();
            } else {
                showErrorMessage('حدث خطأ في حذف البيانات');
            }
        });
    }
}

function deleteBarber(id) {
    if (confirm('هل أنت متأكد من حذف هذا الحلاق؟')) {
        const formData = new FormData();
        formData.append('action', 'delete_barber');
        formData.append('id', id);

        fetch('admin.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessMessage(data.message);
                loadBarbers();
            } else {
                showErrorMessage('حدث خطأ في حذف البيانات');
            }
        });
    }
}

function showSuccessMessage(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
            <strong>✅ نجح!</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', alertHtml);

    setTimeout(() => {
        const alert = document.querySelector('.alert-success');
        if (alert) alert.remove();
    }, 3000);
}

function showErrorMessage(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
            <strong>❌ خطأ!</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', alertHtml);

    setTimeout(() => {
        const alert = document.querySelector('.alert-danger');
        if (alert) alert.remove();
    }, 3000);
}

// متغيرات البحث والفلترة
let allBookings = [];
let allBarbers = [];
let allCustomers = [];
let allServices = [];
let allPayments = [];
let allComplaints = [];
let allMessages = [];
let allBans = [];

// وظائف تحميل البيانات
function loadBookings() {
    allBookings = <?php echo json_encode($_SESSION['bookings'] ?? []); ?>;
    displayBookings(allBookings);

    // تحديث فلتر الحلاقين
    const barberFilter = document.getElementById('bookingsBarberFilter');
    if (barberFilter) {
        barberFilter.innerHTML = '<option value="">كل الحلاقين</option>';
        const barbers = [...new Set(allBookings.map(b => b.barber))];
        barbers.forEach(barber => {
            barberFilter.innerHTML += `<option value="${barber}">${barber}</option>`;
        });
    }
}

function displayBookings(bookings) {
    const tbody = document.getElementById('bookingsTableBody');
    const noResults = document.getElementById('bookingsNoResults');
    const countBadge = document.getElementById('bookingsCount');

    if (!tbody) return;

    tbody.innerHTML = '';

    if (bookings.length === 0) {
        if (noResults) noResults.style.display = 'block';
        if (countBadge) countBadge.textContent = '0 حجز';
        return;
    }

    if (noResults) noResults.style.display = 'none';
    if (countBadge) countBadge.textContent = `${bookings.length} حجز`;

    bookings.forEach(booking => {
        const statusClass = booking.status === 'مؤكد' ? 'bg-success' : 'bg-warning';
        const row = `
            <tr>
                <td>#${booking.id.toString().padStart(3, '0')}</td>
                <td>${booking.customer}</td>
                <td>${booking.barber}</td>
                <td>${booking.service}</td>
                <td>${booking.date}</td>
                <td>${booking.time}</td>
                <td>${booking.price} ريال</td>
                <td><span class="badge ${statusClass}">${booking.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="editBooking(${booking.id})">تعديل</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteBooking(${booking.id})">حذف</button>
                    <button class="btn btn-sm btn-success" onclick="showPaymentModal(${booking.id}, ${booking.price})">دفع</button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function searchBookings() {
    const searchTerm = document.getElementById('bookingsSearch').value.toLowerCase();
    const filtered = allBookings.filter(booking =>
        booking.customer.toLowerCase().includes(searchTerm) ||
        booking.barber.toLowerCase().includes(searchTerm) ||
        booking.service.toLowerCase().includes(searchTerm) ||
        booking.id.toString().includes(searchTerm)
    );
    displayBookings(filtered);
}

function filterBookings() {
    const statusFilter = document.getElementById('bookingsStatusFilter').value;
    const dateFilter = document.getElementById('bookingsDateFilter').value;
    const barberFilter = document.getElementById('bookingsBarberFilter').value;
    const searchTerm = document.getElementById('bookingsSearch').value.toLowerCase();

    let filtered = allBookings.filter(booking => {
        const matchesSearch = !searchTerm ||
            booking.customer.toLowerCase().includes(searchTerm) ||
            booking.barber.toLowerCase().includes(searchTerm) ||
            booking.service.toLowerCase().includes(searchTerm) ||
            booking.id.toString().includes(searchTerm);

        const matchesStatus = !statusFilter || booking.status === statusFilter;
        const matchesDate = !dateFilter || booking.date === dateFilter;
        const matchesBarber = !barberFilter || booking.barber === barberFilter;

        return matchesSearch && matchesStatus && matchesDate && matchesBarber;
    });

    displayBookings(filtered);
}

function clearBookingsFilters() {
    document.getElementById('bookingsSearch').value = '';
    document.getElementById('bookingsStatusFilter').value = '';
    document.getElementById('bookingsDateFilter').value = '';
    document.getElementById('bookingsBarberFilter').value = '';
    displayBookings(allBookings);
}

function loadBarbers() {
    allBarbers = <?php echo json_encode($_SESSION['barbers'] ?? []); ?>;
    displayBarbers(allBarbers);
}

function displayBarbers(barbers) {
    const container = document.getElementById('barbersContainer');
    const noResults = document.getElementById('barbersNoResults');
    const countBadge = document.getElementById('barbersCount');

    if (!container) return;

    container.innerHTML = '';

    if (barbers.length === 0) {
        if (noResults) noResults.style.display = 'block';
        if (countBadge) countBadge.textContent = '0 حلاق';
        return;
    }

    if (noResults) noResults.style.display = 'none';
    if (countBadge) countBadge.textContent = `${barbers.length} حلاق`;

    barbers.forEach(barber => {
        const statusClass = barber.status === 'متاح' ? 'bg-success' :
                           barber.status === 'محظور' ? 'bg-danger' : 'bg-warning';
        const card = `
            <div class="col-md-4 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <img src="https://via.placeholder.com/80" class="rounded-circle mb-2" alt="حلاق">
                            <h5 class="mb-1">${barber.name}</h5>
                            <div class="mb-2">
                                <span class="badge ${statusClass}">${barber.status}</span>
                            </div>
                        </div>

                        <div class="barber-info">
                            <div class="d-flex justify-content-between mb-2">
                                <span><i class="mdi mdi-star text-warning"></i> التقييم:</span>
                                <strong>${barber.rating || 0}/5</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span><i class="mdi mdi-briefcase"></i> الخبرة:</span>
                                <strong>${barber.experience} سنوات</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span><i class="mdi mdi-scissors-cutting"></i> التخصص:</span>
                                <strong>${barber.specialty}</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span><i class="mdi mdi-earth"></i> الدولة:</span>
                                <strong>${barber.country || 'غير محدد'}</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span><i class="mdi mdi-map-marker"></i> المحافظة:</span>
                                <strong>${barber.city || 'غير محدد'}</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span><i class="mdi mdi-phone"></i> الهاتف:</span>
                                <strong>${barber.phone}</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span><i class="mdi mdi-check-circle"></i> الخدمات:</span>
                                <strong>${barber.completed_services || 0}</strong>
                            </div>
                        </div>

                        <div class="d-flex justify-content-center gap-1">
                            <button class="btn btn-sm btn-info" onclick="editBarber(${barber.id})" title="تعديل">
                                <i class="mdi mdi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-success" onclick="viewBarberDetails(${barber.id})" title="عرض">
                                <i class="mdi mdi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteBarber(${barber.id})" title="حذف">
                                <i class="mdi mdi-delete"></i>
                            </button>
                            ${barber.status !== 'محظور' ?
                                `<button class="btn btn-sm btn-warning" onclick="quickBanBarber(${barber.id}, '${barber.name}')" title="حظر">
                                    <i class="mdi mdi-account-cancel"></i>
                                </button>` :
                                `<button class="btn btn-sm btn-secondary" onclick="viewBanDetails(${barber.id})" title="محظور">
                                    <i class="mdi mdi-information"></i>
                                </button>`
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML += card;
    });
}

function searchBarbers() {
    const searchTerm = document.getElementById('barbersSearch').value.toLowerCase();
    const filtered = allBarbers.filter(barber =>
        barber.name.toLowerCase().includes(searchTerm) ||
        barber.specialty.toLowerCase().includes(searchTerm) ||
        barber.phone.includes(searchTerm)
    );
    displayBarbers(filtered);
}

function filterBarbers() {
    const statusFilter = document.getElementById('barbersStatusFilter').value;
    const specialtyFilter = document.getElementById('barbersSpecialtyFilter').value;
    const countryFilter = document.getElementById('barbersCountryFilter').value;
    const experienceFilter = document.getElementById('barbersExperienceFilter').value;
    const searchTerm = document.getElementById('barbersSearch').value.toLowerCase();

    let filtered = allBarbers.filter(barber => {
        const matchesSearch = !searchTerm ||
            barber.name.toLowerCase().includes(searchTerm) ||
            barber.specialty.toLowerCase().includes(searchTerm) ||
            barber.phone.includes(searchTerm) ||
            (barber.city && barber.city.toLowerCase().includes(searchTerm)) ||
            (barber.country && barber.country.toLowerCase().includes(searchTerm));

        const matchesStatus = !statusFilter || barber.status === statusFilter;
        const matchesSpecialty = !specialtyFilter || barber.specialty === specialtyFilter;
        const matchesCountry = !countryFilter || barber.country === countryFilter;

        let matchesExperience = true;
        if (experienceFilter) {
            if (experienceFilter === '1-2') {
                matchesExperience = barber.experience >= 1 && barber.experience <= 2;
            } else if (experienceFilter === '3-5') {
                matchesExperience = barber.experience >= 3 && barber.experience <= 5;
            } else if (experienceFilter === '6+') {
                matchesExperience = barber.experience >= 6;
            }
        }

        return matchesSearch && matchesStatus && matchesSpecialty && matchesCountry && matchesExperience;
    });

    displayBarbers(filtered);
}

function clearBarbersFilters() {
    document.getElementById('barbersSearch').value = '';
    document.getElementById('barbersStatusFilter').value = '';
    document.getElementById('barbersSpecialtyFilter').value = '';
    document.getElementById('barbersCountryFilter').value = '';
    document.getElementById('barbersExperienceFilter').value = '';
    displayBarbers(allBarbers);
}

function loadCustomers() {
    allCustomers = <?php echo json_encode($_SESSION['customers'] ?? []); ?>;
    displayCustomers(allCustomers);
}

function displayCustomers(customers) {
    const tbody = document.getElementById('customersTableBody');
    const noResults = document.getElementById('customersNoResults');
    const countBadge = document.getElementById('customersCount');

    if (!tbody) return;

    tbody.innerHTML = '';

    if (customers.length === 0) {
        if (noResults) noResults.style.display = 'block';
        if (countBadge) countBadge.textContent = '0 عميل';
        return;
    }

    if (noResults) noResults.style.display = 'none';
    if (countBadge) countBadge.textContent = `${customers.length} عميل`;

    customers.forEach(customer => {
        let statusClass = 'bg-primary';
        let statusText = 'عادي';

        if (customer.bookings_count >= 15) {
            statusClass = 'bg-warning';
            statusText = 'مميز';
        } else if (customer.bookings_count >= 5) {
            statusClass = 'bg-success';
            statusText = 'نشط';
        } else {
            statusClass = 'bg-info';
            statusText = 'جديد';
        }

        const row = `
            <tr>
                <td>${customer.name}</td>
                <td>${customer.email}</td>
                <td>${customer.phone}</td>
                <td>${customer.bookings_count}</td>
                <td>${customer.join_date}</td>
                <td><span class="badge ${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="viewCustomer(${customer.id})">عرض</button>
                    <button class="btn btn-sm btn-warning" onclick="editCustomer(${customer.id})">تعديل</button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function searchCustomers() {
    const searchTerm = document.getElementById('customersSearch').value.toLowerCase();
    const filtered = allCustomers.filter(customer =>
        customer.name.toLowerCase().includes(searchTerm) ||
        customer.email.toLowerCase().includes(searchTerm) ||
        customer.phone.includes(searchTerm)
    );
    displayCustomers(filtered);
}

function filterCustomers() {
    const bookingsFilter = document.getElementById('customersBookingsFilter').value;
    const dateFromFilter = document.getElementById('customersDateFilter').value;
    const dateToFilter = document.getElementById('customersDateToFilter').value;
    const searchTerm = document.getElementById('customersSearch').value.toLowerCase();

    let filtered = allCustomers.filter(customer => {
        const matchesSearch = !searchTerm ||
            customer.name.toLowerCase().includes(searchTerm) ||
            customer.email.toLowerCase().includes(searchTerm) ||
            customer.phone.includes(searchTerm);

        let matchesBookings = true;
        if (bookingsFilter === 'active') {
            matchesBookings = customer.bookings_count >= 5 && customer.bookings_count < 15;
        } else if (bookingsFilter === 'new') {
            matchesBookings = customer.bookings_count < 5;
        } else if (bookingsFilter === 'vip') {
            matchesBookings = customer.bookings_count >= 15;
        }

        const matchesDateFrom = !dateFromFilter || customer.join_date >= dateFromFilter;
        const matchesDateTo = !dateToFilter || customer.join_date <= dateToFilter;

        return matchesSearch && matchesBookings && matchesDateFrom && matchesDateTo;
    });

    displayCustomers(filtered);
}

function clearCustomersFilters() {
    document.getElementById('customersSearch').value = '';
    document.getElementById('customersBookingsFilter').value = '';
    document.getElementById('customersDateFilter').value = '';
    document.getElementById('customersDateToFilter').value = '';
    displayCustomers(allCustomers);
}

function loadServices() {
    allServices = <?php echo json_encode($_SESSION['services'] ?? []); ?>;
    displayServices(allServices);
}

function displayServices(services) {
    const container = document.getElementById('servicesContainer');
    const noResults = document.getElementById('servicesNoResults');
    const countBadge = document.getElementById('servicesCount');

    if (!container) return;

    container.innerHTML = '';

    if (services.length === 0) {
        if (noResults) noResults.style.display = 'block';
        if (countBadge) countBadge.textContent = '0 خدمة';
        return;
    }

    if (noResults) noResults.style.display = 'none';
    if (countBadge) countBadge.textContent = `${services.length} خدمة`;

    services.forEach(service => {
        const typeIcon = service.category === 'رجالي' ? 'mdi-scissors-cutting' :
                        service.category === 'نسائي' ? 'mdi-face-woman' : 'mdi-content-cut';

        const categoryClass = service.category === 'رجالي' ? 'bg-primary' :
                             service.category === 'نسائي' ? 'bg-pink' : 'bg-info';

        const card = `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5><i class="mdi ${typeIcon}"></i> ${service.name}</h5>
                            <span class="badge ${categoryClass}">${service.category}</span>
                        </div>
                        <p class="text-muted">${service.description}</p>
                        <div class="row">
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="mdi mdi-cash text-success me-1"></i>
                                    <span class="h6 text-primary mb-0">${service.price} ريال</span>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="mdi mdi-clock text-warning me-1"></i>
                                    <span class="text-muted">${service.duration} دقيقة</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 d-flex justify-content-between">
                            <div>
                                <button class="btn btn-sm btn-info" onclick="editService(${service.id})" title="تعديل">
                                    <i class="mdi mdi-pencil"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteService(${service.id})" title="حذف">
                                    <i class="mdi mdi-delete"></i>
                                </button>
                            </div>
                            <div class="text-muted small">
                                <i class="mdi mdi-tag"></i> ID: ${service.id}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML += card;
    });
}

function searchServices() {
    const searchTerm = document.getElementById('servicesSearch').value.toLowerCase();
    const filtered = allServices.filter(service =>
        service.name.toLowerCase().includes(searchTerm) ||
        service.description.toLowerCase().includes(searchTerm)
    );
    displayServices(filtered);
}

function filterServices() {
    const priceFilter = document.getElementById('servicesPriceFilter').value;
    const durationFilter = document.getElementById('servicesDurationFilter').value;
    const typeFilter = document.getElementById('servicesTypeFilter').value;
    const searchTerm = document.getElementById('servicesSearch').value.toLowerCase();

    let filtered = allServices.filter(service => {
        const matchesSearch = !searchTerm ||
            service.name.toLowerCase().includes(searchTerm) ||
            service.description.toLowerCase().includes(searchTerm);

        let matchesPrice = true;
        if (priceFilter === '0-50') {
            matchesPrice = service.price < 50;
        } else if (priceFilter === '50-100') {
            matchesPrice = service.price >= 50 && service.price <= 100;
        } else if (priceFilter === '100+') {
            matchesPrice = service.price > 100;
        }

        let matchesDuration = true;
        if (durationFilter === '0-30') {
            matchesDuration = service.duration < 30;
        } else if (durationFilter === '30-60') {
            matchesDuration = service.duration >= 30 && service.duration <= 60;
        } else if (durationFilter === '60+') {
            matchesDuration = service.duration > 60;
        }

        let matchesType = true;
        if (typeFilter === 'رجالي') {
            matchesType = service.name.includes('رجالي');
        } else if (typeFilter === 'نسائي') {
            matchesType = service.name.includes('نسائي');
        } else if (typeFilter === 'مشترك') {
            matchesType = !service.name.includes('رجالي') && !service.name.includes('نسائي');
        }

        return matchesSearch && matchesPrice && matchesDuration && matchesType;
    });

    displayServices(filtered);
}

function clearServicesFilters() {
    document.getElementById('servicesSearch').value = '';
    document.getElementById('servicesPriceFilter').value = '';
    document.getElementById('servicesDurationFilter').value = '';
    document.getElementById('servicesTypeFilter').value = '';
    displayServices(allServices);
}

function loadPayments() {
    allPayments = <?php echo json_encode($_SESSION['payments'] ?? []); ?>;
    displayPayments(allPayments);
    updatePaymentsStats(allPayments);
}

function displayPayments(payments) {
    const tbody = document.getElementById('paymentsTableBody');
    const noResults = document.getElementById('paymentsNoResults');
    const countBadge = document.getElementById('paymentsCount');

    if (!tbody) return;

    tbody.innerHTML = '';

    if (payments.length === 0) {
        if (noResults) noResults.style.display = 'block';
        if (countBadge) countBadge.textContent = '0 دفعة';
        return;
    }

    if (noResults) noResults.style.display = 'none';
    if (countBadge) countBadge.textContent = `${payments.length} دفعة`;

    payments.forEach(payment => {
        const statusClass = payment.status === 'مدفوع' ? 'bg-success' : 'bg-warning';
        const row = `
            <tr>
                <td>#${payment.id.toString().padStart(3, '0')}</td>
                <td>#${payment.booking_id.toString().padStart(3, '0')}</td>
                <td>${payment.amount} ريال</td>
                <td>${payment.method}</td>
                <td><span class="badge ${statusClass}">${payment.status}</span></td>
                <td>${payment.date}</td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="viewPayment(${payment.id})">عرض</button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function updatePaymentsStats(payments) {
    let totalRevenue = 0;
    let paidCount = 0;
    let pendingCount = 0;
    let todayRevenue = 0;
    const today = new Date().toISOString().split('T')[0];

    payments.forEach(payment => {
        if (payment.status === 'مدفوع') {
            totalRevenue += payment.amount;
            paidCount++;
            if (payment.date === today) {
                todayRevenue += payment.amount;
            }
        } else {
            pendingCount++;
        }
    });

    if (document.getElementById('totalRevenue')) {
        document.getElementById('totalRevenue').textContent = totalRevenue + ' ريال';
        document.getElementById('paidPayments').textContent = paidCount;
        document.getElementById('pendingPayments').textContent = pendingCount;
        document.getElementById('todayRevenue').textContent = todayRevenue + ' ريال';
    }
}

function searchPayments() {
    const searchTerm = document.getElementById('paymentsSearch').value.toLowerCase();
    const filtered = allPayments.filter(payment =>
        payment.id.toString().includes(searchTerm) ||
        payment.booking_id.toString().includes(searchTerm) ||
        payment.method.toLowerCase().includes(searchTerm) ||
        payment.amount.toString().includes(searchTerm)
    );
    displayPayments(filtered);
    updatePaymentsStats(filtered);
}

function filterPayments() {
    const methodFilter = document.getElementById('paymentsMethodFilter').value;
    const statusFilter = document.getElementById('paymentsStatusFilter').value;
    const dateFilter = document.getElementById('paymentsDateFilter').value;
    const amountFilter = document.getElementById('paymentsAmountFilter').value;
    const searchTerm = document.getElementById('paymentsSearch').value.toLowerCase();

    let filtered = allPayments.filter(payment => {
        const matchesSearch = !searchTerm ||
            payment.id.toString().includes(searchTerm) ||
            payment.booking_id.toString().includes(searchTerm) ||
            payment.method.toLowerCase().includes(searchTerm) ||
            payment.amount.toString().includes(searchTerm);

        const matchesMethod = !methodFilter || payment.method === methodFilter;
        const matchesStatus = !statusFilter || payment.status === statusFilter;
        const matchesDate = !dateFilter || payment.date === dateFilter;
        const matchesAmount = !amountFilter || payment.amount >= parseFloat(amountFilter);

        return matchesSearch && matchesMethod && matchesStatus && matchesDate && matchesAmount;
    });

    displayPayments(filtered);
    updatePaymentsStats(filtered);
}

function clearPaymentsFilters() {
    document.getElementById('paymentsSearch').value = '';
    document.getElementById('paymentsMethodFilter').value = '';
    document.getElementById('paymentsStatusFilter').value = '';
    document.getElementById('paymentsDateFilter').value = '';
    document.getElementById('paymentsAmountFilter').value = '';
    displayPayments(allPayments);
    updatePaymentsStats(allPayments);
}

function showPaymentModal(bookingId, amount) {
    const modalHtml = `
        <div class="modal fade" id="paymentModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">💳 تسجيل دفع للحجز #${bookingId.toString().padStart(3, '0')}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <input type="hidden" name="booking_id" value="${bookingId}">
                            <div class="mb-3">
                                <label class="form-label">المبلغ</label>
                                <input type="number" class="form-control" name="amount" value="${amount}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-control" name="method" required>
                                    <option value="نقدي">نقدي</option>
                                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                    <option value="تحويل بنكي">تحويل بنكي</option>
                                    <option value="محفظة إلكترونية">محفظة إلكترونية</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="savePayment()">تسجيل الدفع</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();

    document.getElementById('paymentModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function savePayment() {
    const form = document.querySelector('#paymentModal form');
    const formData = new FormData(form);
    formData.append('action', 'save_payment');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('paymentModal')).hide();
            if (document.getElementById('paymentsTableBody')) {
                loadPayments();
            }
        } else {
            showErrorMessage('حدث خطأ في حفظ البيانات');
        }
    });
}

function loadComplaints() {
    allComplaints = <?php echo json_encode($_SESSION['complaints'] ?? []); ?>;
    displayComplaints(allComplaints);
    updateComplaintsStats(allComplaints);

    // تحديث فلتر الحلاقين
    const barberFilter = document.getElementById('complaintsBarberFilter');
    if (barberFilter) {
        barberFilter.innerHTML = '<option value="">كل الحلاقين</option>';
        const barbers = [...new Set(allComplaints.map(c => c.barber_name))];
        barbers.forEach(barber => {
            barberFilter.innerHTML += `<option value="${barber}">${barber}</option>`;
        });
    }
}

function displayComplaints(complaints) {
    const tbody = document.getElementById('complaintsTableBody');
    const noResults = document.getElementById('complaintsNoResults');
    const countBadge = document.getElementById('complaintsCount');

    if (!tbody) return;

    tbody.innerHTML = '';

    if (complaints.length === 0) {
        if (noResults) noResults.style.display = 'block';
        if (countBadge) countBadge.textContent = '0 شكوى';
        return;
    }

    if (noResults) noResults.style.display = 'none';
    if (countBadge) countBadge.textContent = `${complaints.length} شكوى`;

    complaints.forEach(complaint => {
        let statusClass = 'bg-secondary';
        let priorityClass = 'bg-info';

        if (complaint.status === 'جديد') {
            statusClass = 'bg-danger';
        } else if (complaint.status === 'قيد المراجعة') {
            statusClass = 'bg-warning';
        } else if (complaint.status === 'مغلق') {
            statusClass = 'bg-success';
        }

        if (complaint.priority === 'عالي') {
            priorityClass = 'bg-danger';
        } else if (complaint.priority === 'متوسط') {
            priorityClass = 'bg-warning';
        } else {
            priorityClass = 'bg-info';
        }

        const row = `
            <tr>
                <td>#${complaint.id.toString().padStart(3, '0')}</td>
                <td>
                    <strong>${complaint.customer_name}</strong><br>
                    <small class="text-muted">${complaint.customer_phone}</small>
                </td>
                <td>${complaint.barber_name}</td>
                <td>
                    <div class="complaint-text" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                        ${complaint.complaint}
                    </div>
                </td>
                <td><span class="badge ${priorityClass}">${complaint.priority}</span></td>
                <td><span class="badge ${statusClass}">${complaint.status}</span></td>
                <td>${new Date(complaint.date).toLocaleDateString('ar-SA')}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="viewComplaint(${complaint.id})">عرض</button>
                    <button class="btn btn-sm btn-success" onclick="replyComplaint(${complaint.id})">رد</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteComplaint(${complaint.id})">حذف</button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function updateComplaintsStats(complaints) {
    let totalComplaints = complaints.length;
    let newComplaints = 0;
    let pendingComplaints = 0;
    let closedComplaints = 0;

    complaints.forEach(complaint => {
        if (complaint.status === 'جديد') {
            newComplaints++;
        } else if (complaint.status === 'قيد المراجعة') {
            pendingComplaints++;
        } else if (complaint.status === 'مغلق') {
            closedComplaints++;
        }
    });

    if (document.getElementById('totalComplaints')) {
        document.getElementById('totalComplaints').textContent = totalComplaints;
        document.getElementById('newComplaints').textContent = newComplaints;
        document.getElementById('pendingComplaints').textContent = pendingComplaints;
        document.getElementById('closedComplaints').textContent = closedComplaints;
        document.getElementById('newComplaintsCount').textContent = newComplaints;
    }
}

function searchComplaints() {
    const searchTerm = document.getElementById('complaintsSearch').value.toLowerCase();
    const filtered = allComplaints.filter(complaint =>
        complaint.customer_name.toLowerCase().includes(searchTerm) ||
        complaint.barber_name.toLowerCase().includes(searchTerm) ||
        complaint.complaint.toLowerCase().includes(searchTerm) ||
        complaint.id.toString().includes(searchTerm)
    );
    displayComplaints(filtered);
    updateComplaintsStats(filtered);
}

function filterComplaints() {
    const statusFilter = document.getElementById('complaintsStatusFilter').value;
    const priorityFilter = document.getElementById('complaintsPriorityFilter').value;
    const dateFilter = document.getElementById('complaintsDateFilter').value;
    const barberFilter = document.getElementById('complaintsBarberFilter').value;
    const searchTerm = document.getElementById('complaintsSearch').value.toLowerCase();

    let filtered = allComplaints.filter(complaint => {
        const matchesSearch = !searchTerm ||
            complaint.customer_name.toLowerCase().includes(searchTerm) ||
            complaint.barber_name.toLowerCase().includes(searchTerm) ||
            complaint.complaint.toLowerCase().includes(searchTerm) ||
            complaint.id.toString().includes(searchTerm);

        const matchesStatus = !statusFilter || complaint.status === statusFilter;
        const matchesPriority = !priorityFilter || complaint.priority === priorityFilter;
        const matchesDate = !dateFilter || complaint.date.startsWith(dateFilter);
        const matchesBarber = !barberFilter || complaint.barber_name === barberFilter;

        return matchesSearch && matchesStatus && matchesPriority && matchesDate && matchesBarber;
    });

    displayComplaints(filtered);
    updateComplaintsStats(filtered);
}

function clearComplaintsFilters() {
    document.getElementById('complaintsSearch').value = '';
    document.getElementById('complaintsStatusFilter').value = '';
    document.getElementById('complaintsPriorityFilter').value = '';
    document.getElementById('complaintsDateFilter').value = '';
    document.getElementById('complaintsBarberFilter').value = '';
    displayComplaints(allComplaints);
    updateComplaintsStats(allComplaints);
}

function loadMessages() {
    allMessages = <?php echo json_encode($_SESSION['messages'] ?? []); ?>;
    displayMessages(allMessages);
    updateMessagesStats(allMessages);
}

function displayMessages(messages) {
    const tbody = document.getElementById('messagesTableBody');
    const noResults = document.getElementById('messagesNoResults');
    const countBadge = document.getElementById('messagesCount');

    if (!tbody) return;

    tbody.innerHTML = '';

    if (messages.length === 0) {
        if (noResults) noResults.style.display = 'block';
        if (countBadge) countBadge.textContent = '0 رسالة';
        return;
    }

    if (noResults) noResults.style.display = 'none';
    if (countBadge) countBadge.textContent = `${messages.length} رسالة`;

    messages.forEach(message => {
        let statusClass = 'bg-secondary';
        let typeClass = message.sender_type === 'عميل' ? 'bg-primary' : 'bg-success';

        if (message.status === 'جديد') {
            statusClass = 'bg-danger';
        } else if (message.status === 'مقروء') {
            statusClass = 'bg-warning';
        } else if (message.status === 'مغلق') {
            statusClass = 'bg-success';
        }

        const row = `
            <tr>
                <td>#${message.id.toString().padStart(3, '0')}</td>
                <td>
                    <strong>${message.sender_name}</strong><br>
                    <small class="text-muted">${message.sender_phone}</small>
                </td>
                <td><span class="badge ${typeClass}">${message.sender_type}</span></td>
                <td>
                    <strong>${message.subject}</strong><br>
                    <small class="text-muted">${message.message.substring(0, 50)}...</small>
                </td>
                <td><span class="badge ${statusClass}">${message.status}</span></td>
                <td>${new Date(message.date).toLocaleDateString('ar-SA')}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="viewMessage(${message.id})">عرض</button>
                    <button class="btn btn-sm btn-success" onclick="replyMessage(${message.id})">رد</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteMessage(${message.id})">حذف</button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function updateMessagesStats(messages) {
    let totalMessages = messages.length;
    let newMessages = 0;
    let repliedMessages = 0;

    messages.forEach(message => {
        if (message.status === 'جديد') {
            newMessages++;
        } else if (message.status === 'مغلق') {
            repliedMessages++;
        }
    });

    if (document.getElementById('totalMessages')) {
        document.getElementById('totalMessages').textContent = totalMessages;
        document.getElementById('newMessages').textContent = newMessages;
        document.getElementById('repliedMessages').textContent = repliedMessages;
        document.getElementById('newMessagesCount').textContent = newMessages;
    }
}

function searchMessages() {
    const searchTerm = document.getElementById('messagesSearch').value.toLowerCase();
    const filtered = allMessages.filter(message =>
        message.sender_name.toLowerCase().includes(searchTerm) ||
        message.subject.toLowerCase().includes(searchTerm) ||
        message.message.toLowerCase().includes(searchTerm) ||
        message.id.toString().includes(searchTerm)
    );
    displayMessages(filtered);
    updateMessagesStats(filtered);
}

function filterMessages() {
    const statusFilter = document.getElementById('messagesStatusFilter').value;
    const typeFilter = document.getElementById('messagesTypeFilter').value;
    const dateFilter = document.getElementById('messagesDateFilter').value;
    const subjectFilter = document.getElementById('messagesSubjectFilter').value;
    const searchTerm = document.getElementById('messagesSearch').value.toLowerCase();

    let filtered = allMessages.filter(message => {
        const matchesSearch = !searchTerm ||
            message.sender_name.toLowerCase().includes(searchTerm) ||
            message.subject.toLowerCase().includes(searchTerm) ||
            message.message.toLowerCase().includes(searchTerm) ||
            message.id.toString().includes(searchTerm);

        const matchesStatus = !statusFilter || message.status === statusFilter;
        const matchesType = !typeFilter || message.sender_type === typeFilter;
        const matchesDate = !dateFilter || message.date.startsWith(dateFilter);
        const matchesSubject = !subjectFilter || message.subject.toLowerCase().includes(subjectFilter);

        return matchesSearch && matchesStatus && matchesType && matchesDate && matchesSubject;
    });

    displayMessages(filtered);
    updateMessagesStats(filtered);
}

function clearMessagesFilters() {
    document.getElementById('messagesSearch').value = '';
    document.getElementById('messagesStatusFilter').value = '';
    document.getElementById('messagesTypeFilter').value = '';
    document.getElementById('messagesDateFilter').value = '';
    document.getElementById('messagesSubjectFilter').value = '';
    displayMessages(allMessages);
    updateMessagesStats(allMessages);
}

function loadBans() {
    allBans = <?php echo json_encode($_SESSION['barber_bans'] ?? []); ?>;
    displayBans(allBans);
    updateBansStats(allBans);
}

function displayBans(bans) {
    const tbody = document.getElementById('bansTableBody');
    const noResults = document.getElementById('bansNoResults');
    const countBadge = document.getElementById('bansCount');

    if (!tbody) return;

    tbody.innerHTML = '';

    if (bans.length === 0) {
        if (noResults) noResults.style.display = 'block';
        if (countBadge) countBadge.textContent = '0 حظر';
        return;
    }

    if (noResults) noResults.style.display = 'none';
    if (countBadge) countBadge.textContent = `${bans.length} حظر`;

    bans.forEach(ban => {
        let statusClass = ban.status === 'نشط' ? 'bg-danger' : 'bg-secondary';
        let typeClass = ban.ban_type === 'مؤقت' ? 'bg-warning' : 'bg-danger';

        const endDate = ban.end_date ? new Date(ban.end_date).toLocaleDateString('ar-SA') : 'دائم';

        const row = `
            <tr>
                <td>#${ban.id.toString().padStart(3, '0')}</td>
                <td>${ban.barber_name}</td>
                <td>${ban.reason}</td>
                <td><span class="badge ${typeClass}">${ban.ban_type}</span></td>
                <td>${new Date(ban.start_date).toLocaleDateString('ar-SA')}</td>
                <td>${endDate}</td>
                <td><span class="badge ${statusClass}">${ban.status}</span></td>
                <td>
                    ${ban.status === 'نشط' ?
                        `<button class="btn btn-sm btn-success" onclick="unbanBarber(${ban.id})">إلغاء الحظر</button>` :
                        `<span class="text-muted">ملغي</span>`
                    }
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function updateBansStats(bans) {
    let totalBans = bans.length;
    let activeBans = 0;
    let temporaryBans = 0;

    bans.forEach(ban => {
        if (ban.status === 'نشط') {
            activeBans++;
            if (ban.ban_type === 'مؤقت') {
                temporaryBans++;
            }
        }
    });

    if (document.getElementById('totalBans')) {
        document.getElementById('totalBans').textContent = totalBans;
        document.getElementById('activeBans').textContent = activeBans;
        document.getElementById('temporaryBans').textContent = temporaryBans;
    }
}

function searchBans() {
    const searchTerm = document.getElementById('bansSearch').value.toLowerCase();
    const filtered = allBans.filter(ban =>
        ban.barber_name.toLowerCase().includes(searchTerm) ||
        ban.reason.toLowerCase().includes(searchTerm) ||
        ban.id.toString().includes(searchTerm)
    );
    displayBans(filtered);
    updateBansStats(filtered);
}

function filterBans() {
    const statusFilter = document.getElementById('bansStatusFilter').value;
    const typeFilter = document.getElementById('bansTypeFilter').value;
    const dateFilter = document.getElementById('bansDateFilter').value;
    const reasonFilter = document.getElementById('bansReasonFilter').value;
    const searchTerm = document.getElementById('bansSearch').value.toLowerCase();

    let filtered = allBans.filter(ban => {
        const matchesSearch = !searchTerm ||
            ban.barber_name.toLowerCase().includes(searchTerm) ||
            ban.reason.toLowerCase().includes(searchTerm) ||
            ban.id.toString().includes(searchTerm);

        const matchesStatus = !statusFilter || ban.status === statusFilter;
        const matchesType = !typeFilter || ban.ban_type === typeFilter;
        const matchesDate = !dateFilter || ban.start_date === dateFilter;
        const matchesReason = !reasonFilter || ban.reason.toLowerCase().includes(reasonFilter);

        return matchesSearch && matchesStatus && matchesType && matchesDate && matchesReason;
    });

    displayBans(filtered);
    updateBansStats(filtered);
}

function clearBansFilters() {
    document.getElementById('bansSearch').value = '';
    document.getElementById('bansStatusFilter').value = '';
    document.getElementById('bansTypeFilter').value = '';
    document.getElementById('bansDateFilter').value = '';
    document.getElementById('bansReasonFilter').value = '';
    displayBans(allBans);
    updateBansStats(allBans);
}

function showNewPaymentModal(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    const modalHtml = `
        <div class="modal fade" id="newPaymentModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">💳 تسجيل دفع جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="mb-3">
                                <label class="form-label">رقم الحجز</label>
                                <select class="form-control" name="booking_id" required>
                                    <option value="">اختر الحجز</option>
                                    <option value="1">حجز #001 - أحمد محمد</option>
                                    <option value="2">حجز #002 - سارة أحمد</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المبلغ</label>
                                <input type="number" class="form-control" name="amount" placeholder="المبلغ بالريال" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-control" name="method" required>
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="نقدي">نقدي</option>
                                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                    <option value="تحويل بنكي">تحويل بنكي</option>
                                    <option value="محفظة إلكترونية">محفظة إلكترونية</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="saveNewPayment()">تسجيل الدفع</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('newPaymentModal'));
    modal.show();

    document.getElementById('newPaymentModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });

    return false;
}

function saveNewPayment() {
    const form = document.querySelector('#newPaymentModal form');
    const formData = new FormData(form);
    formData.append('action', 'save_payment');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('newPaymentModal')).hide();
            if (document.getElementById('paymentsTableBody')) {
                loadPayments();
            }
        } else {
            showErrorMessage('حدث خطأ في حفظ البيانات');
        }
    });
}

function updatePrice(selectElement) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const price = selectedOption.getAttribute('data-price');
    const priceInput = document.getElementById('bookingPrice');
    if (priceInput && price) {
        priceInput.value = price;
    }
}

function editBooking(id) {
    const bookings = <?php echo json_encode($_SESSION['bookings'] ?? []); ?>;
    const booking = bookings.find(b => b.id === id);
    if (!booking) return;

    const modalHtml = `
        <div class="modal fade" id="editBookingModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">✏️ تعديل الحجز #${id.toString().padStart(3, '0')}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <input type="hidden" name="id" value="${id}">
                            <div class="mb-3">
                                <label class="form-label">اسم العميل</label>
                                <input type="text" class="form-control" name="customer" value="${booking.customer}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الحلاق</label>
                                <select class="form-control" name="barber" required>
                                    <option value="محمد الحلاق" ${booking.barber === 'محمد الحلاق' ? 'selected' : ''}>محمد الحلاق</option>
                                    <option value="فاطمة الحلاقة" ${booking.barber === 'فاطمة الحلاقة' ? 'selected' : ''}>فاطمة الحلاقة</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الخدمة</label>
                                <select class="form-control" name="service" required onchange="updateEditPrice(this)">
                                    <option value="قص شعر رجالي" data-price="50" ${booking.service === 'قص شعر رجالي' ? 'selected' : ''}>قص شعر رجالي - 50 ريال</option>
                                    <option value="تصفيف شعر نسائي" data-price="80" ${booking.service === 'تصفيف شعر نسائي' ? 'selected' : ''}>تصفيف شعر نسائي - 80 ريال</option>
                                    <option value="حلاقة كاملة" data-price="70" ${booking.service === 'حلاقة كاملة' ? 'selected' : ''}>حلاقة كاملة - 70 ريال</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">السعر</label>
                                <input type="number" class="form-control" name="price" id="editBookingPrice" value="${booking.price}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">التاريخ</label>
                                <input type="date" class="form-control" name="date" value="${booking.date}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الوقت</label>
                                <input type="time" class="form-control" name="time" value="${booking.time}" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveBookingEdit()">حفظ التعديل</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('editBookingModal'));
    modal.show();

    document.getElementById('editBookingModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function saveBookingEdit() {
    const form = document.querySelector('#editBookingModal form');
    const formData = new FormData(form);
    formData.append('action', 'update_booking');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('editBookingModal')).hide();
            loadBookings();
        } else {
            showErrorMessage('حدث خطأ في تحديث الحجز');
        }
    });
}

function editBarber(id) {
    const barbers = <?php echo json_encode($_SESSION['barbers'] ?? []); ?>;
    const barber = barbers.find(b => b.id === id);
    if (!barber) return;

    const modalHtml = `
        <div class="modal fade" id="editBarberModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">✏️ تعديل بيانات الحلاق: ${barber.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <input type="hidden" name="id" value="${id}">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control" name="name" value="${barber.name}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone" value="${barber.phone}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">سنوات الخبرة</label>
                                        <input type="number" class="form-control" name="experience" value="${barber.experience}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">التخصص</label>
                                        <select class="form-control" name="specialty" required>
                                            <option value="حلاقة رجالي" ${barber.specialty === 'حلاقة رجالي' ? 'selected' : ''}>حلاقة رجالي</option>
                                            <option value="تصفيف نسائي" ${barber.specialty === 'تصفيف نسائي' ? 'selected' : ''}>تصفيف نسائي</option>
                                            <option value="كلاهما" ${barber.specialty === 'كلاهما' ? 'selected' : ''}>كلاهما</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الدولة</label>
                                <select class="form-control" name="country" required onchange="loadCities(this.value, 'editBarberCitySelect')">
                                    <option value="">اختر الدولة</option>
                                    <option value="مصر" ${barber.country === 'مصر' ? 'selected' : ''}>🇪🇬 مصر</option>
                                    <option value="السعودية" ${barber.country === 'السعودية' ? 'selected' : ''}>🇸🇦 السعودية</option>
                                    <option value="الإمارات" ${barber.country === 'الإمارات' ? 'selected' : ''}>🇦🇪 الإمارات العربية المتحدة</option>
                                    <option value="الكويت" ${barber.country === 'الكويت' ? 'selected' : ''}>🇰🇼 الكويت</option>
                                    <option value="قطر" ${barber.country === 'قطر' ? 'selected' : ''}>🇶🇦 قطر</option>
                                    <option value="البحرين" ${barber.country === 'البحرين' ? 'selected' : ''}>🇧🇭 البحرين</option>
                                    <option value="عمان" ${barber.country === 'عمان' ? 'selected' : ''}>🇴🇲 عمان</option>
                                    <option value="الأردن" ${barber.country === 'الأردن' ? 'selected' : ''}>🇯🇴 الأردن</option>
                                    <option value="لبنان" ${barber.country === 'لبنان' ? 'selected' : ''}>🇱🇧 لبنان</option>
                                    <option value="سوريا" ${barber.country === 'سوريا' ? 'selected' : ''}>🇸🇾 سوريا</option>
                                    <option value="العراق" ${barber.country === 'العراق' ? 'selected' : ''}>🇮🇶 العراق</option>
                                    <option value="المغرب" ${barber.country === 'المغرب' ? 'selected' : ''}>🇲🇦 المغرب</option>
                                    <option value="الجزائر" ${barber.country === 'الجزائر' ? 'selected' : ''}>🇩🇿 الجزائر</option>
                                    <option value="تونس" ${barber.country === 'تونس' ? 'selected' : ''}>🇹🇳 تونس</option>
                                    <option value="ليبيا" ${barber.country === 'ليبيا' ? 'selected' : ''}>🇱🇾 ليبيا</option>
                                    <option value="السودان" ${barber.country === 'السودان' ? 'selected' : ''}>🇸🇩 السودان</option>
                                    <option value="فلسطين" ${barber.country === 'فلسطين' ? 'selected' : ''}>🇵🇸 فلسطين</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المحافظة/المدينة</label>
                                <select class="form-control" name="city" id="editBarberCitySelect" required>
                                    <option value="${barber.city}" selected>${barber.city}</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">العنوان التفصيلي</label>
                                <textarea class="form-control" name="address" rows="2" required>${barber.address || ''}</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveBarberEdit()">حفظ التعديل</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('editBarberModal'));
    modal.show();

    // تحميل المحافظات للدولة المحددة
    if (barber.country) {
        setTimeout(() => {
            loadCitiesForEdit(barber.country, 'editBarberCitySelect', barber.city);
        }, 100);
    }

    document.getElementById('editBarberModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function saveBarberEdit() {
    const form = document.querySelector('#editBarberModal form');
    const formData = new FormData(form);
    formData.append('action', 'update_barber');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('editBarberModal')).hide();
            loadBarbers();
        } else {
            showErrorMessage('حدث خطأ في تحديث بيانات الحلاق');
        }
    });
}

function viewPayment(id) {
    showSuccessMessage('سيتم إضافة وظيفة العرض قريباً');
}

function viewComplaint(id) {
    const complaints = <?php echo json_encode($_SESSION['complaints'] ?? []); ?>;
    const complaint = complaints.find(c => c.id === id);
    if (!complaint) return;

    const modalHtml = `
        <div class="modal fade" id="viewComplaintModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">🔍 تفاصيل الشكوى #${id.toString().padStart(3, '0')}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات العميل:</h6>
                                <p><strong>الاسم:</strong> ${complaint.customer_name}</p>
                                <p><strong>الهاتف:</strong> ${complaint.customer_phone}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>معلومات الشكوى:</h6>
                                <p><strong>الحلاق:</strong> ${complaint.barber_name}</p>
                                <p><strong>الأولوية:</strong> <span class="badge bg-warning">${complaint.priority}</span></p>
                                <p><strong>الحالة:</strong> <span class="badge bg-info">${complaint.status}</span></p>
                                <p><strong>التاريخ:</strong> ${new Date(complaint.date).toLocaleString('ar-SA')}</p>
                            </div>
                        </div>
                        <hr>
                        <h6>نص الشكوى:</h6>
                        <div class="alert alert-light">
                            ${complaint.complaint}
                        </div>
                        ${complaint.admin_reply ? `
                            <h6>رد الإدارة:</h6>
                            <div class="alert alert-success">
                                ${complaint.admin_reply}
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="replyComplaint(${id})">رد على الشكوى</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('viewComplaintModal'));
    modal.show();

    document.getElementById('viewComplaintModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function replyComplaint(id) {
    const complaints = <?php echo json_encode($_SESSION['complaints'] ?? []); ?>;
    const complaint = complaints.find(c => c.id === id);
    if (!complaint) return;

    const modalHtml = `
        <div class="modal fade" id="replyComplaintModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">💬 الرد على الشكوى #${id.toString().padStart(3, '0')}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <input type="hidden" name="id" value="${id}">
                            <div class="mb-3">
                                <label class="form-label">نص الشكوى:</label>
                                <div class="alert alert-light">
                                    ${complaint.complaint}
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">ردك على الشكوى:</label>
                                <textarea class="form-control" name="reply" rows="4" placeholder="اكتب ردك هنا..." required>${complaint.admin_reply || ''}</textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">حالة الشكوى:</label>
                                <select class="form-control" name="status" required>
                                    <option value="جديد" ${complaint.status === 'جديد' ? 'selected' : ''}>جديد</option>
                                    <option value="قيد المراجعة" ${complaint.status === 'قيد المراجعة' ? 'selected' : ''}>قيد المراجعة</option>
                                    <option value="مغلق" ${complaint.status === 'مغلق' ? 'selected' : ''}>مغلق</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="saveComplaintReply()">حفظ الرد</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('replyComplaintModal'));
    modal.show();

    document.getElementById('replyComplaintModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function saveComplaintReply() {
    const form = document.querySelector('#replyComplaintModal form');
    const formData = new FormData(form);
    formData.append('action', 'reply_complaint');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('replyComplaintModal')).hide();
            loadComplaints();
        } else {
            showErrorMessage('حدث خطأ في حفظ الرد');
        }
    });
}

function deleteComplaint(id) {
    if (confirm('هل أنت متأكد من حذف هذه الشكوى؟')) {
        const formData = new FormData();
        formData.append('action', 'delete_complaint');
        formData.append('id', id);

        fetch('admin.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessMessage(data.message);
                loadComplaints();
            } else {
                showErrorMessage('حدث خطأ في حذف الشكوى');
            }
        });
    }
}

function viewMessage(id) {
    const messages = <?php echo json_encode($_SESSION['messages'] ?? []); ?>;
    const message = messages.find(m => m.id === id);
    if (!message) return;

    const modalHtml = `
        <div class="modal fade" id="viewMessageModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">📧 تفاصيل الرسالة #${id.toString().padStart(3, '0')}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات المرسل:</h6>
                                <p><strong>الاسم:</strong> ${message.sender_name}</p>
                                <p><strong>الهاتف:</strong> ${message.sender_phone}</p>
                                <p><strong>النوع:</strong> <span class="badge bg-primary">${message.sender_type}</span></p>
                            </div>
                            <div class="col-md-6">
                                <h6>معلومات الرسالة:</h6>
                                <p><strong>الموضوع:</strong> ${message.subject}</p>
                                <p><strong>الحالة:</strong> <span class="badge bg-info">${message.status}</span></p>
                                <p><strong>التاريخ:</strong> ${new Date(message.date).toLocaleString('ar-SA')}</p>
                            </div>
                        </div>
                        <hr>
                        <h6>نص الرسالة:</h6>
                        <div class="alert alert-light">
                            ${message.message}
                        </div>
                        ${message.admin_reply ? `
                            <h6>ردك:</h6>
                            <div class="alert alert-success">
                                ${message.admin_reply}
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="replyMessage(${id})">رد على الرسالة</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('viewMessageModal'));
    modal.show();

    document.getElementById('viewMessageModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function replyMessage(id) {
    const messages = <?php echo json_encode($_SESSION['messages'] ?? []); ?>;
    const message = messages.find(m => m.id === id);
    if (!message) return;

    const modalHtml = `
        <div class="modal fade" id="replyMessageModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">💬 الرد على الرسالة #${id.toString().padStart(3, '0')}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <input type="hidden" name="id" value="${id}">
                            <div class="mb-3">
                                <label class="form-label">الموضوع: ${message.subject}</label>
                                <div class="alert alert-light">
                                    ${message.message}
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">ردك:</label>
                                <textarea class="form-control" name="reply" rows="4" placeholder="اكتب ردك هنا..." required>${message.admin_reply || ''}</textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">حالة الرسالة:</label>
                                <select class="form-control" name="status" required>
                                    <option value="جديد" ${message.status === 'جديد' ? 'selected' : ''}>جديد</option>
                                    <option value="مقروء" ${message.status === 'مقروء' ? 'selected' : ''}>مقروء</option>
                                    <option value="مغلق" ${message.status === 'مغلق' ? 'selected' : ''}>مغلق</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="saveMessageReply()">حفظ الرد</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('replyMessageModal'));
    modal.show();

    document.getElementById('replyMessageModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function saveMessageReply() {
    const form = document.querySelector('#replyMessageModal form');
    const formData = new FormData(form);
    formData.append('action', 'reply_message');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('replyMessageModal')).hide();
            loadMessages();
        } else {
            showErrorMessage('حدث خطأ في حفظ الرد');
        }
    });
}

function deleteMessage(id) {
    if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
        const formData = new FormData();
        formData.append('action', 'delete_message');
        formData.append('id', id);

        fetch('admin.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessMessage(data.message);
                loadMessages();
            } else {
                showErrorMessage('حدث خطأ في حذف الرسالة');
            }
        });
    }
}

function showBanBarberModal(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    const barbers = <?php echo json_encode($_SESSION['barbers'] ?? []); ?>;
    let barbersOptions = '<option value="">اختر الحلاق</option>';
    barbers.forEach(barber => {
        if (barber.status !== 'محظور') {
            barbersOptions += `<option value="${barber.id}">${barber.name}</option>`;
        }
    });

    const modalHtml = `
        <div class="modal fade" id="banBarberModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">🚫 حظر حلاق</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="mb-3">
                                <label class="form-label">الحلاق:</label>
                                <select class="form-control" name="barber_id" required>
                                    ${barbersOptions}
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">سبب الحظر:</label>
                                <textarea class="form-control" name="reason" rows="3" placeholder="اكتب سبب الحظر..." required></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">نوع الحظر:</label>
                                <select class="form-control" name="ban_type" required onchange="toggleDaysInput(this)">
                                    <option value="">اختر نوع الحظر</option>
                                    <option value="مؤقت">مؤقت</option>
                                    <option value="دائم">دائم</option>
                                </select>
                            </div>
                            <div class="mb-3" id="daysContainer" style="display: none;">
                                <label class="form-label">عدد الأيام:</label>
                                <input type="number" class="form-control" name="days" min="1" max="365" placeholder="عدد أيام الحظر">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">ملاحظات إضافية:</label>
                                <textarea class="form-control" name="notes" rows="2" placeholder="ملاحظات للإدارة (اختياري)"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" onclick="saveBanBarber()">تطبيق الحظر</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('banBarberModal'));
    modal.show();

    document.getElementById('banBarberModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });

    return false;
}

function toggleDaysInput(selectElement) {
    const daysContainer = document.getElementById('daysContainer');
    const daysInput = document.querySelector('input[name="days"]');

    if (selectElement.value === 'مؤقت') {
        daysContainer.style.display = 'block';
        daysInput.required = true;
    } else {
        daysContainer.style.display = 'none';
        daysInput.required = false;
        daysInput.value = '';
    }
}

function saveBanBarber() {
    const form = document.querySelector('#banBarberModal form');
    const formData = new FormData(form);
    formData.append('action', 'ban_barber');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('banBarberModal')).hide();
            loadBans();
            loadBarbers(); // تحديث قائمة الحلاقين
        } else {
            showErrorMessage('حدث خطأ في تطبيق الحظر');
        }
    });
}

function unbanBarber(banId) {
    if (confirm('هل أنت متأكد من إلغاء حظر هذا الحلاق؟')) {
        const formData = new FormData();
        formData.append('action', 'unban_barber');
        formData.append('id', banId);

        fetch('admin.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessMessage(data.message);
                loadBans();
                loadBarbers(); // تحديث قائمة الحلاقين
            } else {
                showErrorMessage('حدث خطأ في إلغاء الحظر');
            }
        });
    }
}

function markAllComplaintsRead() {
    showSuccessMessage('تم تحديد جميع الشكاوى كمقروءة');
}

function markAllMessagesRead() {
    showSuccessMessage('تم تحديد جميع الرسائل كمقروءة');
}

function quickBanBarber(barberId, barberName) {
    const modalHtml = `
        <div class="modal fade" id="quickBanModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">🚫 حظر سريع: ${barberName}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <input type="hidden" name="barber_id" value="${barberId}">
                            <div class="mb-3">
                                <label class="form-label">سبب الحظر:</label>
                                <select class="form-control" name="reason" required>
                                    <option value="">اختر السبب</option>
                                    <option value="شكاوى متكررة من العملاء">شكاوى متكررة من العملاء</option>
                                    <option value="عدم الالتزام بالمواعيد">عدم الالتزام بالمواعيد</option>
                                    <option value="سوء في التعامل">سوء في التعامل</option>
                                    <option value="مخالفة قوانين التطبيق">مخالفة قوانين التطبيق</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">مدة الحظر:</label>
                                <select class="form-control" name="duration" required>
                                    <option value="">اختر المدة</option>
                                    <option value="1">يوم واحد</option>
                                    <option value="3">3 أيام</option>
                                    <option value="7">أسبوع</option>
                                    <option value="14">أسبوعين</option>
                                    <option value="30">شهر</option>
                                    <option value="permanent">دائم</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" onclick="executeQuickBan()">تطبيق الحظر</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('quickBanModal'));
    modal.show();

    document.getElementById('quickBanModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function executeQuickBan() {
    const form = document.querySelector('#quickBanModal form');
    const formData = new FormData(form);

    const duration = formData.get('duration');
    const banType = duration === 'permanent' ? 'دائم' : 'مؤقت';
    const days = duration === 'permanent' ? 0 : parseInt(duration);

    formData.append('action', 'ban_barber');
    formData.append('ban_type', banType);
    formData.append('days', days);
    formData.append('notes', 'حظر سريع من صفحة الحلاقين');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('quickBanModal')).hide();
            loadBarbers();
        } else {
            showErrorMessage('حدث خطأ في تطبيق الحظر');
        }
    });
}

// وظائف إضافية مفقودة
function viewCustomer(id) {
    const customers = <?php echo json_encode($_SESSION['customers'] ?? []); ?>;
    const customer = customers.find(c => c.id === id);
    if (!customer) return;

    const modalHtml = `
        <div class="modal fade" id="viewCustomerModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">👤 تفاصيل العميل: ${customer.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>الاسم:</strong> ${customer.name}</p>
                                <p><strong>البريد الإلكتروني:</strong> ${customer.email}</p>
                                <p><strong>رقم الهاتف:</strong> ${customer.phone}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>عدد الحجوزات:</strong> ${customer.bookings_count}</p>
                                <p><strong>تاريخ التسجيل:</strong> ${customer.join_date}</p>
                                <p><strong>الحالة:</strong> ${customer.bookings_count >= 15 ? 'عميل مميز' : customer.bookings_count >= 5 ? 'عميل نشط' : 'عميل جديد'}</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="editCustomer(${id})">تعديل</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('viewCustomerModal'));
    modal.show();

    document.getElementById('viewCustomerModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function editCustomer(id) {
    const customers = <?php echo json_encode($_SESSION['customers'] ?? []); ?>;
    const customer = customers.find(c => c.id === id);
    if (!customer) return;

    const modalHtml = `
        <div class="modal fade" id="editCustomerModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">✏️ تعديل بيانات العميل: ${customer.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <input type="hidden" name="id" value="${id}">
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" name="name" value="${customer.name}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email" value="${customer.email}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" name="phone" value="${customer.phone}" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveCustomerEdit()">حفظ التعديل</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('editCustomerModal'));
    modal.show();

    document.getElementById('editCustomerModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function saveCustomerEdit() {
    const form = document.querySelector('#editCustomerModal form');
    const formData = new FormData(form);
    formData.append('action', 'update_customer');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('editCustomerModal')).hide();
            loadCustomers();
        } else {
            showErrorMessage('حدث خطأ في تحديث بيانات العميل');
        }
    });
}

function editService(id) {
    const services = <?php echo json_encode($_SESSION['services'] ?? []); ?>;
    const service = services.find(s => s.id === id);
    if (!service) return;

    const modalHtml = `
        <div class="modal fade" id="editServiceModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">✏️ تعديل الخدمة: ${service.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <input type="hidden" name="id" value="${id}">
                            <div class="mb-3">
                                <label class="form-label">اسم الخدمة</label>
                                <input type="text" class="form-control" name="name" value="${service.name}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">وصف الخدمة</label>
                                <textarea class="form-control" name="description" rows="3" required>${service.description}</textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">السعر (ريال)</label>
                                        <input type="number" class="form-control" name="price" value="${service.price}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">المدة (دقيقة)</label>
                                        <input type="number" class="form-control" name="duration" value="${service.duration}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-control" name="category" required>
                                    <option value="رجالي" ${service.name.includes('رجالي') ? 'selected' : ''}>خدمات رجالية</option>
                                    <option value="نسائي" ${service.name.includes('نسائي') ? 'selected' : ''}>خدمات نسائية</option>
                                    <option value="مشترك" ${!service.name.includes('رجالي') && !service.name.includes('نسائي') ? 'selected' : ''}>خدمات مشتركة</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveServiceEdit()">حفظ التعديل</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('editServiceModal'));
    modal.show();

    document.getElementById('editServiceModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function saveServiceEdit() {
    const form = document.querySelector('#editServiceModal form');
    const formData = new FormData(form);
    formData.append('action', 'update_service');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('editServiceModal')).hide();
            loadServices();
        } else {
            showErrorMessage('حدث خطأ في تحديث الخدمة');
        }
    });
}

function deleteService(id) {
    if (confirm('هل أنت متأكد من حذف هذه الخدمة؟')) {
        const formData = new FormData();
        formData.append('action', 'delete_service');
        formData.append('id', id);

        fetch('admin.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessMessage(data.message);
                loadServices();
            } else {
                showErrorMessage('حدث خطأ في حذف الخدمة');
            }
        });
    }
}

function showAddServiceModal(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    const modalHtml = `
        <div class="modal fade" id="addServiceModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">➕ إضافة خدمة جديدة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="mb-3">
                                <label class="form-label">اسم الخدمة</label>
                                <input type="text" class="form-control" name="name" placeholder="أدخل اسم الخدمة" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">وصف الخدمة</label>
                                <textarea class="form-control" name="description" rows="3" placeholder="أدخل وصف الخدمة" required></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">السعر (ريال)</label>
                                        <input type="number" class="form-control" name="price" placeholder="السعر" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">المدة (دقيقة)</label>
                                        <input type="number" class="form-control" name="duration" placeholder="المدة بالدقائق" required>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-control" name="category" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="رجالي">خدمات رجالية</option>
                                    <option value="نسائي">خدمات نسائية</option>
                                    <option value="مشترك">خدمات مشتركة</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveNewService()">إضافة الخدمة</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('addServiceModal'));
    modal.show();

    document.getElementById('addServiceModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function saveNewService() {
    const form = document.querySelector('#addServiceModal form');
    const formData = new FormData(form);
    formData.append('action', 'save_service');

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.message);
            bootstrap.Modal.getInstance(document.getElementById('addServiceModal')).hide();
            loadServices();
        } else {
            showErrorMessage('حدث خطأ في إضافة الخدمة');
        }
    });
}

function exportCustomers() {
    showSuccessMessage('جاري تصدير بيانات العملاء...');
}

function updateEditPrice(selectElement) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const price = selectedOption.getAttribute('data-price');
    const priceInput = document.getElementById('editBookingPrice');
    if (priceInput && price) {
        priceInput.value = price;
    }
}

function viewBarberDetails(id) {
    const barbers = <?php echo json_encode($_SESSION['barbers'] ?? []); ?>;
    const barber = barbers.find(b => b.id === id);
    if (!barber) return;

    const modalHtml = `
        <div class="modal fade" id="viewBarberModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">👨‍💼 تفاصيل الحلاق: ${barber.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <img src="https://via.placeholder.com/150" class="rounded-circle mb-3" alt="حلاق">
                                <h5>${barber.name}</h5>
                                <span class="badge ${barber.status === 'متاح' ? 'bg-success' : barber.status === 'محظور' ? 'bg-danger' : 'bg-warning'}">${barber.status}</span>
                            </div>
                            <div class="col-md-8">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong><i class="mdi mdi-phone"></i> الهاتف:</strong> ${barber.phone}</p>
                                        <p><strong><i class="mdi mdi-briefcase"></i> الخبرة:</strong> ${barber.experience} سنوات</p>
                                        <p><strong><i class="mdi mdi-scissors-cutting"></i> التخصص:</strong> ${barber.specialty}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong><i class="mdi mdi-earth"></i> الدولة:</strong> ${barber.country || 'غير محدد'}</p>
                                        <p><strong><i class="mdi mdi-map-marker"></i> المحافظة:</strong> ${barber.city || 'غير محدد'}</p>
                                        <p><strong><i class="mdi mdi-star"></i> التقييم:</strong> ${barber.rating || 0}/5</p>
                                        <p><strong><i class="mdi mdi-check-circle"></i> الخدمات المكتملة:</strong> ${barber.completed_services || 0}</p>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <p><strong><i class="mdi mdi-home"></i> العنوان:</strong></p>
                                    <p class="text-muted">${barber.address || 'لم يتم تحديد العنوان'}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="editBarber(${id})">تعديل</button>
                        ${barber.status !== 'محظور' ?
                            `<button type="button" class="btn btn-warning" onclick="quickBanBarber(${id}, '${barber.name}')">حظر</button>` :
                            `<button type="button" class="btn btn-success" onclick="viewBanDetails(${id})">عرض تفاصيل الحظر</button>`
                        }
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('viewBarberModal'));
    modal.show();

    document.getElementById('viewBarberModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function viewBanDetails(barberId) {
    const bans = <?php echo json_encode($_SESSION['barber_bans'] ?? []); ?>;
    const activeBan = bans.find(ban => ban.barber_id === barberId && ban.status === 'نشط');

    if (!activeBan) {
        showErrorMessage('لا توجد تفاصيل حظر لهذا الحلاق');
        return;
    }

    const modalHtml = `
        <div class="modal fade" id="viewBanModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">🚫 تفاصيل الحظر: ${activeBan.barber_name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <h6><i class="mdi mdi-alert-circle"></i> هذا الحلاق محظور حالياً</h6>
                        </div>
                        <p><strong>السبب:</strong> ${activeBan.reason}</p>
                        <p><strong>نوع الحظر:</strong> <span class="badge ${activeBan.ban_type === 'مؤقت' ? 'bg-warning' : 'bg-danger'}">${activeBan.ban_type}</span></p>
                        <p><strong>تاريخ البداية:</strong> ${new Date(activeBan.start_date).toLocaleDateString('ar-SA')}</p>
                        <p><strong>تاريخ النهاية:</strong> ${activeBan.end_date ? new Date(activeBan.end_date).toLocaleDateString('ar-SA') : 'دائم'}</p>
                        ${activeBan.admin_notes ? `<p><strong>ملاحظات:</strong> ${activeBan.admin_notes}</p>` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" onclick="unbanBarber(${activeBan.id})">إلغاء الحظر</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('viewBanModal'));
    modal.show();

    document.getElementById('viewBanModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// وظيفة تحميل المحافظات حسب الدولة
function loadCities(country, selectId) {
    const citySelect = document.getElementById(selectId);

    if (!country) {
        citySelect.innerHTML = '<option value="">اختر الدولة أولاً</option>';
        citySelect.disabled = true;
        return;
    }

    const formData = new FormData();
    formData.append('action', 'get_cities');
    formData.append('country', country);

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            citySelect.innerHTML = '<option value="">اختر المحافظة/المدينة</option>';
            data.cities.forEach(city => {
                citySelect.innerHTML += `<option value="${city}">${city}</option>`;
            });
            citySelect.disabled = false;
        } else {
            citySelect.innerHTML = '<option value="">خطأ في تحميل المحافظات</option>';
        }
    })
    .catch(error => {
        citySelect.innerHTML = '<option value="">خطأ في الاتصال</option>';
    });
}

// تحميل المحافظات عند تحميل نموذج التعديل
function loadCitiesForEdit(country, selectId, selectedCity) {
    const citySelect = document.getElementById(selectId);

    if (!country) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'get_cities');
    formData.append('country', country);

    fetch('admin.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            citySelect.innerHTML = '<option value="">اختر المحافظة/المدينة</option>';
            data.cities.forEach(city => {
                const selected = city === selectedCity ? 'selected' : '';
                citySelect.innerHTML += `<option value="${city}" ${selected}>${city}</option>`;
            });
            citySelect.disabled = false;
        }
    });
}

// تحميل الصفحة الافتراضية
document.addEventListener('DOMContentLoaded', function() {
    showPage('dashboard');

    // تحديث البيانات عند تحميل الصفحات
    const originalShowPage = showPage;
    showPage = function(pageName, event) {
        originalShowPage(pageName, event);

        // تحميل البيانات حسب الصفحة
        setTimeout(() => {
            if (pageName === 'bookings') {
                loadBookings();
            } else if (pageName === 'barbers') {
                loadBarbers();
            } else if (pageName === 'customers') {
                loadCustomers();
            } else if (pageName === 'services') {
                loadServices();
            } else if (pageName === 'payments') {
                loadPayments();
            } else if (pageName === 'complaints') {
                loadComplaints();
            } else if (pageName === 'messages') {
                loadMessages();
            } else if (pageName === 'bans') {
                loadBans();
            }
        }, 100);
    };
});
</script>
</body>
</html>
