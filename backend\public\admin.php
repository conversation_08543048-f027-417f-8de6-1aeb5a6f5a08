<?php
session_start();

// Simple authentication
$admin_username = '<EMAIL>';
$admin_password = 'admin123';

// Handle login
if (isset($_POST['login'])) {
    if ($_POST['email'] === $admin_username && $_POST['password'] === $admin_password) {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_email'] = $admin_username;
    } else {
        $error = 'بيانات تسجيل الدخول غير صحيحة';
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin.php');
    exit;
}

// Check if logged in
$is_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حلاق على بابك - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.3.67/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .admin-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1200px;
        }
        .login-container {
            max-width: 400px;
            margin: 100px auto;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 600px;
            border-radius: 20px 0 0 20px;
        }
        .content-area {
            padding: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e0e0e0;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>

<?php if (!$is_logged_in): ?>
    <!-- Login Form -->
    <div class="login-container">
        <div class="admin-container p-4">
            <div class="text-center mb-4">
                <h1 class="h3">🚀 حلاق على بابك</h1>
                <p class="text-muted">لوحة التحكم الإدارية</p>
            </div>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="mdi mdi-alert-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="email" name="email" required 
                           placeholder="<EMAIL>">
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <input type="password" class="form-control" id="password" name="password" required 
                           placeholder="admin123">
                </div>
                <button type="submit" name="login" class="btn btn-primary w-100">
                    <i class="mdi mdi-login"></i> تسجيل الدخول
                </button>
            </form>
            
            <div class="mt-4 p-3 bg-light rounded">
                <small class="text-muted">
                    <strong>بيانات تجريبية:</strong><br>
                    البريد: <EMAIL><br>
                    كلمة المرور: admin123
                </small>
            </div>
        </div>
    </div>

<?php else: ?>
    <!-- Admin Dashboard -->
    <div class="admin-container">
        <div class="row g-0">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar p-3">
                    <div class="text-center mb-4">
                        <h4>🚀 حلاق على بابك</h4>
                        <small>مرحباً، <?php echo $_SESSION['admin_email']; ?></small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#dashboard">
                            <i class="mdi mdi-view-dashboard"></i> لوحة التحكم
                        </a>
                        <a class="nav-link" href="#bookings">
                            <i class="mdi mdi-calendar-check"></i> الحجوزات
                        </a>
                        <a class="nav-link" href="#barbers">
                            <i class="mdi mdi-account-group"></i> الحلاقين
                        </a>
                        <a class="nav-link" href="#customers">
                            <i class="mdi mdi-account-multiple"></i> العملاء
                        </a>
                        <a class="nav-link" href="#services">
                            <i class="mdi mdi-scissors-cutting"></i> الخدمات
                        </a>
                        <a class="nav-link" href="#reports">
                            <i class="mdi mdi-chart-line"></i> التقارير
                        </a>
                        <a class="nav-link" href="#settings">
                            <i class="mdi mdi-cog"></i> الإعدادات
                        </a>
                        <hr class="my-3">
                        <a class="nav-link" href="?logout=1">
                            <i class="mdi mdi-logout"></i> تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Content Area -->
            <div class="col-md-9">
                <div class="content-area">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>لوحة التحكم الرئيسية</h2>
                        <span class="badge bg-success">متصل</span>
                    </div>
                    
                    <!-- Statistics Cards -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-card text-center">
                                <i class="mdi mdi-calendar-check display-4"></i>
                                <h3>156</h3>
                                <p>إجمالي الحجوزات</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card text-center">
                                <i class="mdi mdi-account-group display-4"></i>
                                <h3>23</h3>
                                <p>الحلاقين النشطين</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card text-center">
                                <i class="mdi mdi-account-multiple display-4"></i>
                                <h3>89</h3>
                                <p>العملاء المسجلين</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card text-center">
                                <i class="mdi mdi-cash-multiple display-4"></i>
                                <h3>12,450</h3>
                                <p>الإيرادات (ريال)</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Activity -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="mdi mdi-clock-outline"></i> آخر الحجوزات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <div class="list-group-item d-flex justify-content-between">
                                            <span>أحمد محمد - قص شعر</span>
                                            <small class="text-muted">منذ 5 دقائق</small>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between">
                                            <span>سارة أحمد - تصفيف شعر</span>
                                            <small class="text-muted">منذ 15 دقيقة</small>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between">
                                            <span>محمد علي - حلاقة كاملة</span>
                                            <small class="text-muted">منذ 30 دقيقة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="mdi mdi-chart-line"></i> إحصائيات سريعة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>معدل الحجوزات اليومية</span>
                                            <strong>85%</strong>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 85%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>رضا العملاء</span>
                                            <strong>92%</strong>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" style="width: 92%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>الحلاقين المتاحين</span>
                                            <strong>78%</strong>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-info" style="width: 78%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="mdi mdi-lightning-bolt"></i> إجراءات سريعة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <button class="btn btn-primary w-100 mb-2">
                                                <i class="mdi mdi-plus"></i> حجز جديد
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-success w-100 mb-2">
                                                <i class="mdi mdi-account-plus"></i> إضافة حلاق
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-info w-100 mb-2">
                                                <i class="mdi mdi-file-chart"></i> تقرير يومي
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-warning w-100 mb-2">
                                                <i class="mdi mdi-bell"></i> إشعارات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
