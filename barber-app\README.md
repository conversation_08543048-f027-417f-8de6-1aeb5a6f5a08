# تطبيق الحلاقين المحترفين - حلاق على بابك ✂️

تطبيق Flutter للحلاقين المحترفين في منصة "حلاق على بابك" - إدارة شاملة للطلبات والأرباح والجدولة.

## 🌟 المزايا الرئيسية

### 📊 **لوحة تحكم ديناميكية**
- إحصائيات شاملة للأداء اليومي
- مراقبة الأرباح في الوقت الفعلي
- تتبع التقييمات وآراء العملاء
- إشعارات فورية للطلبات الجديدة

### 📅 **إدارة الحجوزات المتقدمة**
- استقبال طلبات الحجز الفورية
- قبول أو رفض الطلبات بنقرة واحدة
- تتبع حالة كل حجز
- إدارة الجدول الزمني بذكاء

### 💰 **نظام الأرباح الشامل**
- تتبع الأرباح اليومية والشهرية
- طلبات السحب السريعة
- تقارير مالية مفصلة
- حساب العمولات تلقائياً

### ⏰ **إدارة الجدول الذكية**
- تحديد أوقات العمل المرنة
- إدارة فترات الراحة
- تحديث حالة التوفر فورياً
- منع التداخل في المواعيد

### ⭐ **نظام التقييم والجودة**
- مراقبة التقييمات المستمرة
- تحليل آراء العملاء
- تحسين الخدمة المستمر
- برنامج الحلاق المميز

## 🏗️ البنية التقنية

### 📱 **Frontend (Flutter)**
```
lib/
├── core/                    # الأساسيات
│   ├── constants/          # الثوابت
│   ├── theme/              # الألوان والخطوط
│   ├── network/            # شبكة الاتصال
│   └── utils/              # الأدوات المساعدة
├── features/               # المزايا
│   ├── auth/               # المصادقة
│   ├── dashboard/          # لوحة التحكم
│   ├── bookings/           # إدارة الحجوزات
│   ├── earnings/           # الأرباح
│   ├── schedule/           # الجدولة
│   ├── services/           # الخدمات
│   ├── analytics/          # التحليلات
│   └── profile/            # الملف الشخصي
└── shared/                 # المشترك
    ├── widgets/            # العناصر المشتركة
    ├── models/             # نماذج البيانات
    └── services/           # الخدمات
```

### 🔧 **التقنيات المستخدمة**
- **Flutter 3.x** - إطار العمل الأساسي
- **GetX** - إدارة الحالة والتنقل
- **Provider** - إدارة الحالة المحلية
- **Dio** - طلبات HTTP
- **Hive** - قاعدة بيانات محلية
- **Firebase** - الإشعارات والتحليلات
- **Google Maps** - الخرائط والمواقع
- **FL Chart** - الرسوم البيانية
- **WorkManager** - المهام الخلفية

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
```bash
# Flutter SDK 3.x
flutter --version

# Android Studio / VS Code
# Xcode (للـ iOS)
```

### خطوات التثبيت
```bash
# 1. استنساخ المشروع
git clone https://github.com/barber-app/barber-app.git
cd barber-app

# 2. تثبيت Dependencies
flutter pub get

# 3. تشغيل Code Generation
flutter packages pub run build_runner build

# 4. تشغيل التطبيق
flutter run
```

### إعداد Firebase
```bash
# 1. إنشاء مشروع Firebase
# 2. تحميل google-services.json (Android)
# 3. تحميل GoogleService-Info.plist (iOS)
# 4. تفعيل Cloud Messaging و Analytics
```

## 📱 الشاشات الرئيسية

### 🏠 **لوحة التحكم**
- **Dashboard** - نظرة عامة شاملة
- **Statistics** - إحصائيات الأداء
- **Quick Actions** - إجراءات سريعة
- **Status Toggle** - تغيير حالة التوفر

### 📅 **إدارة الحجوزات**
- **Booking Requests** - طلبات جديدة
- **Active Bookings** - الحجوزات النشطة
- **Booking History** - تاريخ الحجوزات
- **Booking Details** - تفاصيل كل حجز

### 💰 **الأرباح والمالية**
- **Earnings Overview** - نظرة عامة على الأرباح
- **Daily/Monthly Reports** - تقارير مفصلة
- **Withdrawal Requests** - طلبات السحب
- **Transaction History** - تاريخ المعاملات

### ⏰ **إدارة الجدول**
- **Schedule View** - عرض الجدول
- **Availability Settings** - إعدادات التوفر
- **Time Slots** - إدارة الفترات الزمنية
- **Break Management** - إدارة فترات الراحة

## 🔧 الإعدادات

### ملف البيئة (.env)
```env
# API Configuration
API_BASE_URL=https://api.barber-app.com/v1
API_TIMEOUT=30

# Firebase
FIREBASE_PROJECT_ID=barber-app-prod
FIREBASE_API_KEY=your_api_key

# Google Maps
GOOGLE_MAPS_API_KEY=your_maps_key

# Background Tasks
ENABLE_BACKGROUND_SYNC=true
SYNC_INTERVAL_MINUTES=15

# App Configuration
APP_NAME=حلاق على بابك - المحترفين
APP_VERSION=1.0.0
```

### إعدادات المهام الخلفية
```yaml
# android/app/src/main/AndroidManifest.xml
<service
    android:name="be.tramckrijte.workmanager.BackgroundService"
    android:exported="false" />
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
flutter test

# اختبارات التكامل
flutter test integration_test/

# اختبار الأداء
flutter test --coverage
```

## 📦 البناء للإنتاج

### Android
```bash
# بناء APK
flutter build apk --release

# بناء App Bundle
flutter build appbundle --release
```

### iOS
```bash
# بناء للـ iOS
flutter build ios --release

# أرشفة للـ App Store
flutter build ipa
```

## 🔄 المهام الخلفية

### المهام المجدولة
- **مزامنة الحجوزات** - كل 15 دقيقة
- **فحص الطلبات الجديدة** - كل 5 دقائق
- **تحديث الموقع** - كل 10 دقائق
- **مزامنة الأرباح** - كل ساعة

### إدارة الإشعارات
```dart
// تفعيل الإشعارات
await NotificationService.initialize();

// إشعار حجز جديد
await NotificationService.showBookingNotification(booking);
```

## 📊 التحليلات والتقارير

### مؤشرات الأداء الرئيسية
- معدل قبول الحجوزات
- متوسط التقييمات
- الأرباح اليومية/الشهرية
- عدد العملاء المخدومين

### التقارير المتاحة
- تقرير الأداء الشهري
- تحليل أوقات الذروة
- إحصائيات الخدمات الأكثر طلباً
- تقييم رضا العملاء

## 🔐 الأمان والخصوصية

### حماية البيانات
- تشفير جميع البيانات الحساسة
- مصادقة ثنائية العامل
- تسجيل دخول آمن بـ JWT
- حماية من الهجمات الشائعة

### الخصوصية
- عدم مشاركة بيانات العملاء
- حذف البيانات عند الطلب
- شفافية في استخدام البيانات
- امتثال لقوانين حماية البيانات

## 🆘 استكشاف الأخطاء

### مشاكل شائعة
```bash
# مسح الـ Cache
flutter clean
flutter pub get

# إعادة بناء
flutter packages pub run build_runner build --delete-conflicting-outputs

# فحص المشاكل
flutter doctor
```

### مشاكل المهام الخلفية
```bash
# فحص حالة WorkManager
adb shell dumpsys jobscheduler

# إعادة تشغيل الخدمة
flutter run --release
```

## 📞 الدعم والمساعدة

- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +201234567890
- 💬 **الدردشة المباشرة**: متاحة في التطبيق
- 📖 **التوثيق**: [رابط التوثيق]

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**تم تطويره بـ ❤️ بواسطة فريق حلاق على بابك**
