import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../../core/constants/app_constants.dart';
import '../../core/network/api_client.dart';
import '../../core/network/api_endpoints.dart';
import '../models/barber_model.dart';
import '../models/service_model.dart';
import '../models/recommendation_model.dart';
import 'cache_service.dart';
import 'storage_service.dart';

abstract class AIRecommendationService {
  Future<void> initialize();
  Future<List<BarberModel>> getRecommendedBarbers({
    String? serviceId,
    double? latitude,
    double? longitude,
    int limit = 10,
  });
  Future<List<ServiceModel>> getRecommendedServices({
    String? barberId,
    int limit = 10,
  });
  Future<List<RecommendationModel>> getPersonalizedRecommendations({
    int limit = 20,
  });
  Future<BarberModel?> getBestMatchBarber({
    required String serviceId,
    required DateTime preferredDate,
    required String preferredTime,
    double? latitude,
    double? longitude,
  });
  Future<List<String>> getOptimalTimeSlots({
    required String barberId,
    required String serviceId,
    required DateTime date,
  });
  Future<double> predictBookingSuccess({
    required String barberId,
    required String serviceId,
    required DateTime date,
    required String timeSlot,
  });
  Future<void> recordUserInteraction({
    required String type,
    required String itemId,
    Map<String, dynamic>? metadata,
  });
  Future<void> updateUserPreferences(Map<String, dynamic> preferences);
}

class AIRecommendationServiceImpl implements AIRecommendationService {
  final ApiClient _apiClient;
  final CacheService _cacheService;
  final StorageService _storageService;
  
  Map<String, dynamic> _userProfile = {};
  List<String> _userInteractions = [];
  bool _isInitialized = false;

  AIRecommendationServiceImpl({
    required ApiClient apiClient,
    required CacheService cacheService,
    required StorageService storageService,
  }) : _apiClient = apiClient,
       _cacheService = cacheService,
       _storageService = storageService;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Load user profile and preferences
      await _loadUserProfile();
      
      // Load cached interactions
      await _loadUserInteractions();
      
      // Sync with server
      await _syncUserData();
      
      _isInitialized = true;
    } catch (e) {
      // Handle initialization error
    }
  }

  Future<void> _loadUserProfile() async {
    final profileJson = await _storageService.getString('ai_user_profile');
    if (profileJson != null) {
      try {
        _userProfile = Map<String, dynamic>.from(profileJson);
      } catch (e) {
        _userProfile = {};
      }
    }
  }

  Future<void> _loadUserInteractions() async {
    final interactionsJson = await _storageService.getStringList('ai_user_interactions');
    if (interactionsJson != null) {
      _userInteractions = interactionsJson;
    }
  }

  Future<void> _syncUserData() async {
    try {
      // Send user interactions to server for analysis
      if (_userInteractions.isNotEmpty) {
        await _apiClient.post(
          ApiEndpoints.syncUserInteractions,
          data: {
            'interactions': _userInteractions,
            'profile': _userProfile,
          },
        );
        
        // Clear synced interactions
        _userInteractions.clear();
        await _storageService.setStringList('ai_user_interactions', _userInteractions);
      }
    } catch (e) {
      // Handle sync error
    }
  }

  @override
  Future<List<BarberModel>> getRecommendedBarbers({
    String? serviceId,
    double? latitude,
    double? longitude,
    int limit = 10,
  }) async {
    try {
      final cacheKey = 'recommended_barbers_${serviceId ?? 'all'}_${latitude?.toStringAsFixed(3)}_${longitude?.toStringAsFixed(3)}';
      
      // Check cache first
      final cachedBarbers = await _cacheService.get(cacheKey);
      if (cachedBarbers != null) {
        return (cachedBarbers as List)
            .map((json) => BarberModel.fromJson(json))
            .toList();
      }

      final response = await _apiClient.post(
        ApiEndpoints.recommendedBarbers,
        data: {
          if (serviceId != null) 'service_id': serviceId,
          if (latitude != null) 'latitude': latitude,
          if (longitude != null) 'longitude': longitude,
          'limit': limit,
          'user_profile': _userProfile,
          'preferences': await _getUserPreferences(),
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> barbersJson = response.data['data'];
        final barbers = barbersJson.map((json) => BarberModel.fromJson(json)).toList();
        
        // Cache for 1 hour
        await _cacheService.set(cacheKey, barbersJson, 
            duration: const Duration(hours: 1));
        
        return barbers;
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<ServiceModel>> getRecommendedServices({
    String? barberId,
    int limit = 10,
  }) async {
    try {
      final cacheKey = 'recommended_services_${barberId ?? 'all'}';
      
      // Check cache first
      final cachedServices = await _cacheService.get(cacheKey);
      if (cachedServices != null) {
        return (cachedServices as List)
            .map((json) => ServiceModel.fromJson(json))
            .toList();
      }

      final response = await _apiClient.post(
        ApiEndpoints.recommendedServices,
        data: {
          if (barberId != null) 'barber_id': barberId,
          'limit': limit,
          'user_profile': _userProfile,
          'booking_history': await _getBookingHistory(),
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> servicesJson = response.data['data'];
        final services = servicesJson.map((json) => ServiceModel.fromJson(json)).toList();
        
        // Cache for 2 hours
        await _cacheService.set(cacheKey, servicesJson, 
            duration: const Duration(hours: 2));
        
        return services;
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<RecommendationModel>> getPersonalizedRecommendations({
    int limit = 20,
  }) async {
    try {
      final cacheKey = 'personalized_recommendations';
      
      // Check cache first
      final cachedRecommendations = await _cacheService.get(cacheKey);
      if (cachedRecommendations != null) {
        return (cachedRecommendations as List)
            .map((json) => RecommendationModel.fromJson(json))
            .toList();
      }

      final response = await _apiClient.post(
        ApiEndpoints.personalizedRecommendations,
        data: {
          'limit': limit,
          'user_profile': _userProfile,
          'preferences': await _getUserPreferences(),
          'location': await _getCurrentLocation(),
          'time_preferences': await _getTimePreferences(),
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> recommendationsJson = response.data['data'];
        final recommendations = recommendationsJson
            .map((json) => RecommendationModel.fromJson(json))
            .toList();
        
        // Cache for 30 minutes
        await _cacheService.set(cacheKey, recommendationsJson, 
            duration: const Duration(minutes: 30));
        
        return recommendations;
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<BarberModel?> getBestMatchBarber({
    required String serviceId,
    required DateTime preferredDate,
    required String preferredTime,
    double? latitude,
    double? longitude,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.bestMatchBarber,
        data: {
          'service_id': serviceId,
          'preferred_date': preferredDate.toIso8601String().split('T')[0],
          'preferred_time': preferredTime,
          if (latitude != null) 'latitude': latitude,
          if (longitude != null) 'longitude': longitude,
          'user_profile': _userProfile,
          'preferences': await _getUserPreferences(),
          'booking_history': await _getBookingHistory(),
        },
      );

      if (response.statusCode == 200) {
        final barberData = response.data['data'];
        return BarberModel.fromJson(barberData);
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<String>> getOptimalTimeSlots({
    required String barberId,
    required String serviceId,
    required DateTime date,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.optimalTimeSlots,
        data: {
          'barber_id': barberId,
          'service_id': serviceId,
          'date': date.toIso8601String().split('T')[0],
          'user_profile': _userProfile,
          'preferences': await _getUserPreferences(),
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> slotsJson = response.data['data'];
        return slotsJson.map((slot) => slot.toString()).toList();
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<double> predictBookingSuccess({
    required String barberId,
    required String serviceId,
    required DateTime date,
    required String timeSlot,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.predictBookingSuccess,
        data: {
          'barber_id': barberId,
          'service_id': serviceId,
          'date': date.toIso8601String().split('T')[0],
          'time_slot': timeSlot,
          'user_profile': _userProfile,
        },
      );

      if (response.statusCode == 200) {
        return (response.data['data']['success_probability'] as num).toDouble();
      }

      return 0.5; // Default probability
    } catch (e) {
      return 0.5;
    }
  }

  @override
  Future<void> recordUserInteraction({
    required String type,
    required String itemId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final interaction = {
        'type': type,
        'item_id': itemId,
        'timestamp': DateTime.now().toIso8601String(),
        if (metadata != null) 'metadata': metadata,
      };

      _userInteractions.add(interaction.toString());
      
      // Store locally
      await _storageService.setStringList('ai_user_interactions', _userInteractions);
      
      // Send to server immediately for important interactions
      if (['booking_created', 'barber_rated', 'service_completed'].contains(type)) {
        await _apiClient.post(
          ApiEndpoints.recordInteraction,
          data: interaction,
        );
      }
    } catch (e) {
      // Handle interaction recording error
    }
  }

  @override
  Future<void> updateUserPreferences(Map<String, dynamic> preferences) async {
    try {
      _userProfile.addAll(preferences);
      
      // Store locally
      await _storageService.setString('ai_user_profile', _userProfile);
      
      // Send to server
      await _apiClient.post(
        ApiEndpoints.updateUserPreferences,
        data: {
          'preferences': preferences,
          'profile': _userProfile,
        },
      );
    } catch (e) {
      // Handle preference update error
    }
  }

  Future<Map<String, dynamic>> _getUserPreferences() async {
    return {
      'preferred_time_slots': _userProfile['preferred_time_slots'] ?? [],
      'preferred_barber_types': _userProfile['preferred_barber_types'] ?? [],
      'price_sensitivity': _userProfile['price_sensitivity'] ?? 'medium',
      'distance_preference': _userProfile['distance_preference'] ?? 'nearby',
      'rating_importance': _userProfile['rating_importance'] ?? 'high',
      'availability_importance': _userProfile['availability_importance'] ?? 'high',
    };
  }

  Future<Map<String, dynamic>?> _getCurrentLocation() async {
    final latitude = await _storageService.getDouble('current_latitude');
    final longitude = await _storageService.getDouble('current_longitude');
    
    if (latitude != null && longitude != null) {
      return {
        'latitude': latitude,
        'longitude': longitude,
      };
    }
    
    return null;
  }

  Future<Map<String, dynamic>> _getTimePreferences() async {
    return {
      'preferred_days': _userProfile['preferred_days'] ?? ['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday'],
      'preferred_times': _userProfile['preferred_times'] ?? ['morning', 'afternoon'],
      'avoid_rush_hours': _userProfile['avoid_rush_hours'] ?? true,
    };
  }

  Future<List<Map<String, dynamic>>> _getBookingHistory() async {
    // This would typically come from the booking service
    // For now, return cached data
    final historyJson = await _cacheService.get('booking_history_summary');
    if (historyJson != null) {
      return List<Map<String, dynamic>>.from(historyJson);
    }
    
    return [];
  }
}
