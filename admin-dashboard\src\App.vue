<template>
  <v-app>
    <!-- Navigation Drawer -->
    <v-navigation-drawer
      v-model="drawer"
      :rail="rail"
      permanent
      class="elevation-2"
      color="primary"
      theme="dark"
    >
      <!-- Logo Section -->
      <v-list-item
        prepend-icon="mdi-scissors-cutting"
        :title="rail ? '' : 'حلاق على بابك'"
        subtitle="لوحة التحكم"
        class="mb-4"
      >
        <template v-slot:append>
          <v-btn
            variant="text"
            icon="mdi-chevron-right"
            @click.stop="rail = !rail"
            size="small"
          ></v-btn>
        </template>
      </v-list-item>

      <v-divider></v-divider>

      <!-- Navigation Menu -->
      <v-list density="compact" nav>
        <v-list-item
          v-for="item in menuItems"
          :key="item.title"
          :prepend-icon="item.icon"
          :title="item.title"
          :to="item.to"
          :value="item.value"
          color="white"
        >
          <v-badge
            v-if="item.badge"
            :content="item.badge"
            color="error"
            inline
          ></v-badge>
        </v-list-item>
      </v-list>

      <!-- Settings Section -->
      <template v-slot:append>
        <v-divider></v-divider>
        <v-list density="compact" nav>
          <v-list-item
            prepend-icon="mdi-cog"
            title="الإعدادات العامة"
            to="/settings"
            color="white"
          ></v-list-item>
          <v-list-item
            prepend-icon="mdi-logout"
            title="تسجيل الخروج"
            @click="logout"
            color="white"
          ></v-list-item>
        </v-list>
      </template>
    </v-navigation-drawer>

    <!-- App Bar -->
    <v-app-bar
      :order="-1"
      color="white"
      elevation="1"
      height="64"
    >
      <v-app-bar-nav-icon
        @click="drawer = !drawer"
        v-if="$vuetify.display.mobile"
      ></v-app-bar-nav-icon>

      <v-toolbar-title class="text-h6 font-weight-bold">
        {{ currentPageTitle }}
      </v-toolbar-title>

      <v-spacer></v-spacer>

      <!-- Notifications -->
      <v-btn icon size="small" class="me-2">
        <v-badge
          :content="notificationCount"
          :value="notificationCount"
          color="error"
        >
          <v-icon>mdi-bell</v-icon>
        </v-badge>
      </v-btn>

      <!-- User Menu -->
      <v-menu>
        <template v-slot:activator="{ props }">
          <v-btn
            v-bind="props"
            variant="text"
            class="text-none"
          >
            <v-avatar size="32" class="me-2">
              <v-img
                :src="user.avatar || '/default-avatar.png'"
                :alt="user.name"
              ></v-img>
            </v-avatar>
            <span class="hidden-sm-and-down">{{ user.name }}</span>
            <v-icon>mdi-chevron-down</v-icon>
          </v-btn>
        </template>

        <v-list>
          <v-list-item
            prepend-icon="mdi-account"
            title="الملف الشخصي"
            @click="goToProfile"
          ></v-list-item>
          <v-list-item
            prepend-icon="mdi-cog"
            title="الإعدادات"
            @click="goToSettings"
          ></v-list-item>
          <v-divider></v-divider>
          <v-list-item
            prepend-icon="mdi-logout"
            title="تسجيل الخروج"
            @click="logout"
          ></v-list-item>
        </v-list>
      </v-menu>
    </v-app-bar>

    <!-- Main Content -->
    <v-main>
      <v-container fluid class="pa-4">
        <router-view />
      </v-container>
    </v-main>

    <!-- Loading Overlay -->
    <v-overlay
      v-model="loading"
      class="align-center justify-center"
      persistent
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      ></v-progress-circular>
    </v-overlay>
  </v-app>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'

export default {
  name: 'App',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const authStore = useAuthStore()
    const appStore = useAppStore()

    const drawer = ref(true)
    const rail = ref(false)

    const menuItems = ref([
      {
        title: 'لوحة المعلومات',
        icon: 'mdi-view-dashboard',
        to: '/',
        value: 'dashboard'
      },
      {
        title: 'إدارة المستخدمين',
        icon: 'mdi-account-group',
        to: '/users',
        value: 'users'
      },
      {
        title: 'إدارة الحلاقين',
        icon: 'mdi-account-tie',
        to: '/barbers',
        value: 'barbers',
        badge: 5 // عدد الحلاقين في انتظار التوثيق
      },
      {
        title: 'إدارة الحجوزات',
        icon: 'mdi-calendar-check',
        to: '/bookings',
        value: 'bookings'
      },
      {
        title: 'الإدارة المالية',
        icon: 'mdi-cash-multiple',
        to: '/finance',
        value: 'finance'
      },
      {
        title: 'إدارة المتجر',
        icon: 'mdi-store',
        to: '/store',
        value: 'store'
      },
      {
        title: 'التقارير والإحصائيات',
        icon: 'mdi-chart-line',
        to: '/analytics',
        value: 'analytics'
      },
      {
        title: 'الإعدادات الديناميكية',
        icon: 'mdi-tune',
        to: '/dynamic-settings',
        value: 'dynamic-settings'
      }
    ])

    const currentPageTitle = computed(() => {
      const item = menuItems.value.find(item => item.to === route.path)
      return item ? item.title : 'لوحة التحكم'
    })

    const user = computed(() => authStore.user)
    const loading = computed(() => appStore.loading)
    const notificationCount = computed(() => appStore.notificationCount)

    const logout = async () => {
      try {
        await authStore.logout()
        router.push('/login')
      } catch (error) {
        console.error('Logout error:', error)
      }
    }

    const goToProfile = () => {
      router.push('/profile')
    }

    const goToSettings = () => {
      router.push('/settings')
    }

    onMounted(() => {
      // Initialize app data
      appStore.initializeApp()
    })

    return {
      drawer,
      rail,
      menuItems,
      currentPageTitle,
      user,
      loading,
      notificationCount,
      logout,
      goToProfile,
      goToSettings
    }
  }
}
</script>

<style scoped>
.v-navigation-drawer {
  border-left: none !important;
}

.v-app-bar {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}
</style>
