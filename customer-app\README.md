# تطبيق العملاء - حلاق على بابك 💇‍♂️

تطبيق Flutter للعملاء في منصة "حلاق على بابك" - خدمة الحلاقة المنزلية الأولى في مصر.

## 🌟 المزايا الرئيسية

### 🏠 **الحجز المنزلي**
- حجز خدمات الحلاقة في المنزل
- اختيار الحلاق المناسب
- تحديد الوقت والتاريخ المناسب
- تتبع الحلاق في الوقت الفعلي

### 👑 **خدمات VIP**
- حلاقين مختارين ومعتمدين
- أدوات معقمة وحصرية
- خصومات وعروض خاصة
- أولوية في الحجز

### 🛒 **المتجر الإلكتروني**
- منتجات العناية بالشعر
- أدوات الحلاقة المنزلية
- عطور ومستحضرات التجميل
- توصيل سريع للمنزل

### 💳 **نظام الدفع المرن**
- دفع كاش عند الوصول
- دفع إلكتروني آمن
- محفظة إلكترونية
- نظام النقاط والولاء

### ⭐ **نظام التقييم**
- تقييم الحلاقين
- مراجعات العملاء
- ضمان الجودة
- خدمة عملاء 24/7

## 🏗️ البنية التقنية

### 📱 **Frontend (Flutter)**
```
lib/
├── core/                    # الأساسيات
│   ├── constants/          # الثوابت
│   ├── theme/              # الألوان والخطوط
│   ├── network/            # شبكة الاتصال
│   └── utils/              # الأدوات المساعدة
├── features/               # المزايا
│   ├── auth/               # المصادقة
│   ├── home/               # الصفحة الرئيسية
│   ├── booking/            # الحجز
│   ├── profile/            # الملف الشخصي
│   ├── vip/                # خدمات VIP
│   ├── store/              # المتجر
│   ├── wallet/             # المحفظة
│   └── loyalty/            # نظام الولاء
└── shared/                 # المشترك
    ├── widgets/            # العناصر المشتركة
    ├── models/             # نماذج البيانات
    └── services/           # الخدمات
```

### 🔧 **التقنيات المستخدمة**
- **Flutter 3.x** - إطار العمل الأساسي
- **GetX** - إدارة الحالة والتنقل
- **Provider** - إدارة الحالة المحلية
- **Dio** - طلبات HTTP
- **Hive** - قاعدة بيانات محلية
- **Firebase** - الإشعارات والتحليلات
- **Google Maps** - الخرائط والمواقع
- **Stripe/Paymob** - بوابات الدفع

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
```bash
# Flutter SDK 3.x
flutter --version

# Android Studio / VS Code
# Xcode (للـ iOS)
```

### خطوات التثبيت
```bash
# 1. استنساخ المشروع
git clone https://github.com/barber-app/customer-app.git
cd customer-app

# 2. تثبيت Dependencies
flutter pub get

# 3. تشغيل Code Generation
flutter packages pub run build_runner build

# 4. تشغيل التطبيق
flutter run
```

### إعداد Firebase
```bash
# 1. إنشاء مشروع Firebase
# 2. تحميل google-services.json (Android)
# 3. تحميل GoogleService-Info.plist (iOS)
# 4. تفعيل Authentication و Cloud Messaging
```

### إعداد Google Maps
```bash
# 1. الحصول على API Key من Google Cloud Console
# 2. إضافة المفتاح في android/app/src/main/AndroidManifest.xml
# 3. إضافة المفتاح في ios/Runner/AppDelegate.swift
```

## 📱 الشاشات الرئيسية

### 🔐 **المصادقة**
- **Splash Screen** - شاشة البداية
- **Onboarding** - التعريف بالتطبيق
- **Login** - تسجيل الدخول
- **Register** - إنشاء حساب جديد
- **Verify Phone** - تأكيد رقم الهاتف

### 🏠 **الصفحة الرئيسية**
- **Home** - الصفحة الرئيسية
- **Search** - البحث عن الحلاقين
- **Categories** - فئات الخدمات
- **Offers** - العروض والخصومات

### 📅 **الحجز**
- **Service Selection** - اختيار الخدمة
- **Barber Selection** - اختيار الحلاق
- **Date & Time** - تحديد الموعد
- **Location** - تحديد العنوان
- **Payment** - الدفع
- **Confirmation** - تأكيد الحجز

### 👤 **الملف الشخصي**
- **Profile** - الملف الشخصي
- **Edit Profile** - تعديل البيانات
- **Addresses** - إدارة العناوين
- **Booking History** - تاريخ الحجوزات
- **Settings** - الإعدادات

## 🔧 الإعدادات

### ملف البيئة (.env)
```env
# API Configuration
API_BASE_URL=https://api.barber-app.com/v1
API_TIMEOUT=30

# Firebase
FIREBASE_PROJECT_ID=barber-app-prod
FIREBASE_API_KEY=your_api_key

# Google Maps
GOOGLE_MAPS_API_KEY=your_maps_key

# Payment Gateways
PAYMOB_API_KEY=your_paymob_key
STRIPE_PUBLISHABLE_KEY=your_stripe_key

# App Configuration
APP_NAME=حلاق على بابك
APP_VERSION=1.0.0
```

### إعدادات الإنتاج
```yaml
# pubspec.yaml
flutter:
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - .env
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
flutter test

# اختبارات التكامل
flutter test integration_test/

# اختبار الأداء
flutter test --coverage
```

### أنواع الاختبارات
- **Unit Tests** - اختبار الوظائف المنفردة
- **Widget Tests** - اختبار واجهة المستخدم
- **Integration Tests** - اختبار التدفق الكامل

## 📦 البناء للإنتاج

### Android
```bash
# بناء APK
flutter build apk --release

# بناء App Bundle
flutter build appbundle --release
```

### iOS
```bash
# بناء للـ iOS
flutter build ios --release

# أرشفة للـ App Store
flutter build ipa
```

## 🔄 التحديثات

### إدارة الإصدارات
```yaml
# pubspec.yaml
version: 1.0.0+1
```

### نشر التحديثات
```bash
# تحديث رقم الإصدار
flutter pub version patch

# بناء ونشر
flutter build appbundle --release
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة
```bash
# مسح الـ Cache
flutter clean
flutter pub get

# إعادة بناء
flutter packages pub run build_runner build --delete-conflicting-outputs

# فحص المشاكل
flutter doctor
```

## 📞 الدعم والمساعدة

- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +201234567890
- 💬 **Discord**: [رابط الخادم]
- 📖 **التوثيق**: [رابط التوثيق]

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**تم تطويره بـ ❤️ بواسطة فريق حلاق على بابك**
