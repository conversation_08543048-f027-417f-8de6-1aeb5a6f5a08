import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';

class LiveStreamingService {
  static const String _appId = 'YOUR_AGORA_APP_ID';
  
  RtcEngine? _engine;
  bool _isJoined = false;
  bool _isStreaming = false;
  String? _currentChannel;
  
  // Stream controllers for UI updates
  final StreamController<bool> _streamStateController = StreamController<bool>.broadcast();
  final StreamController<Map<String, dynamic>> _streamEventController = StreamController<Map<String, dynamic>>.broadcast();
  
  Stream<bool> get streamState => _streamStateController.stream;
  Stream<Map<String, dynamic>> get streamEvents => _streamEventController.stream;

  /// Initialize Agora engine
  Future<void> initialize() async {
    try {
      // Request permissions
      await _requestPermissions();
      
      // Create RTC engine
      _engine = createAgoraRtcEngine();
      
      await _engine!.initialize(RtcEngineContext(
        appId: _appId,
        channelProfile: ChannelProfileType.channelProfileCommunication,
      ));
      
      // Set up event handlers
      _setupEventHandlers();
      
      debugPrint('Agora engine initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize Agora engine: $e');
      throw Exception('Failed to initialize live streaming: $e');
    }
  }

  /// Request necessary permissions
  Future<void> _requestPermissions() async {
    await [
      Permission.camera,
      Permission.microphone,
    ].request();
  }

  /// Set up Agora event handlers
  void _setupEventHandlers() {
    _engine!.registerEventHandler(
      RtcEngineEventHandler(
        onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
          debugPrint('Successfully joined channel: ${connection.channelId}');
          _isJoined = true;
          _streamStateController.add(true);
          _streamEventController.add({
            'type': 'joined',
            'channel': connection.channelId,
            'elapsed': elapsed,
          });
        },
        onLeaveChannel: (RtcConnection connection, RtcStats stats) {
          debugPrint('Left channel: ${connection.channelId}');
          _isJoined = false;
          _isStreaming = false;
          _currentChannel = null;
          _streamStateController.add(false);
          _streamEventController.add({
            'type': 'left',
            'channel': connection.channelId,
            'stats': stats,
          });
        },
        onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
          debugPrint('Remote user joined: $remoteUid');
          _streamEventController.add({
            'type': 'user_joined',
            'uid': remoteUid,
            'elapsed': elapsed,
          });
        },
        onUserOffline: (RtcConnection connection, int remoteUid, UserOfflineReasonType reason) {
          debugPrint('Remote user offline: $remoteUid, reason: $reason');
          _streamEventController.add({
            'type': 'user_offline',
            'uid': remoteUid,
            'reason': reason,
          });
        },
        onTokenPrivilegeWillExpire: (RtcConnection connection, String token) {
          debugPrint('Token will expire, renewing...');
          _renewToken(connection.channelId!);
        },
        onError: (ErrorCodeType err, String msg) {
          debugPrint('Agora error: $err, message: $msg');
          _streamEventController.add({
            'type': 'error',
            'error': err,
            'message': msg,
          });
        },
        onConnectionStateChanged: (RtcConnection connection, ConnectionStateType state, ConnectionChangedReasonType reason) {
          debugPrint('Connection state changed: $state, reason: $reason');
          _streamEventController.add({
            'type': 'connection_state_changed',
            'state': state,
            'reason': reason,
          });
        },
        onNetworkQuality: (RtcConnection connection, int remoteUid, QualityType txQuality, QualityType rxQuality) {
          _streamEventController.add({
            'type': 'network_quality',
            'uid': remoteUid,
            'tx_quality': txQuality,
            'rx_quality': rxQuality,
          });
        },
      ),
    );
  }

  /// Start live streaming for a booking
  Future<Map<String, dynamic>> startLiveStream(int bookingId, {bool asPublisher = false}) async {
    try {
      if (_engine == null) {
        await initialize();
      }

      // Get stream token from backend
      final streamData = await _getStreamToken(bookingId, asPublisher);
      
      if (!streamData['success']) {
        throw Exception(streamData['error']);
      }

      final channelName = streamData['channel_name'];
      final token = streamData['token'];
      final uid = streamData['uid'];

      // Configure engine for streaming
      await _configureEngine(asPublisher);

      // Join channel
      await _engine!.joinChannel(
        token: token,
        channelId: channelName,
        uid: uid,
        options: ChannelMediaOptions(
          clientRoleType: asPublisher 
              ? ClientRoleType.clientRoleBroadcaster 
              : ClientRoleType.clientRoleAudience,
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
        ),
      );

      _currentChannel = channelName;
      _isStreaming = true;

      return {
        'success': true,
        'channel': channelName,
        'uid': uid,
        'role': asPublisher ? 'publisher' : 'subscriber',
      };

    } catch (e) {
      debugPrint('Failed to start live stream: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Stop live streaming
  Future<void> stopLiveStream() async {
    try {
      if (_engine != null && _isJoined) {
        await _engine!.leaveChannel();
      }
      
      // Notify backend about stream end
      if (_currentChannel != null) {
        await _notifyStreamEnd(_currentChannel!);
      }

      _isJoined = false;
      _isStreaming = false;
      _currentChannel = null;
      
    } catch (e) {
      debugPrint('Error stopping live stream: $e');
    }
  }

  /// Configure engine settings
  Future<void> _configureEngine(bool asPublisher) async {
    // Enable video
    await _engine!.enableVideo();
    
    if (asPublisher) {
      // Publisher settings
      await _engine!.enableAudio();
      await _engine!.setClientRole(role: ClientRoleType.clientRoleBroadcaster);
      
      // Video configuration
      await _engine!.setVideoEncoderConfiguration(
        const VideoEncoderConfiguration(
          dimensions: VideoDimensions(width: 640, height: 480),
          frameRate: 15,
          bitrate: 400,
          orientationMode: OrientationMode.orientationModeAdaptive,
        ),
      );
      
      // Audio configuration
      await _engine!.setAudioProfile(
        profile: AudioProfileType.audioProfileDefault,
        scenario: AudioScenarioType.audioScenarioDefault,
      );
      
    } else {
      // Subscriber settings
      await _engine!.setClientRole(role: ClientRoleType.clientRoleAudience);
    }
  }

  /// Get stream token from backend
  Future<Map<String, dynamic>> _getStreamToken(int bookingId, bool asPublisher) async {
    try {
      final dio = Dio();
      final response = await dio.post(
        '/api/live-stream/start',
        data: {
          'booking_id': bookingId,
          'role': asPublisher ? 'publisher' : 'subscriber',
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${await _getAuthToken()}',
          },
        ),
      );

      return response.data;
    } catch (e) {
      return {
        'success': false,
        'error': 'Failed to get stream token: $e',
      };
    }
  }

  /// Renew token when it's about to expire
  Future<void> _renewToken(String channelName) async {
    try {
      final dio = Dio();
      final response = await dio.post(
        '/api/live-stream/renew-token',
        data: {
          'channel_name': channelName,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${await _getAuthToken()}',
          },
        ),
      );

      if (response.data['success']) {
        final newToken = response.data['token'];
        await _engine!.renewToken(newToken);
      }
    } catch (e) {
      debugPrint('Failed to renew token: $e');
    }
  }

  /// Notify backend about stream end
  Future<void> _notifyStreamEnd(String channelName) async {
    try {
      final dio = Dio();
      await dio.post(
        '/api/live-stream/end',
        data: {
          'channel_name': channelName,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${await _getAuthToken()}',
          },
        ),
      );
    } catch (e) {
      debugPrint('Failed to notify stream end: $e');
    }
  }

  /// Toggle camera on/off
  Future<void> toggleCamera() async {
    if (_engine != null) {
      await _engine!.enableLocalVideo(!_isCameraEnabled);
    }
  }

  /// Toggle microphone on/off
  Future<void> toggleMicrophone() async {
    if (_engine != null) {
      await _engine!.enableLocalAudio(!_isMicrophoneEnabled);
    }
  }

  /// Switch camera (front/back)
  Future<void> switchCamera() async {
    if (_engine != null) {
      await _engine!.switchCamera();
    }
  }

  /// Adjust video quality
  Future<void> adjustVideoQuality(String quality) async {
    if (_engine == null) return;

    VideoEncoderConfiguration config;
    
    switch (quality) {
      case 'low':
        config = const VideoEncoderConfiguration(
          dimensions: VideoDimensions(width: 320, height: 240),
          frameRate: 10,
          bitrate: 200,
        );
        break;
      case 'medium':
        config = const VideoEncoderConfiguration(
          dimensions: VideoDimensions(width: 640, height: 480),
          frameRate: 15,
          bitrate: 400,
        );
        break;
      case 'high':
        config = const VideoEncoderConfiguration(
          dimensions: VideoDimensions(width: 1280, height: 720),
          frameRate: 20,
          bitrate: 800,
        );
        break;
      default:
        return;
    }

    await _engine!.setVideoEncoderConfiguration(config);
  }

  /// Get stream statistics
  Future<Map<String, dynamic>?> getStreamStatistics() async {
    if (_engine == null || !_isJoined) return null;

    try {
      final stats = await _engine!.getRtcStats();
      return {
        'duration': stats.duration,
        'tx_bytes': stats.txBytes,
        'rx_bytes': stats.rxBytes,
        'tx_audio_bytes': stats.txAudioBytes,
        'rx_audio_bytes': stats.rxAudioBytes,
        'tx_video_bytes': stats.txVideoBytes,
        'rx_video_bytes': stats.rxVideoBytes,
        'users': stats.users,
        'cpu_total_usage': stats.cpuTotalUsage,
        'cpu_app_usage': stats.cpuAppUsage,
      };
    } catch (e) {
      debugPrint('Failed to get stream statistics: $e');
      return null;
    }
  }

  /// Create video view widget
  Widget createVideoView({
    required int uid,
    bool isLocal = false,
  }) {
    if (isLocal) {
      return AgoraVideoView(
        controller: VideoViewController(
          rtcEngine: _engine!,
          canvas: const VideoCanvas(uid: 0),
        ),
      );
    } else {
      return AgoraVideoView(
        controller: VideoViewController.remote(
          rtcEngine: _engine!,
          canvas: VideoCanvas(uid: uid),
          connection: RtcConnection(channelId: _currentChannel),
        ),
      );
    }
  }

  /// Take screenshot of current stream
  Future<String?> takeScreenshot() async {
    try {
      if (_engine == null || !_isJoined) return null;

      // This would require additional implementation
      // to capture the current video frame
      return null;
    } catch (e) {
      debugPrint('Failed to take screenshot: $e');
      return null;
    }
  }

  /// Start/stop recording
  Future<bool> toggleRecording() async {
    try {
      // This would communicate with backend to start/stop cloud recording
      final dio = Dio();
      final response = await dio.post(
        '/api/live-stream/toggle-recording',
        data: {
          'channel_name': _currentChannel,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${await _getAuthToken()}',
          },
        ),
      );

      return response.data['success'] ?? false;
    } catch (e) {
      debugPrint('Failed to toggle recording: $e');
      return false;
    }
  }

  /// Get auth token (implement based on your auth system)
  Future<String> _getAuthToken() async {
    // Implementation depends on your authentication system
    return 'your_auth_token';
  }

  /// Check if camera is enabled
  bool get _isCameraEnabled {
    // This would track the current camera state
    return true; // Simplified
  }

  /// Check if microphone is enabled
  bool get _isMicrophoneEnabled {
    // This would track the current microphone state
    return true; // Simplified
  }

  /// Get current stream state
  bool get isStreaming => _isStreaming;
  bool get isJoined => _isJoined;
  String? get currentChannel => _currentChannel;

  /// Dispose resources
  void dispose() {
    _streamStateController.close();
    _streamEventController.close();
    _engine?.release();
  }
}

/// Live streaming widget for easy integration
class LiveStreamWidget extends StatefulWidget {
  final int bookingId;
  final bool isPublisher;
  final VoidCallback? onStreamStarted;
  final VoidCallback? onStreamEnded;
  final Function(String)? onError;

  const LiveStreamWidget({
    Key? key,
    required this.bookingId,
    this.isPublisher = false,
    this.onStreamStarted,
    this.onStreamEnded,
    this.onError,
  }) : super(key: key);

  @override
  State<LiveStreamWidget> createState() => _LiveStreamWidgetState();
}

class _LiveStreamWidgetState extends State<LiveStreamWidget> {
  final LiveStreamingService _streamingService = LiveStreamingService();
  bool _isLoading = false;
  bool _isStreaming = false;
  List<int> _remoteUsers = [];
  bool _isCameraEnabled = true;
  bool _isMicEnabled = true;

  @override
  void initState() {
    super.initState();
    _initializeStreaming();
  }

  Future<void> _initializeStreaming() async {
    try {
      await _streamingService.initialize();
      
      // Listen to stream events
      _streamingService.streamEvents.listen((event) {
        switch (event['type']) {
          case 'joined':
            setState(() {
              _isStreaming = true;
            });
            widget.onStreamStarted?.call();
            break;
          case 'left':
            setState(() {
              _isStreaming = false;
              _remoteUsers.clear();
            });
            widget.onStreamEnded?.call();
            break;
          case 'user_joined':
            setState(() {
              _remoteUsers.add(event['uid']);
            });
            break;
          case 'user_offline':
            setState(() {
              _remoteUsers.remove(event['uid']);
            });
            break;
          case 'error':
            widget.onError?.call(event['message']);
            break;
        }
      });
      
    } catch (e) {
      widget.onError?.call(e.toString());
    }
  }

  Future<void> _startStream() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _streamingService.startLiveStream(
        widget.bookingId,
        asPublisher: widget.isPublisher,
      );

      if (!result['success']) {
        widget.onError?.call(result['error']);
      }
    } catch (e) {
      widget.onError?.call(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _stopStream() async {
    await _streamingService.stopLiveStream();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          // Video views
          if (_isStreaming) ...[
            // Local video (if publisher)
            if (widget.isPublisher)
              Positioned(
                top: 10,
                right: 10,
                width: 120,
                height: 160,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: _streamingService.createVideoView(uid: 0, isLocal: true),
                ),
              ),
            
            // Remote video
            if (_remoteUsers.isNotEmpty)
              _streamingService.createVideoView(uid: _remoteUsers.first)
            else
              const Center(
                child: Text(
                  'في انتظار الاتصال...',
                  style: TextStyle(color: Colors.white),
                ),
              ),
          ] else ...[
            // Not streaming state
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.videocam_off,
                    size: 64,
                    color: Colors.white.withOpacity(0.7),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _isLoading ? 'جاري الاتصال...' : 'البث المباشر غير متاح',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Controls overlay
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  if (!_isStreaming)
                    ElevatedButton.icon(
                      onPressed: _isLoading ? null : _startStream,
                      icon: _isLoading 
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.play_arrow),
                      label: Text(_isLoading ? 'جاري الاتصال' : 'بدء البث'),
                    )
                  else ...[
                    // Camera toggle (publisher only)
                    if (widget.isPublisher)
                      IconButton(
                        onPressed: () {
                          _streamingService.toggleCamera();
                          setState(() {
                            _isCameraEnabled = !_isCameraEnabled;
                          });
                        },
                        icon: Icon(
                          _isCameraEnabled ? Icons.videocam : Icons.videocam_off,
                          color: Colors.white,
                        ),
                      ),
                    
                    // Microphone toggle (publisher only)
                    if (widget.isPublisher)
                      IconButton(
                        onPressed: () {
                          _streamingService.toggleMicrophone();
                          setState(() {
                            _isMicEnabled = !_isMicEnabled;
                          });
                        },
                        icon: Icon(
                          _isMicEnabled ? Icons.mic : Icons.mic_off,
                          color: Colors.white,
                        ),
                      ),
                    
                    // End call
                    IconButton(
                      onPressed: _stopStream,
                      icon: const Icon(Icons.call_end, color: Colors.red),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _streamingService.dispose();
    super.dispose();
  }
}
