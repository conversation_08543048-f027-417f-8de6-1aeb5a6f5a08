# Nginx Configuration for Barber App Development Environment

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
limit_req_zone $binary_remote_addr zone=auth:10m rate=10r/m;
limit_req_zone $binary_remote_addr zone=general:10m rate=200r/m;

# Upstream servers
upstream app_backend {
    server app:9000;
    keepalive 32;
}

# Main server block
server {
    listen 80;
    listen [::]:80;
    server_name localhost api.barber-app.local admin.barber-app.local;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
    
    # Development specific headers
    add_header X-Environment "development" always;
    add_header X-Debug-Mode "enabled" always;
    
    root /var/www/public;
    index index.php index.html index.htm;
    
    # Logging
    access_log /var/log/nginx/barber-app-access.log combined;
    error_log /var/log/nginx/barber-app-error.log warn;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Client settings
    client_max_body_size 100M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Proxy settings
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # Static files caching (disabled for development)
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|eot|svg)$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        try_files $uri $uri/ =404;
    }
    
    # API routes with rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        limit_req_status 429;
        
        try_files $uri $uri/ /index.php?$query_string;
        
        # CORS headers for development
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-API-Key" always;
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-API-Key";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # Authentication routes with stricter rate limiting
    location ~ ^/api/(auth|login|register|password) {
        limit_req zone=auth burst=5 nodelay;
        limit_req_status 429;
        
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # Admin panel
    location /admin {
        try_files $uri $uri/ /index.php?$query_string;
        
        # Additional security for admin
        add_header X-Admin-Panel "true" always;
    }
    
    # Laravel Horizon
    location /horizon {
        try_files $uri $uri/ /index.php?$query_string;
        
        # Basic auth for development (optional)
        # auth_basic "Horizon Development";
        # auth_basic_user_file /etc/nginx/.htpasswd;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Nginx status for monitoring
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        allow **********/12;  # Docker networks
        deny all;
    }
    
    # PHP-FPM status
    location ~ ^/(status|ping)$ {
        access_log off;
        allow 127.0.0.1;
        allow **********/12;  # Docker networks
        deny all;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_pass app_backend;
    }
    
    # Main PHP handler
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app_backend;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        
        # Development specific parameters
        fastcgi_param APP_ENV "local";
        fastcgi_param APP_DEBUG "true";
        
        # Increase timeouts for development
        fastcgi_connect_timeout 300s;
        fastcgi_send_timeout 300s;
        fastcgi_read_timeout 300s;
        
        # Buffer settings
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }
    
    # Main location block
    location / {
        limit_req zone=general burst=50 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~* \.(env|log|htaccess|htpasswd|ini|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Deny access to Laravel specific directories
    location ~ ^/(storage|bootstrap|config|database|resources|routes|tests|vendor)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Allow access to storage/app/public (for file uploads)
    location ~ ^/storage/app/public/ {
        try_files $uri =404;
    }
}

# HTTPS server block (for development with self-signed certificates)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name localhost api.barber-app.local admin.barber-app.local;
    
    # SSL configuration (self-signed for development)
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Development specific SSL settings (less strict)
    ssl_verify_client off;
    
    # Include all the same location blocks as HTTP
    root /var/www/public;
    index index.php index.html index.htm;
    
    # Logging
    access_log /var/log/nginx/barber-app-ssl-access.log combined;
    error_log /var/log/nginx/barber-app-ssl-error.log warn;
    
    # Security headers for HTTPS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Development headers
    add_header X-Environment "development" always;
    add_header X-SSL-Enabled "true" always;
    
    # Include all location blocks from HTTP server
    include /etc/nginx/conf.d/locations.conf;
}

# Redirect HTTP to HTTPS (optional for development)
# server {
#     listen 80;
#     server_name api.barber-app.local admin.barber-app.local;
#     return 301 https://$server_name$request_uri;
# }

# Development tools server
server {
    listen 8080;
    server_name localhost;
    
    # Adminer
    location /adminer {
        proxy_pass http://adminer:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Redis Commander
    location /redis {
        proxy_pass http://redis-commander:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # MailHog
    location /mailhog {
        proxy_pass http://mailhog:8025;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # MinIO Console
    location /minio {
        proxy_pass http://minio:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Load balancer health check
server {
    listen 8090;
    server_name localhost;
    
    location /health {
        access_log off;
        return 200 "nginx healthy\n";
        add_header Content-Type text/plain;
    }
    
    location /ready {
        access_log off;
        return 200 "nginx ready\n";
        add_header Content-Type text/plain;
    }
}
