# 🔧 دليل الصيانة والدعم الفني - حلاق على بابك

## 🎯 نظرة عامة على الصيانة

### 📋 **أهداف الصيانة**
- ضمان استمرارية العمل 99.9%
- الحفاظ على الأداء الأمثل
- منع المشاكل قبل حدوثها
- تحديث النظام بانتظام
- حماية البيانات والأمان

## 🕐 **جدولة الصيانة**

### 📅 **الصيانة اليومية (تلقائية)**
```bash
# النسخ الاحتياطي اليومي
0 2 * * * /scripts/daily_backup.sh

# تنظيف الملفات المؤقتة
0 3 * * * /scripts/cleanup_temp.sh

# تحديث الإحصائيات
0 4 * * * php /var/www/artisan analytics:update

# فحص الأمان
0 5 * * * /scripts/security_scan.sh

# تحديث Cache
0 6 * * * php /var/www/artisan cache:clear
```

### 📅 **الصيانة الأسبوعية (الأحد 2:00 ص)**
```bash
# تحسين قاعدة البيانات
0 2 * * 0 mysql -u root -p$DB_PASSWORD -e "OPTIMIZE TABLE bookings, users, barbers;"

# تنظيف الملفات القديمة
0 2 * * 0 find /var/www/storage/logs -name "*.log" -mtime +30 -delete

# تحديث فهارس البحث
0 3 * * 0 php /var/www/artisan scout:import

# فحص التحديثات الأمنية
0 4 * * 0 /scripts/security_updates.sh

# تقرير الأداء الأسبوعي
0 5 * * 0 php /var/www/artisan reports:weekly
```

### 📅 **الصيانة الشهرية (أول سبت من الشهر)**
```bash
# النسخ الاحتياطي الكامل
0 1 1 * * /scripts/full_backup.sh

# تحليل الأداء الشامل
0 2 1 * * /scripts/performance_analysis.sh

# تحديث شهادات SSL
0 3 1 * * certbot renew --quiet

# مراجعة السجلات الأمنية
0 4 1 * * /scripts/security_audit.sh

# تحسين الخوادم
0 5 1 * * /scripts/server_optimization.sh
```

## 🔍 **مراقبة النظام**

### 📊 **مؤشرات الأداء الرئيسية**
```bash
# مراقبة استخدام الخادم
#!/bin/bash
# monitor_server.sh

# CPU Usage
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "تحذير: استخدام المعالج عالي - $CPU_USAGE%"
    # إرسال تنبيه
    curl -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" \
         -d "chat_id=$CHAT_ID&text=تحذير: استخدام المعالج عالي - $CPU_USAGE%"
fi

# Memory Usage
MEM_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')
if (( $(echo "$MEM_USAGE > 85" | bc -l) )); then
    echo "تحذير: استخدام الذاكرة عالي - $MEM_USAGE%"
fi

# Disk Usage
DISK_USAGE=$(df -h | awk '$NF=="/"{printf "%s", $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 85 ]; then
    echo "تحذير: مساحة القرص منخفضة - $DISK_USAGE%"
fi

# Database Connections
DB_CONNECTIONS=$(mysql -u root -p$DB_PASSWORD -e "SHOW STATUS LIKE 'Threads_connected';" | awk 'NR==2{print $2}')
if [ $DB_CONNECTIONS -gt 100 ]; then
    echo "تحذير: اتصالات قاعدة البيانات عالية - $DB_CONNECTIONS"
fi
```

### 🔔 **نظام التنبيهات**
```php
// app/Services/MonitoringService.php
<?php

namespace App\Services;

class MonitoringService
{
    public function checkSystemHealth()
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'redis' => $this->checkRedis(),
            'storage' => $this->checkStorage(),
            'external_apis' => $this->checkExternalAPIs(),
            'queue' => $this->checkQueue(),
        ];

        foreach ($checks as $service => $status) {
            if (!$status['healthy']) {
                $this->sendAlert($service, $status['message']);
            }
        }

        return $checks;
    }

    private function checkDatabase()
    {
        try {
            DB::connection()->getPdo();
            $responseTime = $this->measureDatabaseResponseTime();
            
            return [
                'healthy' => $responseTime < 1000, // milliseconds
                'message' => "Database response time: {$responseTime}ms",
                'response_time' => $responseTime
            ];
        } catch (\Exception $e) {
            return [
                'healthy' => false,
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
        }
    }

    private function sendAlert($service, $message)
    {
        // Telegram Alert
        Http::post("https://api.telegram.org/bot{$this->botToken}/sendMessage", [
            'chat_id' => $this->chatId,
            'text' => "🚨 تحذير النظام\n\nالخدمة: {$service}\nالرسالة: {$message}\nالوقت: " . now()
        ]);

        // Email Alert
        Mail::to('<EMAIL>')->send(new SystemAlertMail($service, $message));

        // Log Alert
        Log::critical("System Alert: {$service} - {$message}");
    }
}
```

## 🛠️ **إجراءات الصيانة**

### 🔄 **تحديث النظام**
```bash
#!/bin/bash
# update_system.sh

echo "بدء تحديث النظام..."

# 1. النسخ الاحتياطي قبل التحديث
echo "إنشاء نسخة احتياطية..."
/scripts/backup_before_update.sh

# 2. وضع النظام في وضع الصيانة
echo "تفعيل وضع الصيانة..."
php /var/www/artisan down --message="جاري تحديث النظام" --retry=60

# 3. تحديث الكود
echo "تحديث الكود المصدري..."
cd /var/www
git pull origin main

# 4. تحديث المكتبات
echo "تحديث المكتبات..."
composer install --no-dev --optimize-autoloader
npm install --production

# 5. تشغيل الترحيلات
echo "تحديث قاعدة البيانات..."
php artisan migrate --force

# 6. تحديث Cache
echo "تحديث التخزين المؤقت..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 7. إعادة تشغيل الخدمات
echo "إعادة تشغيل الخدمات..."
sudo systemctl restart nginx
sudo systemctl restart php8.1-fpm
sudo supervisorctl restart all

# 8. اختبار النظام
echo "اختبار النظام..."
if curl -f http://localhost/api/health; then
    echo "النظام يعمل بشكل طبيعي"
    # إلغاء وضع الصيانة
    php artisan up
    echo "تم إلغاء وضع الصيانة"
else
    echo "خطأ في النظام - استعادة النسخة الاحتياطية"
    /scripts/restore_backup.sh
    exit 1
fi

echo "تم تحديث النظام بنجاح!"
```

### 🗄️ **إدارة قاعدة البيانات**
```sql
-- تحسين قاعدة البيانات
-- optimize_database.sql

-- تحسين جداول الحجوزات
OPTIMIZE TABLE bookings;
ANALYZE TABLE bookings;

-- تحسين جداول المستخدمين
OPTIMIZE TABLE users;
ANALYZE TABLE users;

-- تحسين جداول الحلاقين
OPTIMIZE TABLE barbers;
ANALYZE TABLE barbers;

-- إعادة بناء الفهارس
ALTER TABLE bookings DROP INDEX idx_barber_date, ADD INDEX idx_barber_date (barber_id, scheduled_at);
ALTER TABLE users DROP INDEX idx_phone, ADD UNIQUE INDEX idx_phone (phone);

-- تنظيف البيانات القديمة
DELETE FROM password_resets WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR);
DELETE FROM failed_jobs WHERE failed_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
DELETE FROM sessions WHERE last_activity < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY));

-- إحصائيات الجداول
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'barber_db'
ORDER BY (data_length + index_length) DESC;
```

## 🔒 **الأمان والحماية**

### 🛡️ **فحص الأمان اليومي**
```bash
#!/bin/bash
# security_scan.sh

echo "بدء فحص الأمان اليومي..."

# 1. فحص محاولات الاختراق
echo "فحص محاولات الاختراق..."
FAILED_LOGINS=$(grep "Failed password" /var/log/auth.log | wc -l)
if [ $FAILED_LOGINS -gt 50 ]; then
    echo "تحذير: محاولات دخول فاشلة كثيرة - $FAILED_LOGINS"
fi

# 2. فحص الملفات المشبوهة
echo "فحص الملفات المشبوهة..."
find /var/www -name "*.php" -type f -exec grep -l "eval\|base64_decode\|shell_exec" {} \; > /tmp/suspicious_files.txt
if [ -s /tmp/suspicious_files.txt ]; then
    echo "تحذير: ملفات مشبوهة موجودة"
    cat /tmp/suspicious_files.txt
fi

# 3. فحص صلاحيات الملفات
echo "فحص صلاحيات الملفات..."
find /var/www -type f -perm 777 > /tmp/world_writable.txt
if [ -s /tmp/world_writable.txt ]; then
    echo "تحذير: ملفات بصلاحيات خطيرة"
    cat /tmp/world_writable.txt
fi

# 4. فحص التحديثات الأمنية
echo "فحص التحديثات الأمنية..."
apt list --upgradable | grep -i security > /tmp/security_updates.txt
if [ -s /tmp/security_updates.txt ]; then
    echo "تحديثات أمنية متاحة:"
    cat /tmp/security_updates.txt
fi

# 5. فحص شهادات SSL
echo "فحص شهادات SSL..."
CERT_EXPIRY=$(openssl x509 -in /etc/letsencrypt/live/api.barber-app.com/cert.pem -noout -dates | grep notAfter | cut -d= -f2)
CERT_EXPIRY_EPOCH=$(date -d "$CERT_EXPIRY" +%s)
CURRENT_EPOCH=$(date +%s)
DAYS_UNTIL_EXPIRY=$(( ($CERT_EXPIRY_EPOCH - $CURRENT_EPOCH) / 86400 ))

if [ $DAYS_UNTIL_EXPIRY -lt 30 ]; then
    echo "تحذير: شهادة SSL تنتهي خلال $DAYS_UNTIL_EXPIRY يوم"
fi

echo "انتهى فحص الأمان"
```

### 🔐 **تحديث كلمات المرور**
```bash
#!/bin/bash
# rotate_passwords.sh

echo "تحديث كلمات المرور الدورية..."

# 1. تحديث كلمة مرور قاعدة البيانات
NEW_DB_PASSWORD=$(openssl rand -base64 32)
mysql -u root -p$OLD_DB_PASSWORD -e "ALTER USER 'barber_user'@'localhost' IDENTIFIED BY '$NEW_DB_PASSWORD';"

# تحديث ملف البيئة
sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$NEW_DB_PASSWORD/" /var/www/.env

# 2. تحديث مفاتيح API
NEW_APP_KEY=$(php /var/www/artisan key:generate --show)
sed -i "s/APP_KEY=.*/APP_KEY=$NEW_APP_KEY/" /var/www/.env

# 3. تحديث مفاتيح JWT
NEW_JWT_SECRET=$(openssl rand -base64 64)
sed -i "s/JWT_SECRET=.*/JWT_SECRET=$NEW_JWT_SECRET/" /var/www/.env

# 4. إعادة تشغيل الخدمات
sudo systemctl restart nginx
sudo systemctl restart php8.1-fpm

echo "تم تحديث كلمات المرور بنجاح"
```

## 📊 **التقارير والتحليلات**

### 📈 **تقرير الأداء اليومي**
```php
// app/Console/Commands/DailyPerformanceReport.php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DailyPerformanceReport extends Command
{
    protected $signature = 'reports:daily-performance';
    protected $description = 'Generate daily performance report';

    public function handle()
    {
        $report = [
            'date' => now()->format('Y-m-d'),
            'system_metrics' => $this->getSystemMetrics(),
            'business_metrics' => $this->getBusinessMetrics(),
            'user_activity' => $this->getUserActivity(),
            'errors' => $this->getErrorSummary(),
        ];

        // إرسال التقرير
        $this->sendReport($report);
        
        $this->info('Daily performance report generated successfully');
    }

    private function getSystemMetrics()
    {
        return [
            'avg_response_time' => $this->getAverageResponseTime(),
            'total_requests' => $this->getTotalRequests(),
            'error_rate' => $this->getErrorRate(),
            'uptime' => $this->getUptime(),
            'database_performance' => $this->getDatabasePerformance(),
        ];
    }

    private function getBusinessMetrics()
    {
        return [
            'new_users' => User::whereDate('created_at', today())->count(),
            'new_bookings' => Booking::whereDate('created_at', today())->count(),
            'completed_bookings' => Booking::whereDate('updated_at', today())
                                          ->where('status', 'completed')->count(),
            'revenue' => Booking::whereDate('updated_at', today())
                               ->where('status', 'completed')
                               ->sum('total_amount'),
            'active_barbers' => Barber::where('is_available', true)->count(),
        ];
    }
}
```

## 🆘 **إجراءات الطوارئ**

### 🚨 **خطة الاستجابة للحوادث**
```bash
#!/bin/bash
# emergency_response.sh

INCIDENT_TYPE=$1
SEVERITY=$2

case $INCIDENT_TYPE in
    "database_down")
        echo "قاعدة البيانات معطلة - بدء الاستجابة الطارئة"
        
        # 1. تفعيل قاعدة البيانات الاحتياطية
        /scripts/failover_database.sh
        
        # 2. إشعار الفريق
        /scripts/alert_team.sh "Database Down" "Critical"
        
        # 3. تحديث DNS للخادم الاحتياطي
        /scripts/update_dns_failover.sh
        ;;
        
    "server_overload")
        echo "الخادم محمل بشكل زائد - تفعيل الخوادم الإضافية"
        
        # 1. تشغيل خوادم إضافية
        /scripts/scale_up_servers.sh
        
        # 2. توزيع الحمولة
        /scripts/redistribute_load.sh
        
        # 3. تحسين الأداء
        /scripts/emergency_optimization.sh
        ;;
        
    "security_breach")
        echo "اختراق أمني محتمل - تفعيل بروتوكول الأمان"
        
        # 1. عزل النظام
        /scripts/isolate_system.sh
        
        # 2. تحليل الاختراق
        /scripts/analyze_breach.sh
        
        # 3. إشعار السلطات
        /scripts/notify_authorities.sh
        ;;
esac
```

### 🔄 **استعادة النظام**
```bash
#!/bin/bash
# disaster_recovery.sh

echo "بدء إجراءات استعادة النظام..."

# 1. تقييم الضرر
echo "تقييم حالة النظام..."
/scripts/assess_damage.sh

# 2. استعادة من النسخة الاحتياطية
echo "استعادة النسخة الاحتياطية..."
/scripts/restore_latest_backup.sh

# 3. التحقق من سلامة البيانات
echo "التحقق من سلامة البيانات..."
/scripts/verify_data_integrity.sh

# 4. اختبار جميع الخدمات
echo "اختبار الخدمات..."
/scripts/test_all_services.sh

# 5. إعادة تشغيل النظام
echo "إعادة تشغيل النظام..."
/scripts/restart_system.sh

# 6. مراقبة مكثفة
echo "بدء المراقبة المكثفة..."
/scripts/intensive_monitoring.sh

echo "تم استعادة النظام بنجاح"
```

## 📞 **معلومات الاتصال الطارئ**

### 🔧 **فريق الدعم الفني**
- **مدير النظام الرئيسي**: +201234567890
- **مطور Backend**: +201234567891
- **مطور Frontend**: +201234567892
- **مختص قواعد البيانات**: +201234567893
- **مختص الأمان**: +201234567894

### 🏢 **الشركاء التقنيين**
- **AWS Support**: ******-266-4064
- **Firebase Support**: عبر Console
- **Stripe Support**: ******-963-8442
- **Google Cloud**: ******-836-3987

### 📧 **قنوات التواصل**
- **Slack**: #emergency-response
- **Telegram**: @barber_app_alerts
- **Email**: <EMAIL>
- **WhatsApp**: +201234567890

---

## ✅ **خلاصة دليل الصيانة**

هذا الدليل يضمن:
- **استمرارية العمل** 99.9%
- **أمان متقدم** ومراقبة مستمرة
- **أداء محسّن** وسرعة استجابة
- **استجابة سريعة** للطوارئ
- **صيانة وقائية** منتظمة

**🔧 النظام محمي ومُراقب على مدار الساعة!**
