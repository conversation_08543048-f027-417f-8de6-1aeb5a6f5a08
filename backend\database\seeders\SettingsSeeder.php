<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // إعدادات عامة للتطبيق
            [
                'key' => 'app_name',
                'value' => 'حلاق على بابك',
                'type' => 'string',
                'category' => 'general',
                'description' => 'اسم التطبيق',
                'is_public' => true
            ],
            [
                'key' => 'app_name_en',
                'value' => 'Barber at Your Door',
                'type' => 'string',
                'category' => 'general',
                'description' => 'اسم التطبيق بالإنجليزية',
                'is_public' => true
            ],
            [
                'key' => 'app_version',
                'value' => '1.0.0',
                'type' => 'string',
                'category' => 'general',
                'description' => 'إصدار التطبيق',
                'is_public' => true
            ],
            [
                'key' => 'support_phone',
                'value' => '+201234567890',
                'type' => 'string',
                'category' => 'general',
                'description' => 'رقم الدعم الفني',
                'is_public' => true
            ],
            [
                'key' => 'support_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'category' => 'general',
                'description' => 'بريد الدعم الفني',
                'is_public' => true
            ],

            // إعدادات تفعيل/تعطيل المزايا
            [
                'key' => 'registration_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'category' => 'features',
                'description' => 'تفعيل التسجيل الجديد',
                'is_public' => false
            ],
            [
                'key' => 'booking_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'category' => 'features',
                'description' => 'تفعيل حجز الحلاق العادي',
                'is_public' => false
            ],
            [
                'key' => 'vip_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'category' => 'features',
                'description' => 'تفعيل حجز VIP',
                'is_public' => false
            ],
            [
                'key' => 'online_payment_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'category' => 'features',
                'description' => 'تفعيل الدفع الإلكتروني',
                'is_public' => false
            ],
            [
                'key' => 'store_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'category' => 'features',
                'description' => 'تفعيل المتجر',
                'is_public' => false
            ],
            [
                'key' => 'loyalty_points_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'category' => 'features',
                'description' => 'تفعيل نظام الولاء بالنقاط',
                'is_public' => false
            ],

            // إعدادات مالية
            [
                'key' => 'default_commission_rate',
                'value' => '15.00',
                'type' => 'float',
                'category' => 'financial',
                'description' => 'نسبة عمولة الطلب العادي (%)',
                'is_public' => false
            ],
            [
                'key' => 'vip_commission_rate',
                'value' => '10.00',
                'type' => 'float',
                'category' => 'financial',
                'description' => 'نسبة عمولة VIP (%)',
                'is_public' => false
            ],
            [
                'key' => 'min_withdrawal_amount',
                'value' => '100.00',
                'type' => 'float',
                'category' => 'financial',
                'description' => 'الحد الأدنى للسحب',
                'is_public' => false
            ],
            [
                'key' => 'default_currency',
                'value' => 'EGP',
                'type' => 'string',
                'category' => 'financial',
                'description' => 'العملة الافتراضية',
                'is_public' => true
            ],
            [
                'key' => 'currency_symbol',
                'value' => 'ج.م',
                'type' => 'string',
                'category' => 'financial',
                'description' => 'رمز العملة',
                'is_public' => true
            ],

            // إعدادات الحجز
            [
                'key' => 'booking_advance_hours',
                'value' => '2',
                'type' => 'integer',
                'category' => 'booking',
                'description' => 'ساعات الحجز المسبق المطلوبة',
                'is_public' => false
            ],
            [
                'key' => 'booking_cancel_hours',
                'value' => '1',
                'type' => 'integer',
                'category' => 'booking',
                'description' => 'ساعات إلغاء الحجز المسموحة',
                'is_public' => false
            ],
            [
                'key' => 'max_daily_bookings_per_barber',
                'value' => '8',
                'type' => 'integer',
                'category' => 'booking',
                'description' => 'الحد الأقصى للحجوزات اليومية للحلاق',
                'is_public' => false
            ],
            [
                'key' => 'default_service_duration',
                'value' => '30',
                'type' => 'integer',
                'category' => 'booking',
                'description' => 'مدة الخدمة الافتراضية (دقيقة)',
                'is_public' => false
            ],

            // إعدادات VIP
            [
                'key' => 'vip_monthly_price',
                'value' => '299.00',
                'type' => 'float',
                'category' => 'vip',
                'description' => 'سعر اشتراك VIP الشهري',
                'is_public' => false
            ],
            [
                'key' => 'vip_discount_percentage',
                'value' => '20.00',
                'type' => 'float',
                'category' => 'vip',
                'description' => 'نسبة خصم VIP (%)',
                'is_public' => false
            ],
            [
                'key' => 'vip_free_bookings_monthly',
                'value' => '2',
                'type' => 'integer',
                'category' => 'vip',
                'description' => 'عدد الحجوزات المجانية شهرياً لـ VIP',
                'is_public' => false
            ],

            // إعدادات نظام النقاط
            [
                'key' => 'points_per_booking',
                'value' => '10',
                'type' => 'integer',
                'category' => 'loyalty',
                'description' => 'نقاط لكل حجز',
                'is_public' => false
            ],
            [
                'key' => 'points_redemption_rate',
                'value' => '100',
                'type' => 'integer',
                'category' => 'loyalty',
                'description' => 'نقاط = 1 جنيه',
                'is_public' => false
            ],

            // إعدادات بوابات الدفع
            [
                'key' => 'paymob_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'category' => 'payment',
                'description' => 'تفعيل Paymob',
                'is_public' => false
            ],
            [
                'key' => 'cash_payment_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'category' => 'payment',
                'description' => 'تفعيل الدفع كاش',
                'is_public' => false
            ],

            // إعدادات الصيانة
            [
                'key' => 'maintenance_mode',
                'value' => 'false',
                'type' => 'boolean',
                'category' => 'system',
                'description' => 'وضع الصيانة',
                'is_public' => false
            ],
            [
                'key' => 'maintenance_message',
                'value' => 'التطبيق تحت الصيانة، سنعود قريباً',
                'type' => 'string',
                'category' => 'system',
                'description' => 'رسالة الصيانة',
                'is_public' => true
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
