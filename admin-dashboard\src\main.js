import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import vuetify from './plugins/vuetify'
import i18n from './plugins/i18n'
import './styles/main.scss'

// Create Vue app
const app = createApp(App)

// Use plugins
app.use(createPinia())
app.use(router)
app.use(vuetify)
app.use(i18n)

// Global properties
app.config.globalProperties.$filters = {
  currency(value) {
    if (!value) return '0 ج.م'
    return `${parseFloat(value).toLocaleString('ar-EG')} ج.م`
  },
  
  date(value) {
    if (!value) return ''
    return new Date(value).toLocaleDateString('ar-EG')
  },
  
  datetime(value) {
    if (!value) return ''
    return new Date(value).toLocaleString('ar-EG')
  },
  
  truncate(text, length = 50) {
    if (!text) return ''
    if (text.length <= length) return text
    return text.substring(0, length) + '...'
  }
}

// Error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err)
  console.error('Component:', vm)
  console.error('Info:', info)
}

// Mount app
app.mount('#app')

// Remove loading screen
setTimeout(() => {
  document.body.classList.add('app-ready')
}, 1000)
