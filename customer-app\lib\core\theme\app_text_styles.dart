import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppTextStyles {
  // Font Family
  static const String fontFamily = 'Cairo';
  
  // Font Weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  
  // Base Text Style
  static const TextStyle _baseTextStyle = TextStyle(
    fontFamily: fontFamily,
    color: AppColors.textPrimary,
    fontWeight: regular,
  );
  
  // Heading Styles
  static final TextStyle h1 = _baseTextStyle.copyWith(
    fontSize: 32,
    fontWeight: bold,
    height: 1.2,
  );
  
  static final TextStyle h2 = _baseTextStyle.copyWith(
    fontSize: 28,
    fontWeight: bold,
    height: 1.3,
  );
  
  static final TextStyle h3 = _baseTextStyle.copyWith(
    fontSize: 24,
    fontWeight: semiBold,
    height: 1.3,
  );
  
  static final TextStyle h4 = _baseTextStyle.copyWith(
    fontSize: 20,
    fontWeight: semiBold,
    height: 1.4,
  );
  
  static final TextStyle h5 = _baseTextStyle.copyWith(
    fontSize: 18,
    fontWeight: medium,
    height: 1.4,
  );
  
  static final TextStyle h6 = _baseTextStyle.copyWith(
    fontSize: 16,
    fontWeight: medium,
    height: 1.5,
  );
  
  // Body Text Styles
  static final TextStyle bodyLarge = _baseTextStyle.copyWith(
    fontSize: 16,
    fontWeight: regular,
    height: 1.5,
  );
  
  static final TextStyle bodyMedium = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: regular,
    height: 1.5,
  );
  
  static final TextStyle bodySmall = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: regular,
    height: 1.4,
  );
  
  // Label Styles
  static final TextStyle labelLarge = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: medium,
    height: 1.4,
  );
  
  static final TextStyle labelMedium = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: medium,
    height: 1.3,
  );
  
  static final TextStyle labelSmall = _baseTextStyle.copyWith(
    fontSize: 10,
    fontWeight: medium,
    height: 1.3,
  );
  
  // Button Styles
  static final TextStyle buttonLarge = _baseTextStyle.copyWith(
    fontSize: 16,
    fontWeight: semiBold,
    height: 1.2,
  );
  
  static final TextStyle buttonMedium = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: semiBold,
    height: 1.2,
  );
  
  static final TextStyle buttonSmall = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: semiBold,
    height: 1.2,
  );
  
  // Caption Styles
  static final TextStyle caption = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.textSecondary,
    height: 1.3,
  );
  
  static final TextStyle overline = _baseTextStyle.copyWith(
    fontSize: 10,
    fontWeight: medium,
    color: AppColors.textSecondary,
    height: 1.3,
    letterSpacing: 1.5,
  );
  
  // Special Styles
  static final TextStyle price = _baseTextStyle.copyWith(
    fontSize: 18,
    fontWeight: bold,
    color: AppColors.primary,
  );
  
  static final TextStyle priceSmall = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: semiBold,
    color: AppColors.primary,
  );
  
  static final TextStyle discount = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: medium,
    color: AppColors.error,
    decoration: TextDecoration.lineThrough,
  );
  
  static final TextStyle rating = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.rating,
  );
  
  static final TextStyle status = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: medium,
  );
  
  static final TextStyle link = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.primary,
    decoration: TextDecoration.underline,
  );
  
  static final TextStyle error = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.error,
  );
  
  static final TextStyle success = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.success,
  );
  
  static final TextStyle warning = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.warning,
  );
  
  static final TextStyle info = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.info,
  );
  
  // App Bar Styles
  static final TextStyle appBarTitle = _baseTextStyle.copyWith(
    fontSize: 20,
    fontWeight: semiBold,
    color: AppColors.textPrimary,
  );
  
  static final TextStyle appBarSubtitle = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: regular,
    color: AppColors.textSecondary,
  );
  
  // Tab Styles
  static final TextStyle tabActive = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: semiBold,
    color: AppColors.primary,
  );
  
  static final TextStyle tabInactive = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: regular,
    color: AppColors.textSecondary,
  );
  
  // Card Styles
  static final TextStyle cardTitle = _baseTextStyle.copyWith(
    fontSize: 16,
    fontWeight: semiBold,
    color: AppColors.textPrimary,
  );
  
  static final TextStyle cardSubtitle = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: regular,
    color: AppColors.textSecondary,
  );
  
  // List Styles
  static final TextStyle listTitle = _baseTextStyle.copyWith(
    fontSize: 16,
    fontWeight: medium,
    color: AppColors.textPrimary,
  );
  
  static final TextStyle listSubtitle = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: regular,
    color: AppColors.textSecondary,
  );
  
  static final TextStyle listTrailing = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: medium,
    color: AppColors.textLight,
  );
  
  // Form Styles
  static final TextStyle inputLabel = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.textPrimary,
  );
  
  static final TextStyle inputText = _baseTextStyle.copyWith(
    fontSize: 16,
    fontWeight: regular,
    color: AppColors.textPrimary,
  );
  
  static final TextStyle inputHint = _baseTextStyle.copyWith(
    fontSize: 16,
    fontWeight: regular,
    color: AppColors.textLight,
  );
  
  static final TextStyle inputError = _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.error,
  );
  
  // Dialog Styles
  static final TextStyle dialogTitle = _baseTextStyle.copyWith(
    fontSize: 20,
    fontWeight: semiBold,
    color: AppColors.textPrimary,
  );
  
  static final TextStyle dialogContent = _baseTextStyle.copyWith(
    fontSize: 16,
    fontWeight: regular,
    color: AppColors.textSecondary,
    height: 1.5,
  );
  
  // Snackbar Styles
  static final TextStyle snackbarText = _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: regular,
    color: AppColors.white,
  );
  
  // Utility Methods
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }
  
  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size);
  }
  
  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }
  
  static TextStyle withOpacity(TextStyle style, double opacity) {
    return style.copyWith(color: style.color?.withOpacity(opacity));
  }
  
  // Text Theme for Material Design
  static const TextTheme textTheme = TextTheme(
    displayLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 57,
      fontWeight: regular,
      color: AppColors.textPrimary,
    ),
    displayMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 45,
      fontWeight: regular,
      color: AppColors.textPrimary,
    ),
    displaySmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 36,
      fontWeight: regular,
      color: AppColors.textPrimary,
    ),
    headlineLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 32,
      fontWeight: regular,
      color: AppColors.textPrimary,
    ),
    headlineMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 28,
      fontWeight: regular,
      color: AppColors.textPrimary,
    ),
    headlineSmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 24,
      fontWeight: regular,
      color: AppColors.textPrimary,
    ),
    titleLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 22,
      fontWeight: regular,
      color: AppColors.textPrimary,
    ),
    titleMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 16,
      fontWeight: medium,
      color: AppColors.textPrimary,
    ),
    titleSmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 14,
      fontWeight: medium,
      color: AppColors.textPrimary,
    ),
    bodyLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 16,
      fontWeight: regular,
      color: AppColors.textPrimary,
    ),
    bodyMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 14,
      fontWeight: regular,
      color: AppColors.textPrimary,
    ),
    bodySmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 12,
      fontWeight: regular,
      color: AppColors.textSecondary,
    ),
    labelLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 14,
      fontWeight: medium,
      color: AppColors.textPrimary,
    ),
    labelMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 12,
      fontWeight: medium,
      color: AppColors.textPrimary,
    ),
    labelSmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 11,
      fontWeight: medium,
      color: AppColors.textSecondary,
    ),
  );
}
