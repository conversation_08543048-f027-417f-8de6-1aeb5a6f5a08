<template>
  <v-app>
    <v-main>
      <v-container fluid class="fill-height">
        <v-row justify="center" align="center" class="fill-height">
          <v-col cols="12" sm="8" md="6" lg="4" xl="3">
            <v-card class="elevation-8 border-radius-lg">
              <!-- Header -->
              <v-card-title class="text-center pa-6">
                <div class="w-100">
                  <v-avatar size="80" class="mb-4">
                    <v-img src="/logo.png" alt="حلاق على بابك"></v-img>
                  </v-avatar>
                  <h1 class="text-h4 font-weight-bold mb-2">حلاق على بابك</h1>
                  <p class="text-subtitle-1 text-medium-emphasis">لوحة التحكم الإدارية</p>
                </div>
              </v-card-title>

              <!-- Login Form -->
              <v-card-text class="pa-6">
                <v-form ref="loginForm" @submit.prevent="handleLogin">
                  <v-text-field
                    v-model="form.email"
                    label="البريد الإلكتروني"
                    type="email"
                    prepend-inner-icon="mdi-email"
                    variant="outlined"
                    :rules="emailRules"
                    :error-messages="errors.email"
                    class="mb-4"
                    required
                  ></v-text-field>

                  <v-text-field
                    v-model="form.password"
                    :label="'كلمة المرور'"
                    :type="showPassword ? 'text' : 'password'"
                    prepend-inner-icon="mdi-lock"
                    :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                    @click:append-inner="showPassword = !showPassword"
                    variant="outlined"
                    :rules="passwordRules"
                    :error-messages="errors.password"
                    class="mb-4"
                    required
                  ></v-text-field>

                  <v-checkbox
                    v-model="form.remember"
                    label="تذكرني"
                    color="primary"
                    hide-details
                    class="mb-4"
                  ></v-checkbox>

                  <v-btn
                    type="submit"
                    color="primary"
                    size="large"
                    block
                    :loading="authStore.loading"
                    class="mb-4"
                  >
                    تسجيل الدخول
                  </v-btn>

                  <div class="text-center">
                    <v-btn
                      variant="text"
                      color="primary"
                      @click="showForgotPassword = true"
                    >
                      نسيت كلمة المرور؟
                    </v-btn>
                  </div>
                </v-form>
              </v-card-text>

              <!-- Error Alert -->
              <v-alert
                v-if="errorMessage"
                type="error"
                variant="tonal"
                class="ma-4"
                closable
                @click:close="errorMessage = ''"
              >
                {{ errorMessage }}
              </v-alert>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </v-main>

    <!-- Forgot Password Dialog -->
    <v-dialog v-model="showForgotPassword" max-width="400">
      <v-card>
        <v-card-title>استعادة كلمة المرور</v-card-title>
        <v-card-text>
          <v-form ref="forgotForm" @submit.prevent="handleForgotPassword">
            <v-text-field
              v-model="forgotEmail"
              label="البريد الإلكتروني"
              type="email"
              variant="outlined"
              :rules="emailRules"
              required
            ></v-text-field>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            @click="showForgotPassword = false"
          >
            إلغاء
          </v-btn>
          <v-btn
            color="primary"
            @click="handleForgotPassword"
            :loading="forgotLoading"
          >
            إرسال
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-app>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useSnackbar } from '@/composables/useSnackbar'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    const { showSnackbar } = useSnackbar()

    const loginForm = ref(null)
    const forgotForm = ref(null)
    const showPassword = ref(false)
    const showForgotPassword = ref(false)
    const errorMessage = ref('')
    const forgotEmail = ref('')
    const forgotLoading = ref(false)

    const form = reactive({
      email: '',
      password: '',
      remember: false
    })

    const errors = reactive({
      email: [],
      password: []
    })

    const emailRules = [
      v => !!v || 'البريد الإلكتروني مطلوب',
      v => /.+@.+\..+/.test(v) || 'البريد الإلكتروني غير صحيح'
    ]

    const passwordRules = [
      v => !!v || 'كلمة المرور مطلوبة',
      v => v.length >= 6 || 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    ]

    const handleLogin = async () => {
      const { valid } = await loginForm.value.validate()
      if (!valid) return

      // Clear previous errors
      errorMessage.value = ''
      errors.email = []
      errors.password = []

      const result = await authStore.login({
        email: form.email,
        password: form.password,
        remember: form.remember
      })

      if (result.success) {
        showSnackbar('تم تسجيل الدخول بنجاح', 'success')
        router.push('/')
      } else {
        errorMessage.value = result.message
        
        // Handle field-specific errors
        if (result.errors) {
          if (result.errors.email) {
            errors.email = Array.isArray(result.errors.email) 
              ? result.errors.email 
              : [result.errors.email]
          }
          if (result.errors.password) {
            errors.password = Array.isArray(result.errors.password) 
              ? result.errors.password 
              : [result.errors.password]
          }
        }
      }
    }

    const handleForgotPassword = async () => {
      const { valid } = await forgotForm.value.validate()
      if (!valid) return

      forgotLoading.value = true
      try {
        // Call forgot password API
        const response = await fetch('/api/v1/auth/admin/forgot-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ email: forgotEmail.value })
        })

        const data = await response.json()

        if (data.success) {
          showSnackbar('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني', 'success')
          showForgotPassword.value = false
          forgotEmail.value = ''
        } else {
          showSnackbar(data.message || 'حدث خطأ أثناء إرسال رابط الاستعادة', 'error')
        }
      } catch (error) {
        console.error('Forgot password error:', error)
        showSnackbar('حدث خطأ أثناء إرسال رابط الاستعادة', 'error')
      } finally {
        forgotLoading.value = false
      }
    }

    onMounted(() => {
      // Check if already authenticated
      if (authStore.isAuthenticated) {
        router.push('/')
      }
    })

    return {
      loginForm,
      forgotForm,
      showPassword,
      showForgotPassword,
      errorMessage,
      forgotEmail,
      forgotLoading,
      form,
      errors,
      emailRules,
      passwordRules,
      authStore,
      handleLogin,
      handleForgotPassword
    }
  }
}
</script>

<style scoped>
.fill-height {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.v-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
}

.v-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
