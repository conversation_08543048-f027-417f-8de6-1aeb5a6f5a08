<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\LiveStream;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class LiveStreamingService
{
    private $agoraAppId;
    private $agoraAppCertificate;
    private $agoraApiUrl;

    public function __construct()
    {
        $this->agoraAppId = config('services.agora.app_id');
        $this->agoraAppCertificate = config('services.agora.app_certificate');
        $this->agoraApiUrl = config('services.agora.api_url', 'https://api.agora.io');
    }

    /**
     * Start live streaming for a booking
     */
    public function startLiveStream(Booking $booking, string $initiatedBy = 'barber')
    {
        try {
            // Generate unique channel name
            $channelName = $this->generateChannelName($booking);
            
            // Create stream record
            $stream = LiveStream::create([
                'booking_id' => $booking->id,
                'channel_name' => $channelName,
                'initiated_by' => $initiatedBy,
                'status' => 'active',
                'started_at' => now(),
                'settings' => [
                    'video_enabled' => true,
                    'audio_enabled' => true,
                    'recording_enabled' => true,
                    'max_duration' => 3600, // 1 hour
                ]
            ]);

            // Generate tokens for participants
            $barberToken = $this->generateToken($channelName, $booking->barber->user_id, 'publisher');
            $customerToken = $this->generateToken($channelName, $booking->customer_id, 'subscriber');

            // Start cloud recording
            $recordingId = $this->startCloudRecording($channelName, $booking);

            $stream->update([
                'barber_token' => $barberToken,
                'customer_token' => $customerToken,
                'recording_id' => $recordingId
            ]);

            // Send notifications
            $this->sendStreamNotifications($booking, $stream);

            return [
                'success' => true,
                'stream' => $stream,
                'tokens' => [
                    'barber' => $barberToken,
                    'customer' => $customerToken
                ],
                'channel_name' => $channelName
            ];

        } catch (\Exception $e) {
            Log::error('Failed to start live stream', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Stop live streaming
     */
    public function stopLiveStream(LiveStream $stream)
    {
        try {
            // Stop cloud recording
            if ($stream->recording_id) {
                $this->stopCloudRecording($stream->recording_id, $stream->channel_name);
            }

            // Update stream status
            $stream->update([
                'status' => 'ended',
                'ended_at' => now(),
                'duration' => now()->diffInSeconds($stream->started_at)
            ]);

            // Process recording
            $this->processRecording($stream);

            return [
                'success' => true,
                'duration' => $stream->duration,
                'recording_url' => $stream->recording_url
            ];

        } catch (\Exception $e) {
            Log::error('Failed to stop live stream', [
                'stream_id' => $stream->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate Agora token for user
     */
    public function generateToken(string $channelName, int $userId, string $role = 'subscriber')
    {
        $uid = $userId;
        $expireTime = now()->addHours(24)->timestamp;
        
        // Role mapping
        $roleMap = [
            'publisher' => 1,   // Can publish and subscribe
            'subscriber' => 2   // Can only subscribe
        ];
        
        $roleType = $roleMap[$role] ?? 2;

        // Generate token using Agora algorithm
        return $this->buildToken($channelName, $uid, $roleType, $expireTime);
    }

    /**
     * Start cloud recording
     */
    private function startCloudRecording(string $channelName, Booking $booking)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Basic ' . base64_encode($this->agoraAppId . ':' . $this->agoraAppCertificate),
                'Content-Type' => 'application/json'
            ])->post($this->agoraApiUrl . '/v1/apps/' . $this->agoraAppId . '/cloud_recording/resourceid', [
                'cname' => $channelName,
                'uid' => '0', // String user ID for recording
                'clientRequest' => [
                    'resourceExpiredHour' => 24,
                    'scene' => 0 // Real-time recording
                ]
            ]);

            if ($response->successful()) {
                $resourceId = $response->json('resourceId');
                
                // Start recording
                $recordResponse = Http::withHeaders([
                    'Authorization' => 'Basic ' . base64_encode($this->agoraAppId . ':' . $this->agoraAppCertificate),
                    'Content-Type' => 'application/json'
                ])->post($this->agoraApiUrl . '/v1/apps/' . $this->agoraAppId . '/cloud_recording/resourceid/' . $resourceId . '/mode/mix/start', [
                    'cname' => $channelName,
                    'uid' => '0',
                    'clientRequest' => [
                        'recordingConfig' => [
                            'maxIdleTime' => 30,
                            'streamTypes' => 2, // Audio and video
                            'channelType' => 0, // Communication
                            'videoStreamType' => 1, // High stream
                            'subscribeVideoUids' => ['#allstream#'],
                            'subscribeAudioUids' => ['#allstream#']
                        ],
                        'recordingFileConfig' => [
                            'avFileType' => ['hls', 'mp4']
                        ],
                        'storageConfig' => [
                            'vendor' => 1, // AWS S3
                            'region' => config('services.aws.region'),
                            'bucket' => config('services.aws.s3_bucket'),
                            'accessKey' => config('services.aws.access_key_id'),
                            'secretKey' => config('services.aws.secret_access_key'),
                            'fileNamePrefix' => ['recordings', 'booking_' . $booking->id]
                        ]
                    ]
                ]);

                if ($recordResponse->successful()) {
                    return $recordResponse->json('sid');
                }
            }

            throw new \Exception('Failed to start cloud recording');

        } catch (\Exception $e) {
            Log::error('Cloud recording start failed', [
                'channel' => $channelName,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Stop cloud recording
     */
    private function stopCloudRecording(string $recordingId, string $channelName)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Basic ' . base64_encode($this->agoraAppId . ':' . $this->agoraAppCertificate),
                'Content-Type' => 'application/json'
            ])->post($this->agoraApiUrl . '/v1/apps/' . $this->agoraAppId . '/cloud_recording/resourceid/' . $recordingId . '/sid/' . $recordingId . '/mode/mix/stop', [
                'cname' => $channelName,
                'uid' => '0',
                'clientRequest' => []
            ]);

            return $response->successful();

        } catch (\Exception $e) {
            Log::error('Cloud recording stop failed', [
                'recording_id' => $recordingId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Process recording after stream ends
     */
    private function processRecording(LiveStream $stream)
    {
        // This would typically involve:
        // 1. Waiting for recording to be available
        // 2. Generating thumbnails
        // 3. Creating different quality versions
        // 4. Updating database with recording URLs
        
        // For now, we'll simulate this process
        $recordingUrl = $this->generateRecordingUrl($stream);
        
        $stream->update([
            'recording_url' => $recordingUrl,
            'recording_processed' => true
        ]);
    }

    /**
     * Generate recording URL
     */
    private function generateRecordingUrl(LiveStream $stream)
    {
        $baseUrl = config('services.aws.cloudfront_url');
        $fileName = 'recordings/booking_' . $stream->booking_id . '_' . $stream->id . '.mp4';
        
        return $baseUrl . '/' . $fileName;
    }

    /**
     * Send stream notifications
     */
    private function sendStreamNotifications(Booking $booking, LiveStream $stream)
    {
        $notificationService = app(NotificationService::class);
        
        // Notify customer
        $notificationService->sendPushNotification(
            $booking->customer_id,
            'بث مباشر متاح',
            'يمكنك الآن مشاهدة خدمتك مباشرة',
            [
                'type' => 'live_stream_started',
                'booking_id' => $booking->id,
                'stream_id' => $stream->id,
                'channel_name' => $stream->channel_name
            ]
        );

        // Notify barber (if customer initiated)
        if ($stream->initiated_by === 'customer') {
            $notificationService->sendPushNotification(
                $booking->barber->user_id,
                'طلب بث مباشر',
                'العميل يريد مشاهدة الخدمة مباشرة',
                [
                    'type' => 'live_stream_request',
                    'booking_id' => $booking->id,
                    'stream_id' => $stream->id
                ]
            );
        }
    }

    /**
     * Generate unique channel name
     */
    private function generateChannelName(Booking $booking)
    {
        return 'booking_' . $booking->id . '_' . Str::random(8);
    }

    /**
     * Build Agora token (simplified version)
     */
    private function buildToken(string $channelName, int $uid, int $role, int $expireTime)
    {
        // This is a simplified version. In production, use the official Agora SDK
        $message = $this->agoraAppId . $channelName . $uid . $expireTime . $role;
        $signature = hash_hmac('sha256', $message, $this->agoraAppCertificate);
        
        return base64_encode(json_encode([
            'app_id' => $this->agoraAppId,
            'channel' => $channelName,
            'uid' => $uid,
            'role' => $role,
            'expire' => $expireTime,
            'signature' => $signature
        ]));
    }

    /**
     * Get stream statistics
     */
    public function getStreamStatistics(LiveStream $stream)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Basic ' . base64_encode($this->agoraAppId . ':' . $this->agoraAppCertificate)
            ])->get($this->agoraApiUrl . '/dev/v1/channel/user/' . $this->agoraAppId . '/' . $stream->channel_name);

            if ($response->successful()) {
                return $response->json();
            }

            return null;

        } catch (\Exception $e) {
            Log::error('Failed to get stream statistics', [
                'stream_id' => $stream->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Handle stream quality adjustment
     */
    public function adjustStreamQuality(LiveStream $stream, string $quality)
    {
        $qualitySettings = [
            'low' => ['width' => 320, 'height' => 240, 'bitrate' => 200],
            'medium' => ['width' => 640, 'height' => 480, 'bitrate' => 500],
            'high' => ['width' => 1280, 'height' => 720, 'bitrate' => 1000],
            'ultra' => ['width' => 1920, 'height' => 1080, 'bitrate' => 2000]
        ];

        if (!isset($qualitySettings[$quality])) {
            return false;
        }

        $settings = $stream->settings;
        $settings['video_quality'] = $qualitySettings[$quality];
        
        $stream->update(['settings' => $settings]);

        return true;
    }

    /**
     * Enable/disable recording during stream
     */
    public function toggleRecording(LiveStream $stream, bool $enable)
    {
        if ($enable && !$stream->recording_id) {
            $recordingId = $this->startCloudRecording($stream->channel_name, $stream->booking);
            $stream->update(['recording_id' => $recordingId]);
        } elseif (!$enable && $stream->recording_id) {
            $this->stopCloudRecording($stream->recording_id, $stream->channel_name);
            $stream->update(['recording_id' => null]);
        }

        $settings = $stream->settings;
        $settings['recording_enabled'] = $enable;
        $stream->update(['settings' => $settings]);

        return true;
    }

    /**
     * Get active streams
     */
    public function getActiveStreams()
    {
        return LiveStream::where('status', 'active')
            ->with(['booking.customer', 'booking.barber.user'])
            ->get();
    }

    /**
     * Get stream history for booking
     */
    public function getStreamHistory(Booking $booking)
    {
        return LiveStream::where('booking_id', $booking->id)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Validate stream permissions
     */
    public function validateStreamPermissions(LiveStream $stream, int $userId)
    {
        $booking = $stream->booking;
        
        return $userId === $booking->customer_id || 
               $userId === $booking->barber->user_id;
    }

    /**
     * Handle stream events (webhooks from Agora)
     */
    public function handleStreamEvent(array $eventData)
    {
        $eventType = $eventData['eventType'] ?? null;
        $channelName = $eventData['channelName'] ?? null;

        if (!$channelName) {
            return false;
        }

        $stream = LiveStream::where('channel_name', $channelName)->first();
        
        if (!$stream) {
            return false;
        }

        switch ($eventType) {
            case 'user_joined':
                $this->handleUserJoined($stream, $eventData);
                break;
            case 'user_left':
                $this->handleUserLeft($stream, $eventData);
                break;
            case 'recording_started':
                $this->handleRecordingStarted($stream, $eventData);
                break;
            case 'recording_stopped':
                $this->handleRecordingStopped($stream, $eventData);
                break;
        }

        return true;
    }

    /**
     * Handle user joined event
     */
    private function handleUserJoined(LiveStream $stream, array $eventData)
    {
        $participants = $stream->participants ?? [];
        $participants[] = [
            'uid' => $eventData['uid'],
            'joined_at' => now()->toISOString()
        ];
        
        $stream->update(['participants' => $participants]);
    }

    /**
     * Handle user left event
     */
    private function handleUserLeft(LiveStream $stream, array $eventData)
    {
        $participants = $stream->participants ?? [];
        
        foreach ($participants as &$participant) {
            if ($participant['uid'] == $eventData['uid']) {
                $participant['left_at'] = now()->toISOString();
                break;
            }
        }
        
        $stream->update(['participants' => $participants]);
    }

    /**
     * Handle recording started event
     */
    private function handleRecordingStarted(LiveStream $stream, array $eventData)
    {
        $stream->update([
            'recording_started_at' => now(),
            'recording_id' => $eventData['sid'] ?? $stream->recording_id
        ]);
    }

    /**
     * Handle recording stopped event
     */
    private function handleRecordingStopped(LiveStream $stream, array $eventData)
    {
        $stream->update([
            'recording_stopped_at' => now(),
            'recording_url' => $eventData['fileList'][0]['fileName'] ?? null
        ]);
    }
}
