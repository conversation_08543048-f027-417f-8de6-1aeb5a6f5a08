-- الإعدادات الديناميكية الافتراضية لمشروع "حلاق على بابك"

-- إعدادات عامة للتطبيق
INSERT INTO settings (key, value, type, category, description, is_public) VALUES
('app_name', 'حلاق على بابك', 'string', 'general', 'اسم التطبيق', true),
('app_name_en', 'Barber at Your Door', 'string', 'general', 'اسم التطبيق بالإنجليزية', true),
('app_version', '1.0.0', 'string', 'general', 'إصدار التطبيق', true),
('app_logo', '/images/logo.png', 'string', 'general', 'شعار التطبيق', true),
('support_phone', '+201234567890', 'string', 'general', 'رقم الدعم الفني', true),
('support_email', '<EMAIL>', 'string', 'general', 'بريد الدعم الفني', true),

-- إعد<PERSON>ات تفعيل/تعطيل المزايا
('registration_enabled', 'true', 'boolean', 'features', 'تفعيل التسجيل الجديد', false),
('booking_enabled', 'true', 'boolean', 'features', 'تفعيل حجز الحلاق العادي', false),
('vip_enabled', 'true', 'boolean', 'features', 'تفعيل حجز VIP', false),
('online_payment_enabled', 'true', 'boolean', 'features', 'تفعيل الدفع الإلكتروني', false),
('group_booking_enabled', 'true', 'boolean', 'features', 'تفعيل الطلب الجماعي', false),
('rating_system_enabled', 'true', 'boolean', 'features', 'تفعيل نظام التقييم', false),
('store_enabled', 'true', 'boolean', 'features', 'تفعيل المتجر', false),
('loyalty_points_enabled', 'true', 'boolean', 'features', 'تفعيل نظام الولاء بالنقاط', false),
('van_vip_enabled', 'true', 'boolean', 'features', 'تفعيل صالونات متنقلة VIP', false),
('monthly_vip_enabled', 'true', 'boolean', 'features', 'تفعيل الاشتراك الشهري VIP', false),
('live_streaming_enabled', 'false', 'boolean', 'features', 'تفعيل بث الفيديو المباشر', false),

-- إعدادات مالية
('default_commission_rate', '15.00', 'float', 'financial', 'نسبة عمولة الطلب العادي (%)', false),
('vip_commission_rate', '10.00', 'float', 'financial', 'نسبة عمولة VIP (%)', false),
('min_withdrawal_amount', '100.00', 'float', 'financial', 'الحد الأدنى للسحب', false),
('withdrawal_fee', '5.00', 'float', 'financial', 'رسوم عملية السحب', false),
('default_currency', 'EGP', 'string', 'financial', 'العملة الافتراضية', true),
('currency_symbol', 'ج.م', 'string', 'financial', 'رمز العملة', true),

-- إعدادات الحجز
('booking_advance_hours', '2', 'integer', 'booking', 'ساعات الحجز المسبق المطلوبة', false),
('booking_cancel_hours', '1', 'integer', 'booking', 'ساعات إلغاء الحجز المسموحة', false),
('max_daily_bookings_per_barber', '8', 'integer', 'booking', 'الحد الأقصى للحجوزات اليومية للحلاق', false),
('default_service_duration', '30', 'integer', 'booking', 'مدة الخدمة الافتراضية (دقيقة)', false),
('auto_assign_barber', 'true', 'boolean', 'booking', 'تعيين حلاق تلقائياً', false),

-- إعدادات VIP
('vip_monthly_price', '299.00', 'float', 'vip', 'سعر اشتراك VIP الشهري', false),
('vip_yearly_price', '2999.00', 'float', 'vip', 'سعر اشتراك VIP السنوي', false),
('vip_discount_percentage', '20.00', 'float', 'vip', 'نسبة خصم VIP (%)', false),
('vip_free_bookings_monthly', '2', 'integer', 'vip', 'عدد الحجوزات المجانية شهرياً لـ VIP', false),
('max_vip_barbers', '50', 'integer', 'vip', 'الحد الأقصى للحلاقين VIP', false),

-- إعدادات نظام النقاط
('points_per_booking', '10', 'integer', 'loyalty', 'نقاط لكل حجز', false),
('points_per_egp', '1', 'integer', 'loyalty', 'نقاط لكل جنيه مصري', false),
('points_redemption_rate', '100', 'integer', 'loyalty', 'نقاط = 1 جنيه', false),
('min_points_redemption', '500', 'integer', 'loyalty', 'الحد الأدنى لاستبدال النقاط', false),

-- إعدادات الإشعارات
('push_notifications_enabled', 'true', 'boolean', 'notifications', 'تفعيل الإشعارات', false),
('sms_notifications_enabled', 'true', 'boolean', 'notifications', 'تفعيل إشعارات SMS', false),
('email_notifications_enabled', 'true', 'boolean', 'notifications', 'تفعيل إشعارات البريد', false),
('booking_reminder_hours', '2', 'integer', 'notifications', 'ساعات تذكير الحجز', false),

-- إعدادات بوابات الدفع
('paymob_enabled', 'true', 'boolean', 'payment', 'تفعيل Paymob', false),
('stripe_enabled', 'false', 'boolean', 'payment', 'تفعيل Stripe', false),
('cash_payment_enabled', 'true', 'boolean', 'payment', 'تفعيل الدفع كاش', false),
('wallet_payment_enabled', 'true', 'boolean', 'payment', 'تفعيل الدفع من المحفظة', false),

-- إعدادات الخرائط والمواقع
('google_maps_api_key', '', 'string', 'maps', 'مفتاح Google Maps API', false),
('default_city_id', '1', 'integer', 'maps', 'المدينة الافتراضية', false),
('max_service_radius_km', '50', 'integer', 'maps', 'أقصى نطاق خدمة (كم)', false),

-- إعدادات المتجر
('store_commission_rate', '10.00', 'float', 'store', 'عمولة المتجر (%)', false),
('free_delivery_threshold', '200.00', 'float', 'store', 'الحد الأدنى للتوصيل المجاني', false),
('delivery_fee', '25.00', 'float', 'store', 'رسوم التوصيل', false),

-- إعدادات الصيانة
('maintenance_mode', 'false', 'boolean', 'system', 'وضع الصيانة', false),
('maintenance_message', 'التطبيق تحت الصيانة، سنعود قريباً', 'string', 'system', 'رسالة الصيانة', true),

-- إعدادات التقييمات
('min_rating_to_show', '3.0', 'float', 'rating', 'أقل تقييم لإظهار الحلاق', false),
('rating_required_for_completion', 'true', 'boolean', 'rating', 'التقييم مطلوب لإتمام الحجز', false),

-- إعدادات الذكاء الاصطناعي
('ai_barber_recommendation', 'false', 'boolean', 'ai', 'ترشيح الحلاق بالذكاء الاصطناعي', false),
('ai_style_preview', 'false', 'boolean', 'ai', 'معاينة الستايل بالذكاء الاصطناعي', false),

-- إعدادات التسويق
('referral_bonus_points', '100', 'integer', 'marketing', 'نقاط مكافأة الإحالة', false),
('first_booking_discount', '20.00', 'float', 'marketing', 'خصم أول حجز (%)', false),
('birthday_discount', '15.00', 'float', 'marketing', 'خصم عيد الميلاد (%)', false);

-- إدراج الأدوار الافتراضية
INSERT INTO roles (name, display_name, description, is_active) VALUES
('super_admin', 'مدير النظام', 'صلاحيات كاملة على النظام', true),
('admin', 'مدير', 'إدارة عامة للنظام', true),
('verification_officer', 'موظف توثيق', 'مراجعة وتوثيق الحلاقين', true),
('support_agent', 'دعم فني', 'التعامل مع الشكاوى والدعم', true),
('financial_manager', 'مدير مالي', 'إدارة المحفظة والمعاملات المالية', true),
('content_manager', 'مشرف محتوى', 'إدارة الإعلانات والمتجر', true);

-- إدراج الصلاحيات الافتراضية
INSERT INTO permissions (name, display_name, description, module) VALUES
-- صلاحيات المستخدمين
('users.view', 'عرض المستخدمين', 'عرض قائمة المستخدمين', 'users'),
('users.create', 'إضافة مستخدم', 'إضافة مستخدم جديد', 'users'),
('users.edit', 'تعديل المستخدمين', 'تعديل بيانات المستخدمين', 'users'),
('users.delete', 'حذف المستخدمين', 'حذف المستخدمين', 'users'),
('users.ban', 'حظر المستخدمين', 'حظر/إلغاء حظر المستخدمين', 'users'),

-- صلاحيات الحلاقين
('barbers.view', 'عرض الحلاقين', 'عرض قائمة الحلاقين', 'barbers'),
('barbers.verify', 'توثيق الحلاقين', 'توثيق وقبول الحلاقين', 'barbers'),
('barbers.edit', 'تعديل الحلاقين', 'تعديل بيانات الحلاقين', 'barbers'),
('barbers.ban', 'حظر الحلاقين', 'حظر/إلغاء حظر الحلاقين', 'barbers'),

-- صلاحيات الحجوزات
('bookings.view', 'عرض الحجوزات', 'عرض جميع الحجوزات', 'bookings'),
('bookings.manage', 'إدارة الحجوزات', 'تعديل وإلغاء الحجوزات', 'bookings'),

-- صلاحيات المالية
('finance.view', 'عرض التقارير المالية', 'عرض التقارير والمعاملات المالية', 'finance'),
('finance.withdraw', 'معالجة السحب', 'معالجة طلبات السحب', 'finance'),
('finance.refund', 'استرداد الأموال', 'معالجة طلبات الاسترداد', 'finance'),

-- صلاحيات الإعدادات
('settings.view', 'عرض الإعدادات', 'عرض إعدادات النظام', 'settings'),
('settings.edit', 'تعديل الإعدادات', 'تعديل إعدادات النظام', 'settings'),

-- صلاحيات المتجر
('store.view', 'عرض المتجر', 'عرض منتجات المتجر', 'store'),
('store.manage', 'إدارة المتجر', 'إضافة وتعديل المنتجات', 'store'),

-- صلاحيات الدعم
('support.view', 'عرض الشكاوى', 'عرض شكاوى المستخدمين', 'support'),
('support.respond', 'الرد على الشكاوى', 'الرد على شكاوى المستخدمين', 'support');

-- ربط الأدوار بالصلاحيات
-- مدير النظام - جميع الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- مدير - معظم الصلاحيات عدا حذف المستخدمين وتعديل الإعدادات الحساسة
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions WHERE name NOT IN ('users.delete', 'settings.edit');

-- موظف توثيق - صلاحيات الحلاقين فقط
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions WHERE module = 'barbers';

-- دعم فني - صلاحيات الدعم والعرض
INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions WHERE module IN ('support', 'users', 'bookings') AND name LIKE '%.view' OR name LIKE 'support.%';

-- مدير مالي - صلاحيات المالية
INSERT INTO role_permissions (role_id, permission_id)
SELECT 5, id FROM permissions WHERE module = 'finance' OR (module IN ('users', 'barbers', 'bookings') AND name LIKE '%.view');

-- مشرف محتوى - صلاحيات المتجر والعرض
INSERT INTO role_permissions (role_id, permission_id)
SELECT 6, id FROM permissions WHERE module = 'store' OR (module IN ('users', 'bookings') AND name LIKE '%.view');
