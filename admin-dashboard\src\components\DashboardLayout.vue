<template>
  <v-app>
    <!-- Navigation Drawer -->
    <v-navigation-drawer
      v-model="drawer"
      :rail="rail"
      permanent
      @click="rail = false"
      class="dashboard-drawer"
    >
      <!-- Logo Section -->
      <v-list-item
        prepend-avatar="/logo.png"
        :title="!rail ? 'حلاق على بابك' : ''"
        subtitle="لوحة التحكم"
        class="logo-section"
      />

      <v-divider />

      <!-- Navigation Menu -->
      <v-list density="compact" nav>
        <v-list-item
          v-for="item in navigationItems"
          :key="item.title"
          :prepend-icon="item.icon"
          :title="item.title"
          :value="item.value"
          :to="item.route"
          :active="$route.path === item.route"
          class="nav-item"
        >
          <v-badge
            v-if="item.badge"
            :content="item.badge"
            color="error"
            inline
          />
        </v-list-item>
      </v-list>

      <!-- Advanced Features Section -->
      <v-divider class="my-4" />
      
      <v-list-subheader v-if="!rail">
        الميزات المتقدمة
      </v-list-subheader>

      <v-list density="compact" nav>
        <v-list-item
          v-for="item in advancedFeatures"
          :key="item.title"
          :prepend-icon="item.icon"
          :title="item.title"
          :value="item.value"
          :to="item.route"
          :active="$route.path === item.route"
          class="nav-item advanced-feature"
        >
          <v-chip
            v-if="item.isNew"
            size="x-small"
            color="success"
            variant="flat"
            class="ml-2"
          >
            جديد
          </v-chip>
        </v-list-item>
      </v-list>

      <!-- User Section -->
      <template v-slot:append>
        <div class="pa-2">
          <v-btn
            block
            @click="toggleRail"
            :icon="rail ? 'mdi-chevron-right' : 'mdi-chevron-left'"
            variant="text"
          />
        </div>
      </template>
    </v-navigation-drawer>

    <!-- App Bar -->
    <v-app-bar
      :order="-1"
      color="primary"
      dark
      elevation="1"
      class="dashboard-appbar"
    >
      <v-app-bar-nav-icon
        @click="drawer = !drawer"
        v-if="$vuetify.display.mobile"
      />

      <v-toolbar-title class="app-title">
        {{ currentPageTitle }}
      </v-toolbar-title>

      <v-spacer />

      <!-- Real-time Status Indicators -->
      <v-chip
        v-for="status in systemStatus"
        :key="status.name"
        :color="status.color"
        size="small"
        variant="flat"
        class="mx-1"
      >
        <v-icon start :icon="status.icon" />
        {{ status.name }}
      </v-chip>

      <!-- Notifications -->
      <v-btn
        icon
        @click="showNotifications = !showNotifications"
        class="mx-2"
      >
        <v-badge
          :content="unreadNotifications"
          :model-value="unreadNotifications > 0"
          color="error"
        >
          <v-icon>mdi-bell</v-icon>
        </v-badge>
      </v-btn>

      <!-- Language Selector -->
      <v-menu>
        <template v-slot:activator="{ props }">
          <v-btn
            icon
            v-bind="props"
            class="mx-2"
          >
            <v-icon>mdi-translate</v-icon>
          </v-btn>
        </template>
        <v-list>
          <v-list-item
            v-for="lang in languages"
            :key="lang.code"
            @click="changeLanguage(lang.code)"
          >
            <v-list-item-title>{{ lang.name }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>

      <!-- Theme Toggle -->
      <v-btn
        icon
        @click="toggleTheme"
        class="mx-2"
      >
        <v-icon>{{ isDark ? 'mdi-weather-sunny' : 'mdi-weather-night' }}</v-icon>
      </v-btn>

      <!-- User Menu -->
      <v-menu>
        <template v-slot:activator="{ props }">
          <v-btn
            icon
            v-bind="props"
            class="mx-2"
          >
            <v-avatar size="32">
              <v-img
                :src="user.avatar || '/default-avatar.png'"
                :alt="user.name"
              />
            </v-avatar>
          </v-btn>
        </template>
        <v-card min-width="200">
          <v-card-text>
            <div class="text-center">
              <v-avatar size="48" class="mb-2">
                <v-img
                  :src="user.avatar || '/default-avatar.png'"
                  :alt="user.name"
                />
              </v-avatar>
              <div class="text-h6">{{ user.name }}</div>
              <div class="text-caption text-medium-emphasis">{{ user.email }}</div>
            </div>
          </v-card-text>
          <v-divider />
          <v-list>
            <v-list-item @click="goToProfile">
              <v-list-item-title>الملف الشخصي</v-list-item-title>
              <template v-slot:prepend>
                <v-icon>mdi-account</v-icon>
              </template>
            </v-list-item>
            <v-list-item @click="goToSettings">
              <v-list-item-title>الإعدادات</v-list-item-title>
              <template v-slot:prepend>
                <v-icon>mdi-cog</v-icon>
              </template>
            </v-list-item>
            <v-divider />
            <v-list-item @click="logout" class="text-error">
              <v-list-item-title>تسجيل الخروج</v-list-item-title>
              <template v-slot:prepend>
                <v-icon>mdi-logout</v-icon>
              </template>
            </v-list-item>
          </v-list>
        </v-card>
      </v-menu>
    </v-app-bar>

    <!-- Notifications Drawer -->
    <v-navigation-drawer
      v-model="showNotifications"
      location="right"
      temporary
      width="400"
    >
      <v-toolbar color="primary" dark>
        <v-toolbar-title>الإشعارات</v-toolbar-title>
        <v-spacer />
        <v-btn icon @click="markAllAsRead">
          <v-icon>mdi-check-all</v-icon>
        </v-btn>
        <v-btn icon @click="showNotifications = false">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>

      <v-list>
        <v-list-item
          v-for="notification in notifications"
          :key="notification.id"
          :class="{ 'bg-blue-lighten-5': !notification.read }"
          @click="markAsRead(notification.id)"
        >
          <template v-slot:prepend>
            <v-avatar :color="notification.color">
              <v-icon :icon="notification.icon" />
            </v-avatar>
          </template>
          <v-list-item-title>{{ notification.title }}</v-list-item-title>
          <v-list-item-subtitle>{{ notification.message }}</v-list-item-subtitle>
          <v-list-item-subtitle class="text-caption">
            {{ formatTime(notification.created_at) }}
          </v-list-item-subtitle>
        </v-list-item>
      </v-list>
    </v-navigation-drawer>

    <!-- Main Content -->
    <v-main class="dashboard-main">
      <v-container fluid class="pa-4">
        <!-- Breadcrumbs -->
        <v-breadcrumbs
          :items="breadcrumbs"
          class="pa-0 mb-4"
        >
          <template v-slot:divider>
            <v-icon>mdi-chevron-left</v-icon>
          </template>
        </v-breadcrumbs>

        <!-- Page Content -->
        <router-view />
      </v-container>
    </v-main>

    <!-- Loading Overlay -->
    <v-overlay
      v-model="isLoading"
      class="align-center justify-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      />
    </v-overlay>

    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="snackbar.timeout"
      location="top right"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          variant="text"
          @click="snackbar.show = false"
        >
          إغلاق
        </v-btn>
      </template>
    </v-snackbar>
  </v-app>
</template>

<script>
import { mapState, mapActions } from 'pinia'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import { useSettingsStore } from '@/stores/settings'
import realTimeAnalytics from '@/services/realTimeAnalytics'

export default {
  name: 'DashboardLayout',
  
  data() {
    return {
      drawer: true,
      rail: false,
      showNotifications: false,
      isLoading: false,
      
      navigationItems: [
        {
          title: 'لوحة التحكم',
          icon: 'mdi-view-dashboard',
          value: 'dashboard',
          route: '/dashboard'
        },
        {
          title: 'الحجوزات',
          icon: 'mdi-calendar-check',
          value: 'bookings',
          route: '/bookings',
          badge: this.pendingBookings
        },
        {
          title: 'الحلاقين',
          icon: 'mdi-account-group',
          value: 'barbers',
          route: '/barbers'
        },
        {
          title: 'العملاء',
          icon: 'mdi-account-multiple',
          value: 'customers',
          route: '/customers'
        },
        {
          title: 'الخدمات',
          icon: 'mdi-scissors-cutting',
          value: 'services',
          route: '/services'
        },
        {
          title: 'المدفوعات',
          icon: 'mdi-credit-card',
          value: 'payments',
          route: '/payments'
        },
        {
          title: 'التقارير',
          icon: 'mdi-chart-line',
          value: 'reports',
          route: '/reports'
        },
        {
          title: 'الإعدادات',
          icon: 'mdi-cog',
          value: 'settings',
          route: '/settings'
        }
      ],

      advancedFeatures: [
        {
          title: 'الذكاء الاصطناعي',
          icon: 'mdi-brain',
          value: 'ai',
          route: '/ai-dashboard',
          isNew: true
        },
        {
          title: 'الحوسبة الفضائية',
          icon: 'mdi-rocket',
          value: 'space',
          route: '/space-computing',
          isNew: true
        },
        {
          title: 'الواجهة الهولوجرافية',
          icon: 'mdi-cube-outline',
          value: 'holographic',
          route: '/holographic',
          isNew: true
        },
        {
          title: 'التواصل التخاطري',
          icon: 'mdi-head-lightbulb',
          value: 'telepathic',
          route: '/telepathic',
          isNew: true
        },
        {
          title: 'الأمان الكمي',
          icon: 'mdi-shield-lock',
          value: 'quantum',
          route: '/quantum-security',
          isNew: true
        }
      ],

      languages: [
        { code: 'ar', name: 'العربية' },
        { code: 'en', name: 'English' },
        { code: 'fr', name: 'Français' },
        { code: 'tr', name: 'Türkçe' },
        { code: 'de', name: 'Deutsch' }
      ],

      systemStatus: [
        {
          name: 'النظام',
          icon: 'mdi-check-circle',
          color: 'success'
        },
        {
          name: 'الأقمار الصناعية',
          icon: 'mdi-satellite-variant',
          color: 'success'
        },
        {
          name: 'الذكاء الاصطناعي',
          icon: 'mdi-brain',
          color: 'success'
        }
      ],

      notifications: [],
      snackbar: {
        show: false,
        message: '',
        color: 'success',
        timeout: 3000
      }
    }
  },

  computed: {
    ...mapState(useAuthStore, ['user']),
    ...mapState(useAppStore, ['isDark', 'currentPageTitle', 'breadcrumbs']),
    ...mapState(useSettingsStore, ['language']),

    unreadNotifications() {
      return this.notifications.filter(n => !n.read).length
    },

    pendingBookings() {
      // This would come from your booking store
      return 5
    }
  },

  async mounted() {
    await this.initializeDashboard()
    this.setupRealTimeUpdates()
  },

  methods: {
    ...mapActions(useAuthStore, ['logout']),
    ...mapActions(useAppStore, ['toggleTheme', 'setPageTitle']),
    ...mapActions(useSettingsStore, ['setLanguage']),

    async initializeDashboard() {
      this.isLoading = true
      try {
        // Initialize real-time analytics
        await realTimeAnalytics.initialize()
        
        // Load initial data
        await this.loadNotifications()
        await this.loadSystemStatus()
        
        this.showSnackbar('تم تحميل لوحة التحكم بنجاح', 'success')
      } catch (error) {
        console.error('Dashboard initialization failed:', error)
        this.showSnackbar('فشل في تحميل لوحة التحكم', 'error')
      } finally {
        this.isLoading = false
      }
    },

    setupRealTimeUpdates() {
      // Listen for real-time updates
      realTimeAnalytics.on('notification', (notification) => {
        this.notifications.unshift(notification)
        this.showSnackbar(notification.title, 'info')
      })

      realTimeAnalytics.on('system_status', (status) => {
        this.updateSystemStatus(status)
      })
    },

    async loadNotifications() {
      // Load notifications from API
      try {
        const response = await this.$api.get('/notifications')
        this.notifications = response.data.data
      } catch (error) {
        console.error('Failed to load notifications:', error)
      }
    },

    async loadSystemStatus() {
      // Load system status from API
      try {
        const response = await this.$api.get('/system/status')
        this.updateSystemStatus(response.data.data)
      } catch (error) {
        console.error('Failed to load system status:', error)
      }
    },

    updateSystemStatus(status) {
      this.systemStatus = this.systemStatus.map(item => ({
        ...item,
        color: status[item.name.toLowerCase()] ? 'success' : 'error'
      }))
    },

    toggleRail() {
      this.rail = !this.rail
    },

    changeLanguage(langCode) {
      this.setLanguage(langCode)
      this.$i18n.locale = langCode
    },

    goToProfile() {
      this.$router.push('/profile')
    },

    goToSettings() {
      this.$router.push('/settings')
    },

    markAsRead(notificationId) {
      const notification = this.notifications.find(n => n.id === notificationId)
      if (notification) {
        notification.read = true
      }
    },

    markAllAsRead() {
      this.notifications.forEach(n => n.read = true)
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString('ar-EG')
    },

    showSnackbar(message, color = 'success', timeout = 3000) {
      this.snackbar = {
        show: true,
        message,
        color,
        timeout
      }
    }
  }
}
</script>

<style scoped>
.dashboard-drawer {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

.dashboard-appbar {
  backdrop-filter: blur(10px);
}

.logo-section {
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.nav-item {
  margin: 4px 8px;
  border-radius: 8px;
}

.advanced-feature {
  background: linear-gradient(45deg, rgba(25, 118, 210, 0.1), rgba(156, 39, 176, 0.1));
}

.app-title {
  font-weight: 600;
  font-size: 1.2rem;
}

.dashboard-main {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.v-navigation-drawer--rail .v-list-item {
  padding-inline: 8px;
}
</style>
